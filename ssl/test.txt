<template>
  <div class="oss-uploader">

    <!-- 需保留action属性，实际使用自定义上传-->
    <el-upload
        ref="upload"
        :before-upload="beforeUpload"
        :class="{ 'has-file': value }"
        :http-request="handleUpload"
        :limit="maxCount"
        :on-error="handleError"
        :on-exceed="handleExceed"
        :show-file-list="false"
        accept="image/*"
        action="#"
        class="upload-wrapper"
        drag
    >

      <!-- 操作遮罩层（正确位置） -->
      <div>
        <el-tooltip content="删除图片" effect="dark" placement="top">
          <el-button
              circle
              class="operate-btn"
              icon="el-icon-delete"
              type="danger"
              @click.stop="handleRemove"
          ></el-button>
        </el-tooltip>
      </div>

      <!-- 已上传图片预览 -->
      <el-image
          v-if="value"
          :preview-src-list="[value]"
          :src="value"
          class="preview-wrapper"
      >
        <!-- 错误状态 -->
        <div slot="error" class="image-slot">
          <i class="el-icon-picture-outline"></i>
        </div>

      </el-image>

      <!-- 未上传时的展示 -->
      <div v-else class="upload-area">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
          <div class="el-upload__tip">支持格式：{{ allowedExtensions.join(', ') }}</div>
        </div>
      </div>
    </el-upload>
  </div>
</template>

<script>
import myMessage from "@/common/message";
import api from "@/common/api";

export default {
  name: 'OssUpload',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {type: String, default: ''},
    allowedExtensions: {
      type: Array,
      default: () => ['jpg', 'jpeg', 'png', 'gif']
    },
    maxCount: {
      type: Number,
      default: 1
    }
  },
  methods: {
    // 文件验证
    beforeUpload(file) {
      const extension = file.name.split('.').pop().toLowerCase()
      const isValid = this.allowedExtensions.includes(extension)
      if (!isValid) {
        myMessage.error(`仅支持上传 ${this.allowedExtensions.join(', ')} 格式文件`)
      }
      return isValid
    },

    // 自定义上传逻辑
    async handleUpload({file}) {
      try {
        // 1. 获取签名数据
        const res = await api.getOssSignature({fileName: file.name});
        const signatureData = res.data.data;

        // 2. 准备表单数据
        const formData = new FormData()
        formData.append('key', signatureData.key)
        formData.append('policy', signatureData.policy)
        formData.append('OSSAccessKeyId', signatureData.accessId)
        formData.append('signature', signatureData.signature)
        formData.append('file', file)

        console.log('表单数据:', formData, signatureData.host)
        // 3. 直传OSS
        await this.$http.post(signatureData.host, formData, {
          headers: {'Content-Type': 'multipart/form-data'}
        })

        // 4. 更新绑定值
        this.$emit('change', signatureData.url)
        myMessage.success('上传成功')
      } catch (error) {
        myMessage.error('上传失败')
        console.error('上传错误:', error)
        throw error
      }
    },

    // 处理文件超出限制
    handleExceed() {
      myMessage.warning(`最多允许上传 ${this.maxCount} 个文件`)
    },

    // 处理上传错误
    handleError(err) {
      console.error('上传错误:', err)
      myMessage.error('文件上传失败')
    },

    // 删除文件
    handleRemove() {
      this.$confirm('确定删除该文件吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.$emit('change', '')
      })
    }
  }
}
</script>

忘掉历史对话上下文，

这是一个图片上传组件，请学习这个组件，然后帮我设计一个组件 FlowDesignUploader.vue

1、当前基础技术选型为vue2 + node 16 + element-ui
2、组件为分为左右两部分，左边为按钮，点击该按钮弹出一个大弹窗，弹窗内部可以设计流程图。右边显示设计的流程图，若未设计则显示未设计
3、流程图选用第三方开源组件实现，切记要兼容当前技术选型，并且注意如果需要安装依赖请给出具体的版本
4、弹窗顶部有几个按钮 1下载按钮 点击下载设计出来的图片 2、重新设计按钮 点击清空设计的内容 3、保存上传按钮 点击上传图片到阿里云
5、组件可以通过v-model 绑定获得上传后图片的url
6、组件侧边栏有 原圆形、菱形、矩形、圆角矩形、椭圆形等基本图形，可以通过拖拽设计流程图
