export const m = {

  // 新增
  My_Share: 'my share',
  My_Code: 'my code',
  TuC: 'TuC',
  Course: 'Course',
  Drawing_Board: 'Drawing Board',
  Login_to_view_your_code: 'Login to view your_code',


  // /src/common/api.js
  Query_error_unable_to_find_the_resource_to_request: 'Query error! Unable to find the resource to request!',
  Server_error_please_refresh_again: 'Server error! Please refresh again!',
  Request_timed_out_please_try_again_later: 'Request timed out! Please try again later!',
  Network_error_abnormal_link_with_server_please_try_again_later: 'Network error! Abnormal link with server, Please try again later!',
  Error:'Error',
  Success:'Success',
  No_Access_There_is_no_open_discussion_area_on_the_website: 'No Access: There is no open discussion area on the website!',
  No_Access_There_is_no_open_group_discussion_area_on_the_website: 'No Access: There is no open group discussion area on the website!',
  No_Access_There_is_no_open_contest_comment_area_on_the_website: 'No Access: There is no open contest comment area on the website!',

  //  /components/oj/common/NavBar.vue 导航栏
  NavBar_Home: 'Home',
  NavBar_Problem: 'Problem',
  NavBar_Training: 'Training',
  NavBar_Contest: 'Contest',
  NavBar_Status: 'Status',
  NavBar_Rank: 'Rank',
  NavBar_ACM_Rank: 'ACM Rank',
  NavBar_OI_Rank: 'OI Rank',
  NavBar_Discussion: 'Discussion',
  NavBar_Group: 'Group',
  NavBar_About: 'About',

  NavBar_Introduction: 'Introduction',
  NavBar_Developer: 'Developer',
  NavBar_Login: 'Login',
  NavBar_Register: 'Register',
  NavBar_UserHome: 'Home',
  NavBar_Submissions: 'Submissions',
  NavBar_Setting: 'Setting',
  NavBar_Management: 'Management',
  NavBar_Logout: 'Logout',
  Dialog_Login: 'Login',
  Dialog_Register: 'Register',
  Dialog_Reset_Password: 'Reset Password',
  Click_To_Change_Web_Language: 'Click to change web language',
  NavBar_Back_Home:'Back Home',
  NavBar_Training_Home:'Training Home',
  NavBar_Contest_Home: 'Contest Home',
  NavBar_Contest_Own_Submission: 'My Submission',
  NavBar_Contest_Rank: 'Contest Rank',
  NavBar_Group_Home: 'Group Home',

  //  /components/oj/common/Login.vue 登录弹窗
  Login_Username: 'Username',
  Login_Password: 'Password',
  Login_Btn: 'Login',
  Slide_Verify: 'Please slide right to verify',
  Slide_Verify_Success: 'Success',
  Login_No_Account: 'No account? Register now!',
  Login_Forget_Password: 'Forget Password',
  Username_Check_Required: 'The username is required.',
  Username_Check_Max: 'The longest length of a username is 20.',
  Password_Check_Required: 'The password is required.',
  Password_Check_Between: 'The length of the password is between 6 and 20.',
  Welcome_Back: 'Welcome back~',

  // /components/oj/common/Register.vue 注册弹窗
  Register_Username: 'Please Enter phone',
  Register_Password: 'Please Enter Password',
  Register_Password_Again: 'Please Enter Password Again',
  Register_Email: 'Please Enter Email',
  Register_Email_Captcha: 'Please enter the captcha from the email',
  Register_Btn: 'Register',
  Register_Already_Registed: 'Already registed? Login now!',
  The_username_already_exists: 'The username already exists.',
  The_email_already_exists: 'The email already exists.',
  Password_does_not_match: 'Password does not match.',
  Email_Check_Required: 'The email is required.',
  Email_Check_Format: 'The email format is incorrect.',
  Phone_Check_Required: 'The phone is required.',
  Phone_Check_Format: 'The phone format is incorrect.',
  Password_Again_Check_Required: 'The password again is required.',

  Code_Check_Required: 'The captcha is required.',
  Code_Check_Length: 'The captcha must be six digits.',
  The_system_is_processing: 'Please Waiting... The system is processing...',
  Register_Send_Phone_Msg: 'Verification code has been sent to the designated mailbox! If you haven\'t received the phone for a long time, please check whether your phone is accurate!',
  Register_Send_Email_Msg: 'Verification code has been sent to the designated mailbox! If you haven\'t received the email for a long time, please check whether your email is accurate!',
  Thanks_for_registering: 'Thanks for your registering, you can login now.',

  // /components/oj/common/ResetPassword.vue 重置密码弹窗
  // /views/oj/organization/SetNeWPassword.vue 设置新密码页
  Reset_Password_Email: 'Please Enter Your Email',
  Reset_Password_Captcha: 'Please Enter the captcha',
  Send_Password_Reset_Email: 'Send Password Reset Email',
  Waiting_Can_Resend_Email: 'resend the Reset Email...',
  ResetPwd_Send_Email_Msg: 'Reset password email has been sent to the designated mailbox! If you haven\'t received the email for a long time, please check whether your email is accurate!',
  Remember_Passowrd_To_Login: 'Remember password? To login!',
  Set_New_Password: 'Set New Password',
  Set_New_Password_Msg: 'Please Enter New Password',
  Set_New_Password_Again_Msg: 'Please Enter New Password Again',
  The_username_does_not_exists: 'The username does not exist.',
  The_email_does_not_exists: 'The email does not exist.',
  Your_password_has_been_reset: 'Your password has been reset.',

  // /components/oj/setting/Account.vue 账号信息管理页面
  Old_Password: 'Old Password',
  New_Password: 'new Password',
  Confirm_New_Password: 'Confirm New Password',
  Current_Password: 'Current Password',
  Old_Email: 'Old Email',
  New_Email: 'New Email',
  Get_Captcha: 'Get Captcha',
  Old_Mobile: 'Old Mobile',
  New_Mobile: 'New Mobile',
  Change_Password: 'Change Password',
  Change_Email: 'Change Email',
  Change_Mobile: 'Change Mobile',
  Update_Password: 'Update Password',
  Update_Email: 'Update Email',
  Update_Mobile: 'Update Mobile',
  Captcha: 'Captcha',
  The_new_password_does_not_change: 'The new password doesn\'t change.',
  The_new_email_does_not_change: 'The new email doesn\'t change.',
  The_new_mobile_does_not_change: 'The new mobile doesn\'t change.',
  Mobile_Check_Required: 'The mobile is required.',
  Mobile_Check_Format: 'The mobile format is incorrect.',
  Change_Email_Captcha: 'Please enter the captcha from the email',
  Change_Mobile_Captcha: 'Please enter the captcha from the mobile',
  Change_Send_Email_Msg: 'Verification code has been sent to the designated mailbox! If you haven\'t received the email for a long time, please check whether your email is accurate!',
  Change_Send_Mobile_Msg: 'Verification code has been sent to the designated mobile! If you haven\'t received the message for a long time, please check whether your mobile is accurate!',
  Update_Successfully: 'Update Successfully',
  Update_Failed: 'Update Failed',
  Guess_robot: 'Speed too fast, may be machine operation! Please verify again!',
  The_current_password_cannot_be_empty: 'The current password is required.',
  The_new_password_cannot_be_empty: 'The new password is required.',
  The_new_email_cannot_be_empty: 'The new email is required.',


  // /components/oj/setting/UserInfo.vue
  Avatar_Setting: 'Avatar Setting',
  UserInfo_Setting: 'User Info Setting',
  Upload_avatar_hint: 'Drag and drop the avatar here, or click here.',
  CF_Username: 'Codeforces Username',
  School: 'School',
  Student_Number: 'Student Number',
  Blog: 'Blog',
  Github: 'Github',
  Gender: 'Gender',
  Male: 'Male',
  Female: 'Female',
  Secrecy: 'Secrecy',
  Save: 'Save',
  Upload: 'Upload',
  Your_new_avatar: 'Your new avatar',
  Upload_Avatar_Successfully: 'Upload avatar successfully',
  File_type_not_support: 'File type not support',
  is_incorrect_format_file: ' is an incorrect format file,please choose the file format of .GIF, .JPG, .JPEG, .PNG, .BMP, .Webp',
  Exceed_max_size_limit: 'Exceed max size limit',
  File_Exceed_Tips: ' file size is wrong, you can upload a image up to 2MB in size',
  Cancel_Avater_Tips: 'Are you sure you want to cancel the capture of this image?',

  // /views/oj/organization/UserHome.vue
  Recent_login_time: 'Recently launched：',
  Not_set_yet: 'This guy is lazy. He doesn\'t write anything.',
  UserHome_Solved: 'Solved',
  UserHome_Submissions: 'Submissions',
  UserHome_Score: 'Score',
  UserHome_Rating: 'Rating',
  List_Solved_Problems: 'List of solved problems',
  UserHome_Not_Data: 'The guy is so lazy that has not solved any problem yet.',
  Personal_Profile: 'Persion Profile',
  UserHome_Solved_Problems: 'Solved Problems',
  Thermal_energy_table_submitted_in_the_last_year:'Thermal energy table submitted in the last year',
  Difficulty_Statistics:'Difficulty Statistics',
  Problems:'Problems',
  Calendar_Tooltip_Uint:'submissions',
  Jan: 'Jan',
  Feb: 'Feb',
  Mar: 'Mar',
  Apr: 'Apr',
  May: 'May',
  Jun: 'Jun',
  Jul: 'Jul',
  Aug: 'Aug',
  Sep: 'Sep',
  Oct: 'Oct',
  Nov: 'Nov',
  Dec: 'Dec',
  Sun: 'Sun',
  Mon: 'Mon',
  Tue: 'Tue',
  Wed: 'Wed',
  Thu: 'Thu',
  Fri: 'Fri',
  Sat: 'Sat',
  Less:'Less',
  More:'More',
  on:'on',

  // /views/oj/organization/Setting.vue
  Account_Setting: 'Account Setting',

  // App.vue 底部文案
  Service: 'Service',
  Judging_Queue: 'Judging Queue',
  System_Info: 'System Info',
  Development: 'Development',
  Open_Source: 'Open Source',
  Support: 'Support',
  Help: 'Help',
  Group: 'Group',

  // /views/oj/Home.vue
  Welcome_to: 'Welcome to ',
  Recent_7_Days_AC_Rank: 'Rank: Accepted In The Last Week',
  Other_OJ_Contest: 'Other Online Judge Contest',
  Latest_Problem: 'Latest Problem',
  Supported_Remote_Online_Judge: 'Supported Remote Online Judge',
  Statistics_Submissions_In_The_Last_Week: 'Statistics: Submissions In The Last Week',

  // 表格通用列名,按钮，搜索框等
  Enter_keyword: 'Enter keyword',
  Reset: 'Reset',
  Username: 'Username',
  Solved: 'Solved',
  AC: 'AC',
  OJ: 'OJ',
  Title: 'Title',
  Begin_Time: 'Begin Time',
  End_Time: 'End Time',
  Contest_Time: 'Contest Time',
  Recent_Contest: 'Recent Contest',
  Problem_ID: 'Problem ID',
  Total: 'Total',
  AC_Rate: 'AC Rate',
  Score: 'Score',

  // /views/oj/problem/problemList.vue
  Problem_List: 'Problem List',
  Problem_Bank: 'Problem Bank',
  All: 'All',
  My_OJ: 'Home OJ',
  Level: 'Level',
  Tags: 'Tags',
  Search_Filter_Tag: 'Input tag',
  Pick_a_random_question: 'Pick a random question',
  Touch_Get_Status: 'Please touch or hover the mouse to the designated problem line to view the submission status',
  Good_luck_to_you: 'Good luck to you!',

  // /views/oj/problem/Problem.vue
  Problem_Description: 'Problem Description',
  My_Submission: 'My Submission',
  Problem_Annex: 'Problem Annex',
  Login_to_view_your_submission_history: 'Login to view your submission history',
  Shrink_Sidebar: 'Shrink Sidebar',
  View_Problem_Content: 'View Problem Content',
  Only_View_Problem: 'Only View Problem',
  Put_away_the_full_screen_and_write_the_code: 'Put away the full screen and write the code',
  Contest_Problem: 'Contest Problem',
  Show_Tags: 'Show Tags',
  Hide_Tags: 'Hide Tags',
  No_tag: 'No tag',
  Statistic: 'Statistic',
  Solutions: 'Solutions',
  Description: 'Description',
  Input: 'Input',
  Output: 'Output',
  Sample_Input: 'Sample Input',
  Sample_Output: 'Sample Output',
  Hint: 'Hint',
  Source: 'Source',
  Status: 'Status',
  Information: 'Information',
  Time_Limit: 'Time Limit',
  Memory_Limit: 'Memory Limit',
  Other: 'Other',
  Created: 'Created By',
  Please_login_first: 'Please login first!',
  Please_login_first_by_admin_account:'Please login again with the administrator account!',
  Submit: 'Submit',
  Online_Test:'Online Test',
  Submitting: 'Submitting',
  runCode: 'run code',
  saveCode: 'save code',
  shareCode: 'share code',


  Judging: 'Judging',
  Wrong_Answer: 'Wrong Answer',
  View_Contest: 'View Contest',
  Are_you_sure_you_want_to_reset_your_code: 'Are you sure you want to reset the original code? If the problem has a code template, it will be restored to the code of the original template, otherwise the code being edited will be cleared.',
  Are_you_sure_you_want_to_get_your_recent_accepted_code:'Are you sure you want to get the recently passed code and fill it in the code box? Note: This operation will overwrite the code being edited!',
  You_havenot_passed_the_problem_so_you_cannot_get_the_code_passed_recently:'You haven\'t passed the problem, so you can\'t get the code passed recently',
  Code_can_not_be_empty: 'Code can not be empty',
  Code_Length_can_not_exceed_65535: 'The code should contain no more than 65535 characters!',
  Submit_code_successfully: 'Submit code successfully',
  You_have_solved_the_problem: 'You have solved the problem',
  Submitted_successfully: 'Submitted successfully',
  Submitted_Not_Result: 'Submitted successfully, but the result is not known at present',
  You_have_submitted_a_solution: 'You have submitted a solution.',
  Contest_has_ended: 'Contest has ended',
  You_have_submission_in_this_problem_sure_to_cover_it: 'You have submission in this problem, sure to cover it?',
  Close: 'Close',
  Cancel: 'Cancel',
  OK: 'OK',
  Copied_successfully: 'Copied successfully',
  Copied_failed: 'Copied failed',


  // /views/oj/status/SubmissionList.vue
  Mine: 'Mine',
  Time: 'Time',
  Memory: 'Memory',
  Length: 'Length',
  Language: 'Language',
  View_submission_details: 'View submission details',
  Judger: 'Judger',
  Author: 'Author',
  Submit_Time: 'Submit Time',
  Option: 'Option',
  Rejudge: 'Rejudge',
  Resubmitted_Successfully: 'Resubmitted Successfully!',
  Refresh: 'Refresh',
  Enter_Problem_ID: 'Enter Problem ID',
  Enter_Author: 'Enter Author',
  Run_ID: 'Run ID',
  Problem: 'Problem',
  Problem_Score: 'Problem Score',
  OI_Rank_Score: 'OI Rank Score',
  OI_Rank_Calculation_Rule: 'OI Rank Calculation Rule',
  Cancel_Evaluation: 'Cancel',
  Modify_Evaluation:'Modify',
  Has_Been_Manually_Judged:'Has been manually judged',
  Manually_Jugde:'Manually Judge',
  Cancel_Judge_Tips:'Are you sure you want to mark this submission as Cancelled?',
  Cancel_Successfully:'Cancel Successfully',
  Click_to_Manually_Judge:'Click to Manually Judge',

  // /views/oj/status/SubmissionDetails.vue
  Test_point_details: 'Test point details',
  Copy: 'Copy',
  Shared: 'Shared',
  Unshared: 'Unshared',
  Shared_successfully: 'Shared successfully',
  Cancel_Sharing_Successfully: 'Cancel Sharing Succeessfully',
  Input_File: 'Input File',
  Output_File: 'Output File',
  Case_tips: 'Case Tips',
  Nothing: 'Nothing',
  Subtask: 'Subtask',

  // /views/oj/rank/ACMRank.vue
  ACM_Ranklist: 'ACM Ranklist',
  User: 'User',
  Nickname: 'Nickname',
  Signature: 'Signature',
  Rating: 'Rating',
  Rank_Search_Placeholder: 'Please enter username, nickname or real name.',

  // /views/oj/rank/OIRank.vue
  OI_Ranklist: 'OI Ranklist',

  // /views/oj/discussion/discussionList.vue
  Go_to_problem:"Go to Problem",
  Release_Time: 'Release Time',
  Likes: 'Likes',
  Like_Successfully: 'Like Successfully',
  Cancel_Like_Successfully: 'Cancel Successfully',
  Views: 'Views',
  Edit: 'Edit',
  Delete: 'Delete',
  Post_discussion: 'Post Discussion',
  Post_problem_discussion: 'Post Problem Discussion',
  General_discussion: 'General Discussion',
  Return: 'Return',
  Category: 'Category',
  Discussion_title: 'Title',
  Discussion_Desc: 'Description',
  Associated_Problem:'Associated Problem ID',
  Associated_Problem_Tips:'Associated Problem ID, e.g. P1000',
  Discussion_Category: 'Category',
  Discussion_top: 'Top',
  Discussion_content: 'Content',
  Create_Discussion: 'Create Discussion',
  Edit_Discussion: 'Edit Discussion',
  Delete_Discussion_Tips: 'This operation will delete the discussion, including the associated comments and replies. Do you want to continue?',
  Delete_successfully: 'Delete Successfully',
  Post_successfully: 'Post Successfully',
  Send_successfully:'Send Successfully',
  Can_not_exceed_255:'length cannot exceed 255',
  Can_not_exceed_65535:'length cannot exceed 65535',
  Can_not_exceed_10000:'length cannot exceed 10000',

  // /views/oj/discussion/discussion.vue
  Report: 'Report',
  Like: 'Like',
  Liked: 'Liked',
  Report_Reason: 'Report Reason',
  The_report_label_and_reason_cannot_be_empty: 'The report label and reason cannot be empty.',

  // 404.vue
  Page_Not_Found:"Sorry, the page can't be found",
  Go_Home: 'Go Home',
  Back: 'Back',

  // /views/oj/contest/ContestList.vue
  Contest_Rule: 'Rule',
  Running: 'Running',
  Scheduled: 'Scheduled',
  Ended: 'Ended',
  No_contest: 'No contest',
  Contests: 'Contests',
  Public: 'Public',
  Private: 'Private',
  Protected: 'Protected',
  Public_Tips: 'Public - Any one can see and submit.',
  Protected_Tips: 'Protected - Any one can see, but only users knowing contest password can submit.',
  Private_Tips: 'Private - Only users knowing contest password can see and submit.',
  Contest_Outside_ScoreBoard: 'OutSide Contest ScoreBoard',

  // /views/oj/contest/ContestDetail.vue
  StartAt: 'StartAt',
  EndAt: 'EndAt',
  Password_Required: 'Password Required',
  To_Enter_Need_Password: 'To enter the Private contest, please input the password!',
  Enter_the_contest_password: 'Enter the contest password',
  Enter: 'Enter',
  Overview: 'Overview',
  Announcement: 'Announcement',
  Submissions: 'Submissions',
  Rankings: 'Rankings',
  Comment: 'Comment',
  Print: 'Print',
  Admin_Print: 'Admin Print',
  Admin_Helper: 'AC Info',
  ScrollBoard: 'Scroll Board',
  Register_contest_successfully: 'Register contest successfully',
  Please_check_the_contest_announcement_for_details: 'Please check the contest announcement for details',
  Go_To_Group_Contest_List:'Go to Group Contest List',
  Group_Contest_Tag:'Group Contest',
  Contains_Submission_After_Contest:'Contains Submission After Contest',

  // /views/oj/contest/children/ACMContestRank.vue
  Contest_Rank: 'Contest Rank',
  Menu: 'Menu',
  Chart: 'Chart',
  Table: 'Table',
  Auto_Refresh: 'Auto Refresh',
  RealName: 'RealName',
  Force_Update: 'Force Update',
  Download_as_CSV: 'Download as CSV',
  TotalTime: 'Time',
  Top_10_Teams: 'Top 10 Teams',
  save_as_image: 'save as image',
  Contest_Rank_Seq: 'Rank',
  Star_User: 'Star User',
  Unfollow: 'Unfollow',
  Top_And_Follow: 'Top And Follow',
  Contest_Rank_Search_Placeholder: 'Please enter the organization rank name or school name',
  Contest_Rank_Setting: 'Rank Setting',
  Contest_Setting: 'Contest Setting',
  Contains_After_Contest: 'Contains After Contest',

  // /views/oj/contest/children/ACMInfo.vue
  AC_Time: 'AC Time',
  First_Blood: 'First Blood',
  Checked: 'Checked',
  Not_Checked: 'Not Checked',
  Check_It: 'Check It',
  Accepted: 'Accepted',

  // /views/oj/contest/children/ContestPrint.vue
  Print_Title: 'Contest Text Printing',
  Print_tips: 'Please put the text to be printed into the content box, and then submit. Note: please do not submit maliciously!',
  Content: 'Content',
  Content_cannot_be_empty: 'Tne content cannot be empty!',
  The_number_of_content_cannot_be_less_than_50: 'The number of words cannot be less than 50',
  Success_submit_tips: 'Submitted successfully! Please wait patiently for the staff to print!',

  // /views/oj/contest/children/ContestAdminPrint.vue
  Download: 'Download',
  Printed: 'Printed',
  Not_Printed: 'Not Printed',

  // /views/oj/contest/children/ContestRejudgeAdmin.vue
  Contest_Rejudge: 'Contest Rejudge',
  ID: 'ID',
  Contest_Rejudge_Tips: 'Are you sure you want to rejudge all submissions of the questions?',
  Rejudge_All: 'Rejudge All',
  Rejudge_successfully: 'Rejudge successfully',

  // /views/oj/contest/children/ScrollBoard.vue
  ScrollBoard_Parameter_Config: 'Scroll Board Config',
  Start_Rolling: 'Start Rolling',
  Contest_ID: 'Contest ID',
  Number_of_gold_medals: 'Number of gold medals',
  Number_of_silver_medals: 'Number of silver medals',
  Number_of_bronze_medals: 'Number of bronze medals',
  Formula_for_calculating_the_number_of_medals: 'Formula for calculating the number of medals',
  Whether_to_remove_the_star_user: 'Whether to remove the star organization',
  Contest_Non_Ended_But_Want_to_Scroll_Board: 'The contest is not over yet. Are you sure you want to sroll the board?',

  // /views/oj/contest/children/OIContestRank.vue
  Total_Score: 'Total Score',
  Based_on_The_Highest_Score_Submitted_For_Each_Problem: 'Based on the highest score submitted for each problem',
  Based_on_The_Recent_Score_Submitted_Of_Each_Problem: 'Based on the recent score submitted of each problem',

  // /views/oj/about/Introduction.vue
  Compiler: 'Compiler',
  Example: 'Example',
  Result_Explanation: 'Result Explanation',
  Pending_Description: 'Your solution is waiting be judged, please wait for the result...',
  Submitted_Faild_Description: 'Your submission failed this time, please click the button to submit again.',
  Compiling_Description: 'Your source code is being compiled, please wait for the result...',
  Judging_Description: 'Your program is running with test data. Please wait for the result...',
  Compile_Error_Description: "Failed to compile your source code. Click on the link to see compiler's output.",
  Persentation_Error_Description: 'The code you submitted is very close to the correct answer. Please check whether there are extra spaces, newlines and other blanks in the code format output.',
  Partial_Accepted_Description:'Come on! The code you submitted has passed some test points. Please consider other possibilities.',
  Accepted_Description: 'Congratulations! Your solution is correct.',
  Wrong_Answer_Description: "Your program's output doesn't match judger's answer.",
  Runtime_Error_Description: 'Your program terminated abnormally. Possible reasons are: segment fault, divided by zero or exited with code other than 0.',
  Time_Limit_Exceeded_Description: 'The time your program used has exceeded limit.',
  Memory_Limit_Exceeded_Description: 'The memory your program actually used has exceeded limit.',
  System_Error_Description: 'Oops, something has gone wrong with the judger. Please report this to administrator.',
  Cancelled_Description:'Your submission has been cancelled!',
  Compile_Explanation: 'Compile Explanation',
  Compile_Tips1:"`__int64` is not defined by ANSI standard and can only be used in `VC`. It should be written as `long long` type in `GNU C++`. For `scanf` and `printf`, please use `%lld` as the format.",
  Compile_Tips2:"The return value of `main()` must be defined as `int`, not `void`",
  Compile_Tips3:"`i` lost definition outside the loop,\"for(int i=0...){...}\"",
  Compile_Tips4:"`itoa` is not an ANSI standard function (not available in standard `C/C++`)",

  // /views/oj/about/Developer.vue
  Leader_BackEnd_FrontEnd_Engineer: 'Leader & BackEnd | FrontEnd Engineer',
  Group_Function_Development_Contributor: 'Group Function Development Contributor',
  Distributed: 'Distributed',
  Distributed_Desc: 'It is divided into frontend and backend separation, and supports the micro service cluster',
  Customization: 'Customization',
  Customization_Desc: 'The website configuration is highly integrated and supports customized modification',
  Security: 'Security',
  Security_Desc: 'The Sandbox is isolated by CGroup, and the website authority control is perfect',
  Diversity: 'Diversity',
  Diversity_Desc: 'Support Codefoces, HDU, POJ, GYM, AtCoder, SPOJ remote judge',
  Available: 'Available',
  Faulty: 'Faulty',

  // /components/oj/common/Announcements.vue
  Contest_Announcement: 'Contest Announcement',
  No_Announcements: 'No Announcements',

  // /components/oj/common/CodeMirror.vue
  Lang: 'Lang',
  Code_Editor_Setting:'Code Editor Setting',
  Setting:'Setting',
  Theme: 'Theme',
  FontSize: 'Font Size',
  TabSize:'Tab Size',
  Two_Spaces:'2 spaces',
  Four_Spaces:'4 spaces',
  Eight_Spaces:'8 spaces',
  Reset_Code: 'Reset Code',
  Upload_file: 'Upload file',
  monokai: 'Monokai',
  solarized: 'Molarized Light',
  material: 'Material',
  idea:'IDEA',
  eclipse:'Eclipse',
  base16_dark:'Base16-dark',
  cobalt:'Cobalt',
  dracula:'Dracula',
  Test_Case: 'Test Case',
  Test_Result: 'Test Result',
  Running_Test: 'Running Test',
  Non_Test_Judge_Tips:'After entering the test case, click Running test, and the running results will be displayed here.',
  Problem_Uncertain_Answer: 'Note: there may be multiple outputs that meet the requirements of this problem. Please judge whether the program output passes.',
  Fill_Case: 'Fill Case',
  Compilation_Failed: 'Compilation Failed',
  Test_Input: 'Test Input',
  Expected_Output: 'Expected Output',
  Real_Output: 'Real Output',
  Pass_Test_Case: 'Pass Test Case',
  Get_Recently_Passed_Code:'Get Recently Passed Code',
  Enter_Focus_Mode:'Enter Focus Mode',
  Exit_Focus_Mode:'Exit Focus Mode',

  // /components/oj/comment/Comment.vue
  Announcement_of_contest_Q_and_A_area: 'Announcement of Contest Q & A Area',
  Announcement_of_contest_Q_and_A_area_tips1:"Please don't ask questions irrelevant to the contest. No irrigation!",
  Announcement_of_contest_Q_and_A_area_tips2: 'During the contest, only the comments of yourself and the contest administrator can be seen!',
  Announcement_of_contest_Q_and_A_area_tips3:"The contest administrator's comments cannot be replied. The comments return to normal at the end of the contest!",
  Come_and_write_down_your_comments: 'Come and write down your comments',
  Inline_Code: 'Inline Code',
  Code_Block: 'Code Block',
  Link: 'Link',
  Unordered_list: 'Unordered List',
  Ordered_List: 'Ordered List',
  Submit_Comment: 'Submit',
  All_Comment: 'All Comment',
  Reply: 'Reply',
  Reply_Successfully: 'Reply Successfully',
  Comment_Successfully: 'Comment Successfully',
  Reply_Total: 'Total',
  Replies: 'replies',
  Click_Show_All: 'Click to Show All',
  Pick_up:"Pick up",
  Load_More: 'Load More',
  Delete_Comment_Tips: 'This operation will delete the comment and all its replies. Do you want to continue?',
  Delete_Reply_Tips: 'This operation will delete the reply. Do you want to continue?',
  Reply_Content:'The reply content',
  Comment_Content:'The comment content',


  // /views/oj/message/message.vue
  Message_Center: 'Message Center',
  No_Data: 'No Data',

  // /views/oj/message/UserMsg.vue
  Msg_Total: 'Total',
  Msg_Messages: 'messages',
  DiscussMsg: 'Discuss',
  ReplyMsg: 'Reply',
  LikeMsg: 'Likes',
  SysMsg: 'System',
  MineMsg: 'Mine',
  Clean_All: 'Clean All',
  Action_Like_Discuss: 'Praised My Comment',
  Action_Like_Post: 'Praised My Discussion Post',
  Action_Discuss: 'Commented on My Discussion Post',
  Action_Reply: 'Responded to My Comment',
  From_Discussion_Post: 'From Discussion Post',
  From_the_Contest: 'From the Contest',
  Delete_Msg_Tips: 'Are you sure you want to delete the message?',

  // /views/oj/training/TrainingList.vue
  Search_Training: 'Search Training',
  Training_Public: 'Public',
  Training_Private: 'Private',
  Training_Category: 'Category',
  Number: 'ID',
  Problem_Number: 'Num',
  Recent_Update: 'Updated',
  Progress:'Progress',

  // /views/oj/training/TrainingDetails.vue
  Training_Introduction: 'Training Introduction',
  Training_Number: 'Number',
  Training_Auth: 'Training Auth',
  Training_Total_Problems: 'Total Problems',
  Record_List: 'Record List',
  To_Enter_Training_Need_Password: 'To enter the Private training, please input the password!',
  Enter_the_training_password: 'Enter the training password',
  Register_training_successfully: 'Register training successfully',

  // /views/oj/training/TrainingRank.vue
  Total_AC: 'Total  AC',
  Training_Rank_Search_Placeholder: 'Please enter the organization name, real name or school.',

  // /views/oj/group/GroupList.vue
  Search_Group: 'Search Group',
  Create_Group: 'Create Group',
  All_Group: 'All Group',
  My_Group: 'My Group',
  No_Groups: 'No Groups',
  Group_Name: 'Group Name',
  Group_Short_Name: 'Group Short Name(display ID prefix for group problems)',
  Group_Brief: 'Group Brief',
  Group_Description: 'Group Description',
  Group_Public: 'Public',
  Group_Protected: 'Protected',
  Group_Private: 'Private',
  Group_Hidden: 'Hidden',
  Group_Auth: 'Group Auth',
  Group_Owner: 'Group Owner',
  Group_Code: 'Group Code',
  Join_Group_By_Code: "Join Group by Code",
  Enter_Group_Code: "Enter Group Code",
  Join_Group_Success: 'Join Group Success',
  Join_Group_Failed: 'Join Group Failed',
  Join_Group: "Join Group",
  Total_Members: 'Total Members',
  Group_Public_Tips: 'Public - Free to join in.',
  Group_Protected_Tips: 'Protected - Apply to join in.',
  Group_Private_Tips: 'Private - Require invitation code to apply.',
  Group_Hidden_Tips: 'Hidden - Only group members can see.',
  Create_Successfully: 'Create Successfully',
  Group_Name_Check_Required: 'The group name is required.',
  Group_Name_Check_Min_Max: 'The length of group name is 5 to 25',
  Group_Short_Name_Check_Required: 'The group shortname is required.',
  Group_Short_Name_Check_Min_Max: 'The length of group shortname is 5 to 10',
  Group_Brief_Check_Required: 'The group brief is required.',
  Group_Brief_Check_Min_Max: 'The length of group brief is 5 to 50',
  Group_Auth_Check_Required: 'The group auth is required.',
  Group_Code_Check_Required: 'The invitation code is required.',
  Group_Code_Check_Min_Max: 'The length of invitation code is 6',
  Group_Description_Check_Required: 'The group description is required.',
  Group_Description_Check_Min_Max: 'The length of group description is 5 to 1000',

  // /views/oj/group/GroupDetails.vue
  Apply_Group: 'Apply to Join',
  Apply_Successfully: 'Apply Successfully',
  Exit_Group: 'Exit Group',
  Exit_Group_Tips: 'Are you sure you want to exit the group? After exiting, you will no longer be able to view group resources!',
  Exit_Successfully: 'Exit Successfully',
  Disband_Group:'Disband Group',
  Disband_Group_Tips:'Are you sure you want to disband the group?',
  Disband_Successfully:'Disband Successfully',
  Group_Number: 'Number',
  Group_Home: 'Home',
  Group_Problem: 'Problem',
  Group_Training: 'Training',
  Group_Contest: 'Contest',
  Group_Submission: 'Submission',
  Group_Discussion: 'Discussion',
  Group_Setting: 'Setting',
  Group_Rank: 'Rank',
  Group_Announcement: 'Announcement',
  Code_Monitor: 'code monitor',
  Group_Member: 'Member',
  Apply_Reason: 'Apply Reason',
  Apply_Reason_Check_Required: 'The apply reason is required.',
  Apply_Reason_Check_Min_Max: 'The length of apply reason is 5 to 100',

  // /views/oj/group/GroupTrainingList.vue
  Back_To_Admin_Training_List: 'Back to Admin Trainingt List',
  Back_To_Training_List: 'Back To Training List',
  Back_Admin_Training_Problem_List: 'Back Admin Training Problem List',

  // /views/oj/group/GroupContestList.vue
  Back_To_Admin_Contest_List:'Back to Admin Contest List',
  Back_To_Contest_List:'Back to Contest List',
  Back_Admin_Contest_Problem_List:'Back to Admin Contest Problem List',

  // /views/oj/group/GroupDiscussionList.vue
  No_Discussion: 'No Discussion',
  Problem_Discussion:'Problem Discussion',

  // /views/oj/group/GroupProblemList.vue
  Cancel_Admin:'Cancel Admin',
  Back_To_Problem_List:'Back to Problem List',
  Back_To_Admin_Problem_List:'Back to Admin Problem List',

  // /views/oj/group/GroupMemberList.vue
  Applying: 'Applying',
  Refused: 'Refused',
  General_Member: 'General Member',
  Member_Admin: 'Member Admin',
  Join_Time: 'Joined Time',
  Change_Time: 'Changed Time',
  Member_Auth: 'Member Auth',
  Group_Admin: 'Admin',
  Group_Root: 'Root',
  View_Reason: 'View Reason',
  Delete_Member: 'Kick Member',
  Delete_Member_Tips: 'Are you sure you want to kick the member out of the group? After kicking out, the member will no longer be able to view group resources! What\'s more, if the member is the owner, the group will be disbanded together!',

  // /views/oj/group/GroupSetting.vue
  Add_From_Group_Problem: 'Add_From_Group_Problem',

  // /views/oj/group/GroupSetting.vue
  Group_Visible: 'Show in group list',
  Group_Not_Visible: 'Hide in group list',

  // /views/oj/group/GroupRank.vue
  Group_ACM_Rank_Type: 'ACM Rank',
  Group_OI_Rank_Type: 'OI Rank',

  // GroupAdminProblemList.vue
  Group_Problem_Apply_Public:'Apply to join the public problem bank',
  Already_Public_Problem:'Already in the public problem bank',
  Applying_for_Publicity:'Applying for publicity',
  Group_Problem_Apply_Public_Tips:'Are you sure you want to apply for this problem to be added to the public bank?',
  Cancel_Group_Problem_Apply_Public_Tips:'Do you want to cancel the application for this problem to be added to the public bank?',

  // GroupCourse.vue
  Group_Course: 'Group Course',

  // 作答内容相关
  Answer_Content: 'Answer Content',
  View_answer_details: 'View answer details',
  Answer_Details: 'Answer Details',
  Option_Key: 'Option Selected',
  Option_Content: 'Option Content',
  Is_Correct: 'Correct',
  Single_Choice: 'Single Choice',
  Multiple_Choice: 'Multiple Choice',
  Fill_Blank: 'Fill in the Blank',
  Short_Answer: 'Short Answer',
  Correct_Answer: 'Correct Answer',
  Wrong_Answer: 'Wrong Answer',
  View_Details: 'View Details',
  Problem_Type: 'Problem Type',
  Selected_Answer: 'Selected Answer',
  Display_Answer: 'Display Answer',
}
