export const m = {

  // 新增
  My_Share: '我的分享',
  My_Code: '我的代码',
  TuC: 'TuC',
  Course: '课程',
  Drawing_Board: '画板',
  Login_to_view_your_code: '登陆查看你的代码',


  // /src/common/api.js
  Query_error_unable_to_find_the_resource_to_request: '查询错误，找不到要请求的资源！',
  Server_error_please_refresh_again: '服务器错误，请重新刷新！',
  Request_timed_out_please_try_again_later: '请求超时，请稍后再尝试！',
  Network_error_abnormal_link_with_server_please_try_again_later: '网络错误，与服务器链接出现异常，请稍后再尝试！',
  Error:'错误',
  Success:'成功',
  No_Access_There_is_no_open_discussion_area_on_the_website: '禁止访问：当前网站未开启讨论区！',
  No_Access_There_is_no_open_group_discussion_area_on_the_website: '禁止访问：当前网站未开启班级讨论区！',
  No_Access_There_is_no_open_contest_comment_area_on_the_website: '禁止访问：当前网站未开启比赛评论区！',

  // /components/oj/common/NavBar.vue 导航栏
  NavBar_Home: '首页',
  NavBar_Problem: '题目',
  NavBar_Training: '训练',
  NavBar_Contest: '比赛',
  NavBar_Status: '评测',
  NavBar_Rank: '排名',
  NavBar_ACM_Rank: 'ACM 排名',
  NavBar_OI_Rank: 'OI 排名',
  NavBar_Discussion: '讨论',
  NavBar_Group: '班级',
  NavBar_About: '关于',

  NavBar_Introduction: '简介',
  NavBar_Developer: '开发者',
  NavBar_Login: '登录',
  NavBar_Register: '注册',
  NavBar_UserHome: '我的首页',
  NavBar_Submissions: '我的提交',
  NavBar_Setting: '我的设置',
  NavBar_Management: '后台管理',
  NavBar_Logout: '退出登录',
  Dialog_Login: '登录',
  Dialog_Register: '注册',
  Dialog_Reset_Password: '重置密码',
  Click_To_Change_Web_Language: '点击切换网站语言',
  NavBar_Back_Home:'前往首页',
  NavBar_Training_Home: '训练主页',
  NavBar_Contest_Home: '比赛主页',
  NavBar_Contest_Own_Submission: '我的提交',
  NavBar_Contest_Rank: '比赛榜单',
  NavBar_Group_Home: '班级主页',

  //  /components/oj/common/Login.vue 登录弹窗
  Login_Username: '用户名',
  Login_Password: '密码',
  Login_Btn: '登录',
  Slide_Verify: '请向右滑动验证',
  Slide_Verify_Success: '验证成功',
  Login_No_Account: '没有账号？立即注册!',
  Login_Forget_Password: '忘记密码',
  Username_Check_Required: '用户名不能为空',
  Username_Check_Max: '用户名长度不能超过20位',
  Password_Check_Required: '密码不能为空',
  Password_Check_Between: '请输入长度为6~20位的密码',
  Welcome_Back: '欢迎回来~',

  // /components/oj/common/Register.vue 注册弹窗
  Register_Username: '请输入手机号',
  Register_Password: '请输入密码',
    Register_Password_Again: '请再次输入密码',
    Register_Email: '请输入邮箱，点击右侧发送验证码',
    Register_Email_Captcha: '请输入的验证码',
    Register_Btn: '注册',
    Register_Already_Registed: '已有账号？立即登录！',
    The_username_already_exists: '用户名已存在',
    The_email_already_exists: '邮箱已存在',
    Password_does_not_match: '两次输入密码不一致',
    Email_Check_Required: '邮箱不能为空',
    Email_Check_Format: '邮箱格式不正确',
    Phone_Check_Required: '手机号格式不正确',
    Phone_Check_Format: '手机号格式不正确',
    Password_Again_Check_Required: '请再次输入密码',
    Code_Check_Required: '验证码不能为空',
    Code_Check_Length: '请输入6位数字的验证码',
    The_system_is_processing: '请稍等... 系统正在处理...',
    Register_Send_Email_Msg: '发送成功！如果长时间没收到，请检查你的邮箱是否准确！',
    Register_Send_Phone_Msg: '发送成功！如果长时间没收到，请检查你的手机号是否准确！',

    Thanks_for_registering: '感谢您的注册，您现在可以登录了',

  // /components/oj/common/ResetPassword.vue 重置密码弹窗
  // /views/oj/organization/SetNeWPassword.vue 设置新密码页
  Reset_Password_Email: '请输入您的邮箱',
  Reset_Password_Captcha: '请输入验证码',
  Send_Password_Reset_Email: '发送密码重置邮件',
  Waiting_Can_Resend_Email: '请稍等片刻，重新发送重置邮件...',
  ResetPwd_Send_Email_Msg: '发送成功！如果长时间没收到邮件，请检查你的邮箱是否准确！',
  Remember_Passowrd_To_Login: '咦，好像记得密码? 请尝试登录!',
  Set_New_Password: '设置新密码',
  Set_New_Password_Msg: '请输入新密码',
  Set_New_Password_Again_Msg: '请再次输入新密码',
  The_username_does_not_exists: '用户名不存在',
  The_email_does_not_exists: '邮箱不存在',
  Your_password_has_been_reset: '您的密码已重置',

  // /components/oj/setting/Account.vue 账号信息管理页面
  Old_Password: '当前密码',
  New_Password: '新密码',
  Confirm_New_Password: '确认新密码',
  Current_Password: '当前密码',
  Old_Email: '当前邮箱',
  New_Email: '新邮箱',
  Get_Captcha: '获取验证码',
  Old_Mobile: '当前手机号',
  New_Mobile: '新手机号',
  Change_Password: '更改密码',
  Change_Email: '更改邮箱',
  Change_Mobile: '更改手机号',
  Update_Password: '更新密码',
  Update_Email: '更新邮箱',
  Update_Mobile: '更新手机号',
  Captcha: '验证码',
  The_new_password_does_not_change: '新密码未变动',
  The_new_email_does_not_change: '新邮箱未变动',
  The_new_mobile_does_not_change: '新手机号未变动',
  Mobile_Check_Required: '手机号不能为空',
  Mobile_Check_Format: '手机号格式不正确',
  Change_Email_Captcha: '请输入的验证码',
  Change_Mobile_Captcha: '请输入短信中的验证码',
  Change_Send_Email_Msg: '发送成功！如果长时间没收到邮件，请检查你的邮箱是否准确！',
  Change_Send_Mobile_Msg: '发送成功！如果长时间没收到短信，请检查你的手机号是否准确！',
  Update_Successfully: '更新成功',
  Update_Failed: '更新失败',
  Guess_robot: '您的操作太快啦，可能是机器操作！请再次验证！',
  The_current_password_cannot_be_empty: '当前密码不能为空',
  The_new_password_cannot_be_empty: '新密码不能为空',
  The_new_email_cannot_be_empty:'新邮箱不能为空',


  // /components/oj/setting/UserInfo.vue
  Avatar_Setting: '头像设置',
  Upload_avatar_hint: '将头像拖放到此处，或单击此处',
  CF_Username: 'Codeforces 用户名',
  School: '学校',
  Student_Number: '学号',
  Blog: '博客',
  Github: 'Github',
  Gender: '性别',
  Male: '男',
  Female: '女',
  Secrecy: '保密',
  Save: '保存',
  Upload: '上传',
  Your_new_avatar: '您的新头像',
  Upload_Avatar_Successfully: '上传头像成功',
  File_type_not_support: '文件类型不支持',
  is_incorrect_format_file: '的文件格式不正确，请选择.gif,.jpg,.jpeg,.png,.bmp,.webp的图片文件。',
  Exceed_max_size_limit: '超过文件大小限制',
  File_Exceed_Tips: '文件大小错误, 您只能上传不大于2MB的图片文件！',
  Cancel_Avater_Tips: '您确定取消该图像的截取？',

  // /views/oj/organization/UserHome.vue
  Recent_login_time: '最近上线时间：',
  Not_set_yet: '这个家伙很懒，什么也没写…',
  UserHome_Solved: '已解决',
  UserHome_Submissions: '总交题数',
  UserHome_Score: 'OI分数',
  UserHome_Rating: 'CF分数',
  List_Solved_Problems: '全部已解决问题列表',
  UserHome_Not_Data: '这家伙太懒了，还没有做过题...',
  Personal_Profile: '个人简介',
  UserHome_Solved_Problems: '做题情况',
  Thermal_energy_table_submitted_in_the_last_year:'最近一年提交热力表',
  Difficulty_Statistics:'难度统计',
  Problems:'题',
  Calendar_Tooltip_Uint:'次提交',
  Jan: '一月',
  Feb: '二月',
  Mar: '三月',
  Apr: '四月',
  May: '五月',
  Jun: '六月',
  Jul: '七月',
  Aug: '八月',
  Sep: '九月',
  Oct: '十月',
  Nov: '十一月',
  Dec: '十二月',
  Sun: '周日',
  Mon: '周一',
  Tue: '周二',
  Wed: '周三',
  Thu: '周四',
  Fri: '周五',
  Sat: '周六',
  Less:'较少',
  More:'较多',
  on:'在',

  // /views/oj/organization/Setting.vue
  Account_Setting: '账户设置',
  UserInfo_Setting: '资料设置',

  // App.vue 底部文案
  Service: '服务',
  Judging_Queue: '评测队列',
  System_Info: '系统信息',
  Development: '开发',
  Open_Source: '开源',
  Support: '支持',
  Help: '帮助',
  Group: 'Q群',

  // /views/oj/Home.vue
  Welcome_to: '欢迎使用 ',
  Recent_7_Days_AC_Rank: '最近一周过题榜单',
  Other_OJ_Contest: '其它OJ的近期比赛',
  Latest_Problem: '最新题目',
  Supported_Remote_Online_Judge: '支持的远程评测平台',
  Statistics_Submissions_In_The_Last_Week: '最近一周提交统计',


  // 表格通用列名,按钮，搜索框等
  Enter_keyword: '输入关键词',
  Reset: '重置',
  Username: '用户名',
  Solved: '已解决',
  OJ: 'OJ',
  Title: '标题',
  Begin_Time: '开始时间',
  End_Time: '结束时间',
  Contest_Time: '比赛时间',
  Recent_Contest: '近期比赛',
  Problem_ID: '题目ID',
  Total: '总数',
  AC_Rate: 'AC 通过率',
  AC: '通过',
  Score: '分数',

  // /views/oj/problem/problemList.vue
  Problem_List: '题目列表',
  Problem_Bank: '题库',
  All: '全部',
  My_OJ: '主题库',
  Level: '难度',
  Tags: '标签',
  Search_Filter_Tag: '输入标签名称...',
  Pick_a_random_question: '随机选一题',
  Touch_Get_Status: '请点击或悬停鼠标至问题行查看提交状态',
  Good_luck_to_you: '祝你好运!',

  // /views/oj/problem/Problem.vue
  Problem_Description: '题目描述',
  My_Submission: '我的提交',

  Problem_Annex: '题目附件',
  Login_to_view_your_submission_history: '登录以查看您的提交记录',
  Shrink_Sidebar: '收缩侧边栏',
  View_Problem_Content: '查看题目内容',
  Only_View_Problem: '只看题目内容',
  Put_away_the_full_screen_and_write_the_code: '收起全屏，编写代码',
  Contest_Problem: '比赛题目',
  Show_Tags: '显示标签',
  Hide_Tags: '隐藏标签',
  No_tag: '暂无标签',
  Statistic: '题目统计',
  Solutions: '全部提交',
  Description: '题目描述',
  Input: '输入描述',
  Output: '输出描述',
  Sample_Input: '样例输入',
  Sample_Output: '样例输出',
  Hint: '说明',
  Source: '来源',
  Status: '状态',
  Information: '题目信息',
  Time_Limit: '时间限制',
  Memory_Limit: '内存限制',
  Other: '其他语言',
  Created: '出题人',
  Please_login_first: '请先登录!',
  Please_login_first_by_admin_account:'请重新使用管理员账号登录！',
  Submit: '提交评测',
  Online_Test:'在线自测',
  Submitting: '提交中',
  runCode: '运行代码',
  saveCode: '保存代码',
  shareCode: '分享代码',


  Judging: '正在评测',
  Wrong_Answer: '答案错误',
  View_Contest: '查看比赛',
  Are_you_sure_you_want_to_reset_your_code: '你是否确定要重置原始代码？如果该题有代码模板，则还原为原始模板的代码，否则正在编辑的代码将清空。',
  Are_you_sure_you_want_to_get_your_recent_accepted_code:'你是否确定要获取最近通过的代码并填充至代码框内？注意：该操作会覆盖正在编辑的代码！',
  You_havenot_passed_the_problem_so_you_cannot_get_the_code_passed_recently:'你还未通过该题目，无法获取最近通过的代码！',
  Code_can_not_be_empty: '代码不能为空',
  Code_Length_can_not_exceed_65535: '代码的字符长度不能超过65535！',
  Submit_code_successfully: '提交代码成功',
  You_have_solved_the_problem: '你已经解决了该问题',
  Submitted_successfully: '提交成功',
  Submitted_Not_Result: '提交成功，判题结果暂不可知',
  You_have_submitted_a_solution: '您已有提交记录',
  Contest_has_ended: '比赛已结束',
  You_have_submission_in_this_problem_sure_to_cover_it: '您已经提交过该问题的代码，确定重新提交？',
  Close: '关闭',
  Cancel: '取消',
  OK: '确定',
  Copied_successfully: '复制成功',
  Copied_failed: '复制失败',


  // /views/oj/status/SubmissionList.vue
  Mine: '我的',
  ID: 'ID',
  Time: '运行时间',
  Memory: '运行内存',
  Length: '代码长度',
  Language: '语言',
  View_submission_details: '查看提交详情',
  Judger: '判题源',
  Author: '作者',
  Submit_Time: '提交时间',
  Option: '操作',
  Rejudge: '重新评测',
  Resubmitted_Successfully: '重新提交成功！',
  Refresh: '刷新',
  Enter_Problem_ID: '请输入题目ID',
  Enter_Author: '请输入作者',
  Run_ID: 'Run ID',
  Problem: '题目',
  Problem_Score: 'OI题目总分数',
  OI_Rank_Score: 'OI排行榜得分',
  OI_Rank_Calculation_Rule: 'OI排行得分计算公式',
  Cancel_Evaluation: '取消评测',
  Modify_Evaluation:'修改评测',
  Has_Been_Manually_Judged:'已被人工评测',
  Manually_Jugde:'人工评测',
  Cancel_Judge_Tips:'你是否确定将该提交标记为已取消？',
  Cancel_Successfully:'取消成功',
  Click_to_Manually_Judge:'点击进行人工评测',


  // /views/oj/status/SubmissionDetails.vue
  Test_point_details: '测试点详情',
  Copy: '复制',
  Shared: '分享',
  Unshared: '不分享',
  Shared_successfully: '分享成功',
  Cancel_Sharing_Successfully: '取消分享成功',
  Input_File: '输入文件',
  Output_File: '输出文件',
  Case_tips: '信息提示',
  Nothing: '无',
  Subtask:'子任务',

  // /views/oj/rank/ACMRank.vue
  ACM_Ranklist: 'ACM 排行榜',
  User: '用户',
  Nickname: '昵称',
  Signature: '个性简介',
  Rating: '通过率',
  Rank_Search_Placeholder: '请输入查询的用户名、昵称或真实姓名',

  // /views/oj/rank/OIRank.vue
  OI_Ranklist: 'OI 排行榜',

  // /views/oj/discussion/discussionList.vue
  Go_to_problem: '前往原题',
  Release_Time: '发布时间',
  Likes: '点赞',
  Like_Successfully: '点赞成功',
  Cancel_Like_Successfully: '取消成功',
  Views: '浏览',
  Edit: '编辑',
  Delete: '删除',
  Post_discussion: '发布一个讨论~',
  Post_problem_discussion: '发布题解',
  General_discussion: '公共讨论区',
  Return: '返回',
  Category: '分类',
  Discussion_title: '标题',
  Discussion_Desc: '描述',
  Associated_Problem:'讨论关联的题目ID',
  Associated_Problem_Tips:'讨论关联的题目ID, 例如 P1000',
  Discussion_Category: '分类',
  Discussion_top: '是否置顶',
  Discussion_content: '内容',
  Create_Discussion: '创建',
  Edit_Discussion: '编辑',
  Delete_Discussion_Tips: '此操作将删除讨论，包括相关的评论和回复。你要继续吗？',
  Delete_successfully: '删除成功',
  Post_successfully: '发布成功',
  Send_successfully: '发送成功',
  Can_not_exceed_255:'的长度不能超过255',
  Can_not_exceed_65535:'的长度不能超过65535',
  Can_not_exceed_10000:'的长度不能超过10000',

  // /views/oj/discussion/discussion.vue
  Report: '举报',
  Like: '点赞',
  Liked: '已点赞',
  Report_Reason: '举报原因',
  The_report_label_and_reason_cannot_be_empty: '举报标签和理由不能都为空',

  // 404.vue
  Page_Not_Found:"页面找不到啦",
  Go_Home: '返回主页',
  Back: '返回',

  // /views/oj/contest/ContestList.vue
  Contest_Rule: '赛制',
  Running: '进行中',
  Scheduled: '筹备中',
  Ended: '已结束',
  No_contest: '暂无比赛',
  Contests: '比赛',
  Public: '公开赛',
  Private: '私有赛',
  Protected: '保护赛',
  Public_Tips: '公开赛 - 每个用户都可查看与提交',
  Protected_Tips: '保护赛 - 每个用户都可查看，但是提交需要密码',
  Private_Tips: '私有赛 - 用户需要密码才可查看与提交',
  Contest_Outside_ScoreBoard: '赛外榜单',

  // /views/oj/contest/ContestDetail.vue
  StartAt: '开始时间',
  EndAt: '结束时间',
  Password_Required: '需要密码',
  To_Enter_Need_Password: '请输入该比赛的密码，方可进入比赛',
  Enter_the_contest_password: '请输入比赛密码',
  Enter: '进入',
  Overview: '比赛简介',
  Announcement: '公告',
  Submissions: '提交记录',
  Rankings: '排行榜',
  Comment: '评论',
  Print: '打印',
  Admin_Print: '管理打印',
  Admin_Helper: 'AC助手',
  ScrollBoard: '滚榜',
  Register_contest_successfully: '比赛报名成功',
  Please_check_the_contest_announcement_for_details: '具体内容请查看比赛公告',
  Go_To_Group_Contest_List:'前往班级比赛列表',
  Group_Contest_Tag:'班级比赛',
  Contains_Submission_After_Contest:'包含赛后的提交',

  // /views/oj/contest/children/ACMContestRank.vue
  Contest_Rank: '比赛排名',
  Menu: '菜单',
  Chart: '图表',
  Table: '表格',
  Auto_Refresh: '自动刷新',
  RealName: '真实姓名',
  Force_Update: '强制更新',
  Download_as_CSV: '以CSV格式导出排名',
  TotalTime: '总时间',
  Top_10_Teams: 'Top 10 Teams',
  save_as_image: '保存成图片',
  Contest_Rank_Seq: '排名',
  Star_User: '打星用户',
  Unfollow: '取消关注',
  Top_And_Follow: '置顶关注',
  Contest_Rank_Search_Placeholder:'请输入用户名称 / 学校名称',
  Contest_Rank_Setting:'榜单设置',
  Contest_Setting:'比赛设置',
  Contains_After_Contest:'包含赛后',

  // /views/oj/contest/children/ACMInfo.vue
  AC_Time: 'AC 时间',
  First_Blood: 'First Blood',
  Checked: '已检查',
  Not_Checked: '未检查',
  Check_It: '检查',
  Accepted: 'Accepted',

  // /views/oj/contest/children/ContestPrint.vue
  Print_Title: '比赛文本打印',
  Print_tips: '请将需要打印的文本放入内容框内提交。注意：请不要恶意提交！',
  Content: '内容',
  Content_cannot_be_empty: '内容不能为空',
  The_number_of_content_cannot_be_less_than_50: '内容字符数不能低于50！',
  Success_submit_tips: '提交成功！请耐心等待工作人员打印！',

  // /views/oj/contest/children/ContestAdminPrint.vue
  Download: '下载',
  Printed: '已打印',
  Not_Printed: '未打印',

  // /views/oj/contest/children/ContestRejudgeAdmin.vue
  Contest_Rejudge: '比赛重新测评',
  Rejudge_All: '重测',
  Contest_Rejudge_Tips: '您确定重测所有提交记录?',
  Rejudge_successfully: '重测成功',

  // /views/oj/contest/children/ScrollBoard.vue
  ScrollBoard_Parameter_Config: '滚榜参数配置',
  Start_Rolling:'开始滚榜',
  Contest_ID:'比赛ID',
  Number_of_gold_medals:'金牌数量',
  Number_of_silver_medals:'银牌数量',
  Number_of_bronze_medals:'铜牌数量',
  Formula_for_calculating_the_number_of_medals:'奖牌的计算公式',
  Whether_to_remove_the_star_user: '是否移除打星用户',
  Contest_Non_Ended_But_Want_to_Scroll_Board:'比赛还未结束，你确定是否前往滚榜？',

  // /views/oj/contest/children/OIContestRank.vue
  Total_Score: '总分',
  Based_on_The_Highest_Score_Submitted_For_Each_Problem: '以每题提交的最高得分计算',
  Based_on_The_Recent_Score_Submitted_Of_Each_Problem: '以每题提交的最近得分计算',

  // /views/oj/about/Introduction.vue
  Compiler: '编译器',
  Example: '例题',
  Result_Explanation: '结果说明',
  Pending_Description: '您的解答正在排队等待评测中，请等待结果...',
  Submitted_Faild_Description: '您的此次提交失败，请点击按钮重新提交...',
  Compiling_Description: '正在对您的源代码进行编译中，请等待结果...',
  Judging_Description: '正在使用测试数据运行您的程序中，请等待结果...',
  Compile_Error_Description: "无法编译您的源代码，点击链接查看编译器的输出。",
  Persentation_Error_Description: '您提交的代码已经很接近正确答案，请检查代码格式输出是否有多余空格，换行等空白符。',
  Partial_Accepted_Description:'加油！您提交的代码通过了部分测试点，请考虑其他可能性。',
  Accepted_Description: '恭喜! 您的解题方法是正确的。',
  Wrong_Answer_Description: "您的程序输出结果与判题程序的答案不符。",
  Runtime_Error_Description: '您的程序异常终止，可能的原因是：段错误，被零除或用非0的代码退出程序。',
  Time_Limit_Exceeded_Description: '您的程序运行时间已超出题目限制。',
  Memory_Limit_Exceeded_Description: '您的程序实际使用的内存已超出题目限制。',
  System_Error_Description: '糟糕，判题机系统出了问题。请报告给管理员。',
  Cancelled_Description:'您的此次提交被取消！',
  Compile_Explanation: '编译说明',
  Compile_Tips1:"__int64不是ANSI标准定义，只能在VC使用，在 GNU C++ 中应写成 long long 类型， scanf和printf 请使用%lld作为格式",
  Compile_Tips2:"main() 返回值必须定义为 int ，而不是 void",
  Compile_Tips3:"i 在循环外失去定义 \"for(int i=0...){...}\"",
  Compile_Tips4:"itoa 不是ansi标准函数（标准 C/C++ 中无此函数）",

  // /views/oj/about/Developer.vue
  Leader_BackEnd_FrontEnd_Engineer: '主导 & 后端 | 前端 开发者',
  Group_Function_Development_Contributor:'班级功能开发贡献者',
  Distributed: '分布式',
  Distributed_Desc: '前后端分离，支持判题微服务集群',
  Customization: '定制化',
  Customization_Desc: '网站配置高度集成，支持定制化修改',
  Security: '安全性',
  Security_Desc: '判题沙盒使用cgroup隔离，网站权限控制完善',
  Diversity: '多样性',
  Diversity_Desc: '支持Codefoces，HDU，POJ，GYM，AtCoder，SPOJ的远程判题',
  Available: '有效',
  Faulty: '不完善',

  // /components/oj/common/Announcements.vue
  Contest_Announcement: '比赛公告',
  No_Announcements: '暂无公告',

  // /components/oj/common/CodeMirror.vue
  Lang: '语言',
  Code_Editor_Setting:'代码编辑器设置',
  Setting:'设置',
  Theme: '主题',
  FontSize: '字体大小',
  TabSize:'Tab 长度',
  Two_Spaces:'2个空格',
  Four_Spaces:'4个空格',
  Eight_Spaces:'8个空格',
  Reset_Code: '重置原始代码',
  Upload_file: '上传文件',
  monokai: 'Monokai',
  solarized: 'Molarized Light',
  material: 'Material',
  idea:'IDEA',
  eclipse:'Eclipse',
  base16_dark:'Base16-dark',
  cobalt:'Cobalt',
  dracula:'Dracula',
  Test_Case: '测试用例',
  Test_Result: '运行结果',
  Running_Test: '运行自测',
  Non_Test_Judge_Tips: '输入测试用例后，点击运行自测，这里将会显示运行结果',
  Problem_Uncertain_Answer:'注意：本题可能存在多个符合题目要求的输出，请自行判断程序输出是否通过。',
  Fill_Case: '填充用例',
  Compilation_Failed: '编译失败',
  Test_Input: '自测输入',
  Expected_Output: '预期输出',
  Real_Output: '实际输出',
  Pass_Test_Case: '通过测试用例',
  Get_Recently_Passed_Code:'获取最近通过的代码',
  Enter_Focus_Mode:'进入专注模式',
  Exit_Focus_Mode:'退出专注模式',

  // /components/oj/comment/Comment.vue
  Announcement_of_contest_Q_and_A_area: '比赛评论区公告',
  Announcement_of_contest_Q_and_A_area_tips1: '请不要提问与比赛无关的问题，禁止灌水！',
  Announcement_of_contest_Q_and_A_area_tips2: '比赛过程中，仅自己与比赛管理员的评论可见！',
  Announcement_of_contest_Q_and_A_area_tips3: '比赛管理员评论不可回复，比赛结束评论恢复正常！',
  Come_and_write_down_your_comments: '快来写下你的评论吧',
  Inline_Code: '行内代码',
  Code_Block: '代码块',
  Link: '链接',
  Unordered_list: '无序列表',
  Ordered_List: '有序列表',
  Submit_Comment: '提交评论',
  All_Comment: '全部评论',
  Reply: '回复',
  Reply_Successfully: '回复成功',
  Comment_Successfully: '评论成功',
  Reply_Total: '总共',
  Replies: '条回复',
  Click_Show_All: '点击查看全部',
  Pick_up: '收起',
  Load_More: '加载更多',
  Delete_Comment_Tips: '此操作将删除该评论及其所有回复, 是否继续?',
  Delete_Reply_Tips: '此操作将删除该回复, 是否继续?',
  Reply_Content:'回复内容',
  Comment_Content:'评论内容',

  // /views/oj/message/message.vue
  Message_Center: '消息中心',
  No_Data: '暂无数据',

  // /views/oj/message/UserMsg.vue
  Msg_Total: '共',
  Msg_Messages: '条',
  DiscussMsg: '评论我的',
  ReplyMsg: '回复我的',
  LikeMsg: '收到的赞',
  SysMsg: '系统通知',
  MineMsg: '我的消息',
  Clean_All: '清空全部',
  Action_Like_Discuss: '赞了我的评论',
  Action_Like_Post: '赞了我的讨论帖',
  Action_Discuss: '评论了我的讨论帖',
  Action_Reply: '回复了我的评论',
  From_Discussion_Post: '来自讨论帖',
  From_the_Contest: '来自比赛',
  Delete_Msg_Tips: '你是否确定要删除或清空消息？',

  // /views/oj/training/TrainingList.vue
  Search_Training: '搜索训练',
  Training_Public: '公开训练',
  Training_Private: '私有训练',
  Training_Category: '训练分类',
  Number: '编号',
  Problem_Number: '题目数',
  Recent_Update: '最近更新',
  Progress:'进度',

  // /views/oj/training/TrainingDetails.vue
  Training_Introduction: '训练简介',
  Training_Number: '训练编号',
  Training_Auth: '训练权限',
  Training_Total_Problems: '总题数',
  Record_List: '记录榜单',
  To_Enter_Training_Need_Password: '请输入该私有训练的密码，方可进入',
  Enter_the_training_password: '请输入私有训练的密码',
  Register_training_successfully: '验证训练密码成功！',

  // /views/oj/training/TrainingRank.vue
  Total_AC: 'AC总数',
  Training_Rank_Search_Placeholder:'请输入查询的用户名、真实姓名或学校',

  // /views/oj/group/GroupList.vue
  Search_Group: '搜索班级',
  Create_Group: '创建班级',
  All_Group: '所有班级',
  My_Group: '我的班级',
  No_Groups: '暂无班级',
  Group_Name: '班级名称',
  Group_Short_Name: '班级简称（班级题目展示ID前缀）',
  Group_Brief: '班级简介',
  Group_Description: '班级描述',
  Group_Public: '公开班级',
  Group_Protected: '保护班级',
  Group_Private: '私有班级',
  Group_Hidden: '隐藏',
  Group_Auth: '班级权限',
  Group_Owner: '班级负责人',
  Group_Code: '班级代码',
  Join_Group_By_Code: "根据班级代码，加入班级",
  Enter_Group_Code: "请输入班级代码",
  Join_Group_Success: '班级加入成功',
  Join_Group_Failed: '班级加入失败',
  Join_Group: "加入班级",
  Total_Members: '成员总数',
  Group_Public_Tips: '公开班级 - 加入无需申请',
  Group_Protected_Tips: '保护班级 - 加入需要申请',
  Group_Private_Tips: '私有班级 - 需要邀请码才能申请',
  Group_Hidden_Tips: '隐藏 - 只有班级成员可见',
  Create_Successfully: '创建成功',
  Group_Name_Check_Required: '班级名称不能为空',
  Group_Name_Check_Min_Max: '班级名称的长度应为 5 到 25',
  Group_Short_Name_Check_Required: '班级简称不能为空',
  Group_Short_Name_Check_Min_Max: '班级简称的长度应为 5 到 10',
  Group_Brief_Check_Required: '班级简介不能为空',
  Group_Brief_Check_Min_Max: '班级简介的长度应为 5 到 50',
  Group_Auth_Check_Required: '班级权限不能为空',
  Group_Code_Check_Required: '班级邀请码不能为空',
  Group_Code_Check_Min_Max: '班级邀请码的长度应为 6',
  Group_Description_Check_Required: '班级描述不能为空',
  Group_Description_Check_Min_Max: '班级描述的长度应为 5 到 1000',

  // /views/oj/group/GroupDetails.vue
  Apply_Group: '申请加入',
  Apply_Successfully: '申请成功',
  Exit_Group: '退出班级',
  Exit_Group_Tips: '确定要退出班级吗？退出后您将无法再查看班级资源！',
  Exit_Successfully: '退出成功',
  Disband_Group:'解散班级',
  Disband_Group_Tips:'您是否确定要解散该班级？',
  Disband_Successfully:'解散成功',

  Group_Number: '班级编号',
  Group_Home: '主页',
  Group_Problem: '题目',
  Group_Training: '训练',
  Group_Contest: '比赛',
  Group_Submission: '评测',
  Group_Discussion: '讨论',
  Group_Setting: '设置',
  Group_Announcement: '公告',
  Code_Monitor: '代码监控',
  Group_Rank: '排名',
  Group_Member: '成员',
  Apply_Reason: '申请理由',
  Apply_Reason_Check_Required: '申请理由不能为空',
  Apply_Reason_Check_Min_Max: '申请理由的长度应为 5 到 100',

  // /views/oj/group/GroupDiscussionList.vue
  No_Discussion: '暂无讨论',
  Problem_Discussion: '题目讨论',

  // /views/oj/group/GroupProblemList.vue
  Cancel_Admin:'取消管理',
  Back_To_Problem_List:'返回题目列表',
  Back_To_Admin_Problem_List:'返回题目管理列表',

  // /views/oj/group/GroupMemberList.vue
  Applying: '申请中',
  Refused: '已拒绝',
  General_Member: '普通成员',
  Member_Admin: '成员管理',
  Join_Time: '加入时间',
  Change_Time: '变更时间',
  Member_Auth: '成员权限',
  Group_Admin: '管理员',
  Group_Root: '超级管理员',
  View_Reason: '查看理由',
  Delete_Member: '踢出该成员',
  Delete_Member_Tips: '确定要踢出该成员吗？踢出后该成员将无法再查看班级资源！另外，如果该成员是班级负责人，那么班级也将被一同解散！',

  // /views/oj/group/GroupTrainingList.vue
  Back_To_Admin_Training_List:'返回训练管理列表',
  Back_To_Training_List:'返回训练列表',
  Back_Admin_Training_Problem_List:'返回训练题目管理列表',

  // /views/oj/group/GroupContestList.vue
  Back_To_Admin_Contest_List:'返回比赛管理列表',
  Back_To_Contest_List:'返回比赛列表',
  Back_Admin_Contest_Problem_List:'返回比赛题目管理列表',

  // /views/oj/group/GroupSetting.vue
  Add_From_Group_Problem: '从班级题库添加题目',

  // /views/oj/group/GroupSetting.vue
  Group_Visible: '在班级列表显示',
  Group_Not_Visible: '在班级列表隐藏',

  // /views/oj/group/GroupRank.vue
  Group_ACM_Rank_Type: 'ACM排序',
  Group_OI_Rank_Type: 'OI排序',

  // GroupCourse.vue
  Group_Course: '班级课程',

  // GroupAdminProblemList.vue
  Group_Problem_Apply_Public:'申请加入公开题库',
  Already_Public_Problem:'已在公开题库中',
  Applying_for_Publicity:'申请公开中',
  Group_Problem_Apply_Public_Tips:'您是否确定要申请该题目加入公开题库？',
  Cancel_Group_Problem_Apply_Public_Tips:'您是否要取消申请该题目加入公开题库？',

  // 作答内容相关
  Answer_Content: '作答内容',
  View_answer_details: '查看作答详情',
  Answer_Details: '作答详情',
  Option_Key: '选择答案',
  Option_Content: '选项内容',
  Is_Correct: '是否正确',
  Single_Choice: '单选题',
  Multiple_Choice: '多选题',
  Fill_Blank: '填空题',
  Short_Answer: '简答题',
  Correct_Answer: '正确答案',
  Wrong_Answer: '错误答案',
  View_Details: '查看详情',
  Problem_Type: '题目类型',
  Selected_Answer: '已选答案',
  Display_Answer: '显示答案'
}
