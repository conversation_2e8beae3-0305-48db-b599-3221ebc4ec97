<template>
  <el-row class="flex-container">
    <div id="main">
      <el-card shadow>
        <div slot="header">
          <el-row :gutter="18">
            <el-col :lg="2" :md="4">
              <span class="panel-title hidden-md-and-down">{{ $t('m.Status') }}</span>
            </el-col>
            <el-col :lg="4" :md="4" :sm="8" :xs="10">
              <el-switch v-model="formFilter.onlyMine" :active-text="$t('m.Mine')" :inactive-text="$t('m.All')" :width="40" style="display: block" @change="handleOnlyMine">
              </el-switch>
            </el-col>

            <el-col :lg="4" :md="5" :sm="8" :xs="10" style="padding-top: 5px;">
              <el-dropdown class="drop-menu" placement="bottom" trigger="hover" @command="handleStatusChange">
                <span class="el-dropdown-link">{{ status }}<i class="el-icon-caret-bottom"></i></span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="All">{{ $t('m.All') }}</el-dropdown-item>
                  <el-dropdown-item v-for="result in Object.keys(JUDGE_STATUS_LIST)" :key="result" :command="result">
                    {{ JUDGE_STATUS_LIST[result].name }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-col>

            <el-col :lg="5" :md="5" :sm="12" :xs="24" class="search">
              <vxe-input v-model="formFilter.problemID" :placeholder="$t('m.Enter_Problem_ID')" size="medium" type="search" @keyup.enter.native="handleQueryChange('probemID')"
                         @search-click="handleQueryChange('probemID')"></vxe-input>
            </el-col>
            <el-col :lg="5" :md="5" :sm="12" :xs="24" class="search">
              <vxe-input v-model="formFilter.username" :disabled="formFilter.onlyMine" :placeholder="$t('m.Enter_Author')" size="medium" type="search"
                         @keyup.enter.native="handleQueryChange('username')" @search-click="handleQueryChange('username')"></vxe-input>
            </el-col>
            <el-col :lg="4" :md="5" :sm="8" class="hidden-xs-only">
              <el-button icon="el-icon-search" round size="small" type="primary" @click="getSubmissions">{{ $t('m.Search') }}</el-button>
            </el-col>
            <el-col :xs="4" class="hidden-sm-and-up">
              <el-button circle icon="el-icon-search" size="small" type="primary" @click="getSubmissions"></el-button>
            </el-col>
          </el-row>
        </div>
        <vxe-table ref="xTable" :data="submissions" :loading="loadingTable" :row-class-name="tableRowClassName" align="center" auto-resize border="inner" highlight-current-row highlight-hover-row
                   keep-source
                   stripe>
          <vxe-table-column :title="$t('m.Run_ID')" field="submitId" width="80"></vxe-table-column>
          <vxe-table-column :title="$t('m.Problem')" field="pid" min-width="150" show-overflow>
            <template v-slot="{ row }">
              <span v-if="contestID" style="color: rgb(87, 163, 243)" @click="getProblemUri(row.displayId)">{{ row.displayId + ' ' + row.title }}</span>
              <span v-else style="color: rgb(87, 163, 243)" @click="getProblemUri(row.displayPid)">{{ row.displayPid + ' ' + row.title }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.Status')" field="status" min-width="200">
            <template v-slot="{ row }">
              <el-tooltip :content="$t('m.Click_to_Manually_Judge')" :disabled="hideManuallyJugdeTooltip || disabledManualJudge(row.status)" effect="dark" placement="top">
                <el-popover :disabled="disabledManualJudge(row.status)" placement="top" trigger="click" width="230" @hide="hideManuallyJugdeTooltip = false" @show="hideManuallyJugdeTooltip = true">
                  <div class="manual-judge-title">
                    <span>{{ $t('m.Manually_Jugde') }}</span>
                  </div>
                  <div>
                    <span>
                      <el-button :disabled="row.status == JUDGE_STATUS_RESERVE['Pending']" icon="el-icon-edit" size="mini" type="warning"
                                 @click="openChangeJudgeStatusDialog(row)">{{ $t('m.Modify_Evaluation') }}</el-button>
                      <el-button :disabled="row.status == JUDGE_STATUS_RESERVE['ca']" icon="el-icon-close" size="mini" type="danger" @click="cancelJudge(row)">{{ $t('m.Cancel_Evaluation')
                        }}</el-button>
                    </span>
                  </div>
                  <span slot="reference" :class="getStatusColor(row.status)">
                    <i v-if="
                    row.status == JUDGE_STATUS_RESERVE['Pending'] ||
                      row.status == JUDGE_STATUS_RESERVE['Compiling'] ||
                      row.status == JUDGE_STATUS_RESERVE['Judging']
                  " class="el-icon-loading"></i>
                    <i v-if="row.status == JUDGE_STATUS_RESERVE['sf']" class="el-icon-refresh" @click="reSubmit(row)"></i>
                    {{ JUDGE_STATUS[row.status].name }}
                  </span>
                </el-popover>
              </el-tooltip>
              <el-tooltip v-if="row.isManual" :content="$t('m.Has_Been_Manually_Judged')" class="item" effect="dark" placement="top">
                <svg class="icon" height="16" p-id="9872" style="vertical-align: middle;margin-left: 4px;" t="1660976601239" version="1.1" viewBox="0 0 1024 1024" width="16"
                     xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M706.1 489.4c24.3-36.7 38.6-80.6 38.6-128 0-128.1-103.9-232-232-232s-232 103.9-232 232c0 47.3 14.2 91.3 38.6 128C168.9 561.5 65 715.2
                  65 893.2h895.3c0.1-178-103.8-331.7-254.2-403.8z"
                    fill="#D6F3FA"
                    p-id="9873"
                  >
                  </path>
                  <path
                    d="M587 800.7l86.3 61.1-72.6 33.4c-10.5 4.8-22.2-3.5-21.1-15l7.4-79.5z m274.9-205L685.4 844.8l-86.3-61.1 176.5-249.1 86.3 61.1zM825 612.3c3.1-4.4
                  2.1-10.5-2.3-13.6-4.4-3.1-10.5-2.1-13.6 2.3l-132 186.2c-3.1 4.4-2.1 10.5 2.3 13.6 4.4 3.1 10.5 2.1 13.6-2.3l132-186.2z m41.5-23.1c6.1-8.6 4-20.6-4.6-26.7l-55-39c-8.6-6.1-20.6-4-26.7
                  4.6l-1.6 2.2 86.3 61.1 1.6-2.2z m-75.4 200.1c0 10.4 8.5 19 19 19h78c10.4 0 19-8.5 19-19 0-10.4-8.5-19-19-19h-78c-10.5 0-19 8.5-19 19z m97 63.5H766.6c-10.4 0-19 8.5-19 19 0 10.4 8.5
                  19 19 19h121.5c10.4 0 19-8.5 19-19 0-10.4-8.6-19-19-19z m-231-275.2c-44.9-21-94.5-32.1-144.5-32.1-113.5 0-220.2 57.4-283.1 151.1-6.3 9.4-19 12-28.8 6.3-10.8-6.4-14-20.6-7-31 53.6-79.5
                  135.2-135.7 227-158.5-51.8-31.3-86.6-87.9-86.6-152.7 0-98.4 80.1-178.5 178.5-178.5s178.5 80.1 178.5 178.5c0 64.7-34.8 121.4-86.5 152.7 24.2 5.9 47.8 14.1 70.3 24.6 11.5 5.3 16.2 19.2
                  10.2 30.3-5.4 9.9-17.7 14.1-28 9.3z m-144.4-76.4c77.5 0 140.5-63 140.5-140.5s-63-140.5-140.5-140.5-140.5 63-140.5 140.5 63 140.5 140.5 140.5zM196.2 711c-11-6.1-24.9-1.7-30.3 9.7-3 6.2-5.8
                  12.5-8.4 18.8-4.8 11.6 1.2 24.9 13 29 10.8 3.7 22.7-1.6 27-12.2 2.4-5.8 4.9-11.5 7.6-17.1 5.1-10.3 1.1-22.7-8.9-28.2z m-28 64.5c-12.1-3.9-24.8 3.4-27.9 15.7-6 23.5-9.7 47.4-11.2 71.7-0.8 12.6
                  9.1 23.4 21.7 23.4 11.4 0 21-8.7 21.6-20.1 1.3-22 4.7-43.8 10.1-65.1 3-10.9-3.5-22.1-14.3-25.6z"
                    fill="#18BAE5"
                    p-id="9874"
                  >
                  </path>
                </svg>
              </el-tooltip>
            </template>
          </vxe-table-column>
          <vxe-table-column
            v-if="scoreColumnVisible"
            :title="$t('m.Score')"
            field="score"
            width="64"
          >
            <template v-slot="{ row }">
              <template v-if="contestID && row.score != null">
                <el-tag
                  :type="JUDGE_STATUS[row.status]['type']"
                  effect="plain"
                  size="medium"
                >{{ row.score }}
                </el-tag>
              </template>
              <template v-else-if="row.score != null">
                <el-tooltip placement="top">
                  <div slot="content">
                    {{ $t('m.Problem_Score') }}：{{
                      row.score != null ? row.score : $t('m.Unknown')
                    }}<br />{{ $t('m.OI_Rank_Score') }}：{{
                      row.oiRankScore != null
                        ? row.oiRankScore
                        : $t('m.Unknown')
                    }}<br />
                    {{
                      $t('m.OI_Rank_Calculation_Rule')
                    }}：(score*0.1+difficulty*2)
                  </div>
                  <el-tag
                    :type="JUDGE_STATUS[row.status]['type']"
                    effect="plain"
                    size="medium"
                  >{{ row.score }}
                  </el-tag>
                </el-tooltip>
              </template>
              <template v-else-if="
                  row.status == JUDGE_STATUS_RESERVE['Pending'] ||
                    row.status == JUDGE_STATUS_RESERVE['Compiling'] ||
                    row.status == JUDGE_STATUS_RESERVE['Judging']
                ">
                <el-tag
                  :type="JUDGE_STATUS[row.status]['type']"
                  effect="plain"
                  size="medium"
                >
                  <i class="el-icon-loading"></i>
                </el-tag>
              </template>
              <template v-else>
                <el-tag
                  :type="JUDGE_STATUS[row.status]['type']"
                  effect="plain"
                  size="medium"
                >--
                </el-tag>
              </template>
            </template>
          </vxe-table-column>
          <vxe-table-column :title="'作答内容'" field="replyOptions" min-width="120">
            <template v-slot="{ row }">
              <template v-if="row.replyOptions && row.replyOptions.length > 0">
                <el-tooltip :content="$t('m.View_answer_details')" class="item" effect="dark" placement="top">
                  <el-button type="text" @click="showReplyDetails(row)">
                    {{ getShortReplyContent(row.replyOptions) }}
                  </el-button>
                </el-tooltip>
              </template>
              <template v-else>
                <span>--</span>
              </template>
            </template>
          </vxe-table-column>
          <vxe-table-column :title="'流程图'" field="flowImage" width="64">
            <template v-slot="{ row }">
              <el-image v-if="row.flowImage" :preview-src-list="[row.flowImage]" :src="row.flowImage" fit="scale-down" style="cursor: pointer;">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.Time')" field="time" min-width="96">
            <template v-slot="{ row }">
              <span>{{ submissionTimeFormat(row.time) }}</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.Memory')" field="memory" min-width="96">
            <template v-slot="{ row }">
              <span>{{ submissionMemoryFormat(row.memory) }}</span>
            </template>
          </vxe-table-column>

          <vxe-table-column :title="$t('m.Length')" field="length" min-width="80">
            <template v-slot="{ row }">
              <span>{{ submissionLengthFormat(row.length) }}</span>
            </template>
          </vxe-table-column>

          <vxe-table-column :title="$t('m.Language')" field="language" min-width="96" show-overflow>
            <template v-slot="{ row }">
              <el-tooltip :content="$t('m.View_submission_details')" class="item" effect="dark" placement="top">
                <span
                  style="color: rgb(87, 163, 243); font-size: .8125rem;"
                  @click="showSubmitDetail(row)"
                >{{ row.language }}
                </span>
              </el-tooltip>
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.Judger')" field="judger" min-width="96" show-overflow>
            <template v-slot="{ row }">
              <span v-if="row.judger">{{ row.judger }}</span>
              <span v-else>--</span>
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.Author')" field="username" min-width="96" show-overflow>
            <template v-slot="{ row }">
              <a style="color: rgb(87, 163, 243)" @click="goUserHome(row.username, row.uid)">{{ row.username }}</a>
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.Author')" field="realname" min-width="96" show-overflow>
            <template v-slot="{ row }">
              <a style="color: rgb(87, 163, 243)" @click="goUserHome(row.realname, row.uid)">{{ row.realname }}</a>
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.Submit_Time')" field="submitTime" min-width="96">
            <template v-slot="{ row }">
              <span>
                <el-tooltip :content="row.submitTime | localtime" placement="top">
                  <span>{{ row.submitTime | fromNow }}</span>
                </el-tooltip>
              </span>
            </template>
          </vxe-table-column>
          <!-- 非比赛提交记录，超级管理员可以对提交进行重判 -->
          <vxe-table-column v-if="rejudgeColumnVisible" :title="$t('m.Option')" min-width="90">
            <template v-slot="{ row }">
              <vxe-button :loading="row.loading" size="mini" status="primary" @click="handleRejudge(row)">{{ $t('m.Rejudge') }}</vxe-button>
            </template>
          </vxe-table-column>
        </vxe-table>
      </el-card>
      <Pagination :current.sync="currentPage" :layout="'prev, pager, next, sizes'" :page-size="limit" :total="total" @on-change="changeRoute" @on-page-size-change="onPageSizeChange"></Pagination>
    </div>
    <el-dialog :close-on-click-modal="false" :title="$t('m.Manually_Jugde')+'(Run ID：'+changeJudgeStatus.submitId+')'" :visible.sync="changeJudgeStatusDialogVisible" center width="350px">
      <el-form>
        <el-form-item :label="$t('m.Status')" required>
          <el-select v-model="changeJudgeStatus.status" size="small">
            <el-option v-for="result in Object.keys(CHANGE_JUDGE_STATUS_LIST)" :key="result" :label="JUDGE_STATUS[result].name" :value="parseInt(result)"></el-option>
            <el-option :value="-4" disabled label="Cancelled"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="changeJudgeStatus.score != null || changeJudgeStatus.status == JUDGE_STATUS_RESERVE['ca']" :label="$t('m.Score')" required>
          <el-input-number v-model="changeJudgeStatus.score" size="small"></el-input-number>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button :disabled="changeJudgeStatus.status == JUDGE_STATUS_RESERVE['ca']" :loading="changeJudgeStatusLoading" type="primary" @click="manualJudge">{{ $t('m.To_Update') }}
        </el-button>
      </span>
    </el-dialog>
    
    <!-- 作答内容详情弹窗 -->
    <el-dialog
      :title="$t('m.Answer_Details') + (currentSubmission ? ' (Run ID：' + currentSubmission.submitId + ')' : '')"
      :visible.sync="replyDetailsVisible"
      width="650px"
      center
      custom-class="apple-style-dialog"
    >
      <div v-if="currentReplyOptions && currentReplyOptions.length > 0" class="answer-content-container">
        <el-card v-for="(item, index) in currentReplyOptions" :key="index" class="reply-card apple-card" shadow="hover">
          <div slot="header" class="reply-header">
            <span class="problem-id">{{ item.problemId }}</span>
            <span class="problem-type">{{ getProblemTypeText(item.problemType) }}</span>
          </div>
          <div class="reply-content">
            <div v-if="item.problemType === 'SingleChoice' || item.problemType === 'MultipleChoice'" class="choice-content">
              <div class="answer-row">
                <span class="answer-label">{{ $t('m.Selected_Answer') }}</span>
                <span class="answer-value option-key">{{ item.optionKey }}</span>
              </div>
              <div class="answer-row">
                <span class="answer-label">{{ $t('m.Option_Content') }}</span>
                <span class="answer-value">{{ item.optionContent }}</span>
              </div>
              <div class="answer-row">
                <span class="answer-label">{{ $t('m.Is_Correct') }}</span>
                <span class="answer-value">
                  <i 
                    :class="item.answer ? 'el-icon-check' : 'el-icon-close'" 
                    :style="{
                      'color': item.answer ? '#34C759' : '#FF3B30',
                      'font-size': '16px',
                      'background': item.answer ? 'rgba(52, 199, 89, 0.1)' : 'rgba(255, 59, 48, 0.1)',
                      'border-radius': '50%',
                      'padding': '6px',
                      'display': 'inline-flex',
                      'align-items': 'center',
                      'justify-content': 'center'
                    }"
                  ></i>
                </span>
              </div>
            </div>
            <div v-else class="text-content">
              <div class="answer-row">
                <span class="answer-label">{{ $t('m.Answer_Content') }}</span>
              </div>
              <pre class="answer-text">{{ item.optionContent }}</pre>
            </div>
          </div>
        </el-card>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="replyDetailsVisible = false">{{ $t('m.Close') }}</el-button>
        <el-button type="primary" @click="goToSubmissionDetails">{{ $t('m.View_Details') }}</el-button>
      </span>
    </el-dialog>
  </el-row>
</template>

<script>
import { SubmitListType } from '@/common/submitListType';
import { mapActions, mapGetters } from 'vuex';
import api from '@/common/api';
import { CONTEST_STATUS, JUDGE_STATUS, JUDGE_STATUS_RESERVE, RULE_TYPE } from '@/common/constants';
import utils from '@/common/utils';
import Pagination from '@/components/oj/common/Pagination';
import myMessage from '@/common/message';
import 'element-ui/lib/theme-chalk/display.css';

export default {
  name: 'SubmitList',
  components: {
    Pagination,
  },
  props: {
    uid: {
      type: String,
      default: null,
    },
    type: {
      type: String,
      default: SubmitListType.All,
    },
  },
  data() {
    return {
      formFilter: {
        onlyMine: false,
        status: '',
        username: '',
        problemID: '',
      },
      loadingTable: false,
      submissions: [],
      needCheckSubmitIds: {}, // 当前状态为6和7的提交记录Id 需要重新检查更新
      total: 30,
      limit: 15,
      currentPage: 1,
      contestID: null,
      groupID: null,
      routeName: '',
      checkStatusNum: 0,
      JUDGE_STATUS: '',
      JUDGE_STATUS_LIST: '',
      CHANGE_JUDGE_STATUS_LIST: '',
      autoCheckOpen: false,
      JUDGE_STATUS_RESERVE: {},
      CONTEST_STATUS: {},
      RULE_TYPE: {},
      hideManuallyJugdeTooltip: false,
      changeJudgeStatusDialogVisible: false,
      changeJudgeStatusLoading: false,
      changeJudgeStatus: {
        submitId: null,
        status: null,
        score: null,
      },
      replyDetailsVisible: false,
      currentReplyOptions: [],
      currentSubmission: {
        submitId: ''
      },
    };
  },
  created() {
    this.init();
  },
  mounted() {
    this.JUDGE_STATUS = Object.assign({}, JUDGE_STATUS);
    this.JUDGE_STATUS_LIST = Object.assign({}, JUDGE_STATUS);
    this.JUDGE_STATUS_RESERVE = Object.assign({}, JUDGE_STATUS_RESERVE);
    this.CONTEST_STATUS = Object.assign({}, CONTEST_STATUS);
    this.RULE_TYPE = Object.assign({}, RULE_TYPE);
    // 去除下拉框选择中的Not Submitted,Submitted Unknown Result 三种状态
    delete this.JUDGE_STATUS_LIST['9'];
    delete this.JUDGE_STATUS_LIST['-5'];
    delete this.JUDGE_STATUS_LIST['-10'];

    // 再次去除Cancelled,Compile Error,Compiling,Judging,Submitting
    this.CHANGE_JUDGE_STATUS_LIST = Object.assign({}, this.JUDGE_STATUS_LIST);
    delete this.CHANGE_JUDGE_STATUS_LIST['-4'];
    delete this.CHANGE_JUDGE_STATUS_LIST['-2'];
    delete this.CHANGE_JUDGE_STATUS_LIST['5'];
    delete this.CHANGE_JUDGE_STATUS_LIST['6'];
    delete this.CHANGE_JUDGE_STATUS_LIST['7'];
    this.getData();
  },
  methods: {
    init() {
      this.checkStatusNum = 0;
      this.contestID = this.$route.params.contestID;
      this.groupID = this.$route.params.groupID;
      let query = this.$route.query;
      this.formFilter.problemID = query.problemID;
      this.formFilter.username = query.username || '';
      this.formFilter.onlyMine = query.onlyMine + '' == 'true' ? true : false; // 统一换成字符串判断
      this.formFilter.status = query.status;
      this.formFilter.completeProblemID = query.completeProblemID || false;
      if (this.formFilter.onlyMine) {
        // 当前为搜索自己的提交 那么不可搜索别人的提交
        this.formFilter.username = '';
      }
      this.currentPage = parseInt(query.currentPage) || 1;
      if (this.currentPage < 1) {
        this.currentPage = 1;
      }
      this.limit = parseInt(query.limit) || 15;
      this.routeName = this.$route.name;
    },

    getData() {
      this.getSubmissions();
    },

    buildQuery() {

      let params = {
        onlyMine: this.formFilter.onlyMine,
        status: this.formFilter.status,
        username: this.formFilter.username,
        problemID: this.formFilter.problemID,
        currentPage: this.currentPage,
        type: this.type,
        limit: this.limit,
        completeProblemID: this.formFilter.completeProblemID,
        gid: this.groupID,
      };

      switch (this.type) {
        case SubmitListType.All:
          break;
        case SubmitListType.Group:
          params.gid = this.groupID;
          break;
        case SubmitListType.User:
          params.uid = this.uid;
          break;
      }
      return params;
    },

    submissionTimeFormat(time) {
      return utils.submissionTimeFormat(time);
    },

    submissionMemoryFormat(memory) {
      return utils.submissionMemoryFormat(memory);
    },

    submissionLengthFormat(length) {
      return utils.submissionLengthFormat(length);
    },
    reSubmit(row) {
      api.reSubmitRemoteJudge(row.submitId).then((res) => {
        let xTable = this.$refs.xTable;
        // 重新提交开始，需要将该提交的部分参数初始化
        row.status = res.data.data.status;
        row.time = res.data.data.time;
        row.memory = res.data.data.memory;
        row.errorMessage = res.data.data.errorMessage;
        row.judger = res.data.data.judger;
        row.isManual = false;
        // 重新加载该行数据到view
        xTable.reloadRow(row, null, null);

        this.submissions[row.index] = res.data.data;
        myMessage.success(this.$i18n.t('m.Resubmitted_Successfully'));

        // 加入待重判列表
        this.needCheckSubmitIds[row.submitId] = row.index;

        this.checkStatusNum = 0;
        if (!this.autoCheckOpen) {
          // 如果当前未开启自动检查提交状态的定时任务，则开启
          this.checkSubmissionsStatus();
        }
      });
    },
    getSubmissions() {
      let params = this.buildQuery();
      params.contestID = this.contestID;

      if (this.contestID) {
        if (this.contestStatus == CONTEST_STATUS.SCHEDULED) {
          params.beforeContestSubmit = true;
        } else {
          params.beforeContestSubmit = false;
        }
        params.containsEnd = true;
      }
      if (this.formFilter.onlyMine) {
        // 需要判断是否为登陆状态
        if (this.isAuthenticated) {
          params.username = ''; // 如果是搜索当前用户的提交记录，那么用户名搜索应该无效
          this.formFilter.username = '';
        } else {
          this.formFilter.onlyMine = false;
          myMessage.error(this.$i18n.t('m.Please_login_first'));
          return;
        }
      }

      this.loadingTable = true;
      this.submissions = [];
      this.needCheckSubmitIds = {};
      let func = this.contestID
        ? 'getContestSubmissionList'
        : 'getSubmissionList';
      api[func](this.limit, utils.filterEmptyValue(params))
        .then((res) => {
          let data = res.data.data;
          let index = 0;
          for (let v of data.records) {
            if (
              v.status == JUDGE_STATUS_RESERVE['Pending'] ||
              v.status == JUDGE_STATUS_RESERVE['Compiling'] ||
              v.status == JUDGE_STATUS_RESERVE['Judging']
            ) {
              this.needCheckSubmitIds[v.submitId] = index;
            }
            v.loading = false;
            v.index = index;
            
            // 处理replyOptions字段，确保它是一个数组
            if (v.replyOptions) {
              try {
                // 如果replyOptions是字符串，尝试解析为JSON对象
                if (typeof v.replyOptions === 'string' && v.replyOptions.trim() !== '') {
                  v.replyOptions = JSON.parse(v.replyOptions);
                }
                // 如果不是数组，则转换为数组
                if (!Array.isArray(v.replyOptions)) {
                  v.replyOptions = [v.replyOptions];
                }
              } catch (e) {
                console.error('Parse replyOptions error:', e);
                v.replyOptions = [];
              }
            } else {
              v.replyOptions = [];
            }
            
            index += 1;
          }
          this.loadingTable = false;
          this.submissions = data.records;
          
          // 调试输出
          console.log('提交列表:', this.submissions.map(s => ({
            submitId: s.submitId,
            hasReplyOptions: s.replyOptions && s.replyOptions.length > 0,
            replyOptionsLength: s.replyOptions ? s.replyOptions.length : 0
          })));
          
          this.total = data.total;
          if (Object.keys(this.needCheckSubmitIds).length > 0) {
            this.checkSubmissionsStatus();
          }
        })
        .catch(() => {
          this.loadingTable = false;
        });
    },
    // 对当前提交列表 状态为Pending（6）和Judging（7）的提交记录每2秒查询一下最新结果
    checkSubmissionsStatus() {
      // 使用setTimeout避免一些问题
      if (this.refreshStatus) {
        // 如果之前的提交状态检查还没有停止,则停止,否则将会失去timeout的引用造成无限请求
        clearTimeout(this.refreshStatus);
        this.autoCheckOpen = false;
      }
      const checkStatus = () => {
        let submitIds = this.needCheckSubmitIds;
        let func = this.contestID
          ? 'checkContestSubmissonsStatus'
          : 'checkSubmissonsStatus';
        api[func](Object.keys(submitIds), this.contestID).then(
          (res) => {
            let result = res.data.data;
            if (!this.$refs.xTable) {
              // 避免请求一半退出view保错
              return;
            }
            let viewData = this.$refs.xTable.getTableData().tableData;
            for (let key in submitIds) {
              let submitId = parseInt(key);
              if (!result[submitId]) {
                continue;
              }
              // 更新数据列表
              this.submissions[submitIds[key]] = result[submitId];
              // 更新view中的结果，f分数，耗时，空间消耗，判题机ip
              viewData[submitIds[key]].status = result[submitId].status;
              viewData[submitIds[key]].score = result[submitId].score;
              viewData[submitIds[key]].time = result[submitId].time;
              viewData[submitIds[key]].memory = result[submitId].memory;
              viewData[submitIds[key]].judger = result[submitId].judger;
              viewData[submitIds[key]].isManual = result[submitId].isManual;
              // 重新加载这行数据到view中
              this.$refs.xTable.reloadRow(viewData[submitIds[key]], null, null);

              if (
                result[submitId].status != JUDGE_STATUS_RESERVE['Pending'] &&
                result[submitId].status != JUDGE_STATUS_RESERVE['Compiling'] &&
                result[submitId].status != JUDGE_STATUS_RESERVE['Judging']
              ) {
                delete this.needCheckSubmitIds[key];
              }
            }
            // 当前提交列表的提交都判题结束或者检查结果600s（2s*300）还没判题结束，为了避免无用请求加重服务器负担，直接停止检查的请求。
            if (
              Object.keys(this.needCheckSubmitIds).length == 0 ||
              this.checkStatusNum == 300
            ) {
              clearTimeout(this.refreshStatus);
              this.autoCheckOpen = false;
            } else {
              this.checkStatusNum += 1;
              this.refreshStatus = setTimeout(checkStatus, 2000);
            }
          },
          (res) => {
            clearTimeout(this.refreshStatus);
            this.autoCheckOpen = false;
          },
        );
      };
      // 设置每2秒检查一下提交结果
      this.checkStatusNum += 1;
      this.refreshStatus = setTimeout(checkStatus, 2000);
      this.autoCheckOpen = true;
    },
    onPageSizeChange(pageSize) {
      this.limit = pageSize;
      this.changeRoute();
    },
    // 改变route， 通过监听route变化请求数据，这样可以产生route history， 用户返回时就会保存之前的状态
    changeRoute() {
      let query = this.buildQuery();
      query.contestID = this.contestID;
      let queryParams = utils.filterEmptyValue(query);
      this.getSubmissions();
    },

    goUserHome(username, uid) {
      this.$router.push({
        path: '/organization-home',
        query: { uid, username },
      });
    },
    handleStatusChange(status) {
      if (status == 'All') {
        this.formFilter.status = '';
      } else {
        this.formFilter.status = status;
      }
      this.currentPage = 1;
      this.changeRoute();
    },
    handleQueryChange(searchParam) {
      if (searchParam == 'probemID') {
        this.formFilter.completeProblemID = false; // 并非走完全检索displayID了
      }
      this.currentPage = 1;
      this.changeRoute();
    },
    handleRejudge(row) {
      this.submissions[row.index].loading = true;
      api.submissionRejudge(row.submitId).then(
        (res) => {
          let xTable = this.$refs.xTable;
          // 重判开始，需要将该提交的部分参数初始化
          row.status = res.data.data.status;
          row.score = null;
          row.time = res.data.data.time;
          row.memory = res.data.data.memory;
          row.errorMessage = res.data.data.errorMessage;
          row.judger = res.data.data.judger;
          row.loading = false;
          row.isManual = false;
          // 重新加载该行数据到view
          xTable.reloadRow(row, null, null);

          this.submissions[row.index] = res.data.data;
          this.submissions[row.index].loading = false;
          myMessage.success(this.$i18n.t('m.Rejudge_successfully'));

          // 加入待重判列表
          this.needCheckSubmitIds[row.submitId] = row.index;
          this.checkStatusNum = 0;
          if (!this.autoCheckOpen) {
            // 如果当前未开启自动检查提交状态的定时任务，则开启
            this.checkSubmissionsStatus();
          }
        },
        () => {
          this.submissions[row.index].loading = false;
        },
      );
    },
    handleOnlyMine() {
      if (this.formFilter.onlyMine) {
        // 需要判断是否为登陆状态
        if (this.isAuthenticated) {
          this.formFilter.username = '';
        } else {
          this.formFilter.onlyMine = false;
          myMessage.error(this.$i18n.t('m.Please_login_first'));
          return;
        }
      }
      this.currentPage = 1;
      this.changeRoute();
    },
    ...mapActions(['changeModalStatus']),

    showSubmitDetail(row) {
      if (this.contestID != null) {
        // 比赛提交详情
        this.$router.push({
          name: 'ContestSubmissionDetails',
          params: {
            contestID: this.contestID,
            problemID: row.displayId,
            submitID: row.submitId,
          },
        });
      } else if (this.groupID != null) {
        this.$router.push({
          name: 'GroupSubmissionDetails',
          params: { submitID: row.submitId },
        });
      } else {
        this.$router.push({
          name: 'SubmissionDetails',
          params: { submitID: row.submitId },
        });
      }
    },
    getProblemUri(pid) {
      if (this.contestID) {
        this.$router.push({
          name: 'ContestProblemDetails',
          params: {
            contestID: this.$route.params.contestID,
            problemID: pid,
          },
        });
      } else if (this.groupID) {
        this.$router.push({
          name: 'GroupProblemDetails',
          params: {
            problemID: pid,
            groupID: this.groupID,
          },
        });
      } else {
        this.$router.push({
          name: 'ProblemDetails',
          params: {
            problemID: pid,
          },
        });
      }
    },
    getStatusColor(status) {
      return 'el-tag el-tag--medium status-' + JUDGE_STATUS[status]['color'];
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.username == this.userInfo.username && this.isAuthenticated) {
        return 'own-submit-row';
      }
    },
    disabledManualJudge(status) {
      return !this.isSuperAdmin || status == JUDGE_STATUS_RESERVE['Judging']
        || status == JUDGE_STATUS_RESERVE['Compiling']
        || status == JUDGE_STATUS_RESERVE['ce'];
    },
    openChangeJudgeStatusDialog(judge) {
      this.changeJudgeStatus = {
        submitId: judge.submitId,
        score: judge.score,
        status: judge.status,
        index: judge.index,
      };
      this.changeJudgeStatusDialogVisible = true;
    },
    cancelJudge(row) {
      this.$confirm(this.$i18n.t('m.Cancel_Judge_Tips'), 'Run ID：' + row.submitId, {
        type: 'warning',
      }).then(
        () => {
          api
            .admin_cancelJudge(row.submitId)
            .then((res) => {
              myMessage.success(this.$i18n.t('m.Cancel_Successfully'));
              let data = res.data.data;
              row.status = data.status;
              row.score = data.score;
              row.oiRankScore = data.oiRankScore;
              row.judger = data.judger;
              row.loading = false;
              row.isManual = true;
              // 重新加载这行数据到view中
              this.$refs.xTable.reloadRow(row, null, null);
            })
            .catch(() => {});
        },
        () => {},
      );
    },
    manualJudge() {
      this.changeJudgeStatusLoading = true;
      api
        .admin_manualJudge(
          this.changeJudgeStatus.submitId,
          this.changeJudgeStatus.status,
          this.changeJudgeStatus.score,
        )
        .then(
          (res) => {
            myMessage.success(this.$i18n.t('m.Update_Successfully'));
            let data = res.data.data;
            // 更新数据列表
            this.submissions[this.changeJudgeStatus.index].status = data.status;
            this.submissions[this.changeJudgeStatus.index].score = data.score;
            this.submissions[this.changeJudgeStatus.index].oiRankScore =
              data.oiRankScore;
            this.submissions[this.changeJudgeStatus.index].judger = data.judger;
            this.submissions[this.changeJudgeStatus.index].isManual = true;
            // 更新view中的结果，f分数，耗时，空间消耗，判题机ip
            let viewData = this.$refs.xTable.getTableData().tableData;
            let row = viewData[this.changeJudgeStatus.index];
            row.status = data.status;
            row.score = data.score;
            row.oiRankScore = data.oiRankScore;
            row.judger = data.judger;
            row.isManual = true;
            // 重新加载这行数据到view中
            this.$refs.xTable.reloadRow(row, null, null);
            this.changeJudgeStatusLoading = false;
            this.changeJudgeStatusDialogVisible = false;
          },
          (err) => {
            this.changeJudgeStatusLoading = false;
          },
        )
        .catch(() => {});
    },
    showReplyDetails(row) {
      try {
        let replyOptions = row.replyOptions;
        // 确保replyOptions是一个数组
        if (!Array.isArray(replyOptions)) {
          // 如果是字符串，尝试解析
          if (typeof replyOptions === 'string' && replyOptions.trim() !== '') {
            try {
              replyOptions = JSON.parse(replyOptions);
              // 解析后可能是对象，需要转为数组
              if (!Array.isArray(replyOptions)) {
                replyOptions = [replyOptions];
              }
            } catch (e) {
              console.error('解析replyOptions失败:', e);
              replyOptions = [];
            }
          } else if (replyOptions && typeof replyOptions === 'object') {
            // 如果是单个对象，转为数组
            replyOptions = [replyOptions];
          } else {
            replyOptions = [];
          }
        }
        
        // 调试输出，查看处理后的数据结构
        console.log('处理后的replyOptions:', replyOptions);
        
        this.currentReplyOptions = replyOptions;
        this.currentSubmission = row;
        this.replyDetailsVisible = true;
      } catch (e) {
        console.error('showReplyDetails错误:', e);
        myMessage.error('展示答题内容失败');
      }
    },
    goToSubmissionDetails() {
      if (this.currentSubmission && this.currentSubmission.submitId) {
        this.$router.push({
          name: 'SubmissionDetails',
          params: { submitID: this.currentSubmission.submitId },
        });
      }
      this.replyDetailsVisible = false;
    },
    getShortReplyContent(replyOptions) {
      if (!replyOptions || !Array.isArray(replyOptions) || replyOptions.length === 0) {
        return '--';
      }
      
      try {
        const firstItem = replyOptions[0];
        if (!firstItem) return '--';
        
        if (firstItem.problemType === 'SingleChoice' || firstItem.problemType === 'MultipleChoice') {
          return `${firstItem.optionKey || ''}${replyOptions.length > 1 ? ` +${replyOptions.length - 1}` : ''}`;
        } else {
          const content = firstItem.optionContent || '';
          return content.length > 10 ? content.substring(0, 10) + '...' : content;
        }
      } catch (e) {
        console.error('Error in getShortReplyContent:', e);
        return '--';
      }
    },
    getProblemTypeText(type) {
      try {
        if (!type) return '';
        
        const typeMap = {
          'SingleChoice': this.$t('m.Single_Choice'),
          'MultipleChoice': this.$t('m.Multiple_Choice'),
          'FillBlank': this.$t('m.Fill_Blank'),
          'ShortAnswer': this.$t('m.Short_Answer')
        };
        return typeMap[type] || type;
      } catch (e) {
        console.error('Error in getProblemTypeText:', e);
        return '';
      }
    },
  },
  computed: {
    ...mapGetters([
      'isAuthenticated',
      'userInfo',
      'isSuperAdmin',
      'isAdminRole',
      'contestRuleType',
      'contestStatus',
      'ContestRealTimePermission',
    ]),
    title() {
      if (!this.contestID) {
        return 'Status';
      } else if (this.problemID) {
        return 'Problem Submissions';
      } else {
        return 'Submissions';
      }
    },
    status() {
      return this.formFilter.status === ''
        ? this.$i18n.t('m.Status')
        : JUDGE_STATUS[this.formFilter.status]
          ? JUDGE_STATUS[this.formFilter.status].name
          : this.$i18n.t('m.Status');
    },
    rejudgeColumnVisible() {
      return this.isSuperAdmin;
    },
    scoreColumnVisible() {
      return (
        (this.contestID && this.contestRuleType == this.RULE_TYPE.OI) ||
        !this.contestID
      );
    },
  },
  watch: {
    $route(newVal, oldVal) {
      if (newVal !== oldVal) {
        if (this.autoCheckOpen) {
          clearInterval(this.refreshStatus);
        }
        this.init();
        this.getData();
      }
    },
    isAuthenticated() {
      this.init();
      this.getData();
    },
  },
  beforeRouteLeave(to, from, next) {
    // 防止切换组件后仍然不断请求
    clearInterval(this.refreshStatus);
    next();
  },
};
</script>

<style scoped>
@media only screen and (max-width: 767px) {
  .search {
    margin-top: 20px;
  }
}

.flex-container #main {
  flex: auto;
}

.flex-container .filter {
  margin-right: -10px;
}

.flex-container #contest-menu {
  flex: none;
  width: 210px;
}

::v-deep .el-card__header {
  border-bottom: 0px;
  padding-bottom: 0px;
  text-align: center;
}

::v-deep .el-dialog {
  border-radius: 6px !important;
  text-align: center;
}

::v-deep .el-switch {
  padding-top: 6px;
}

@media only screen and (min-width: 768px) and (max-width: 992px) {
  .el-col-sm-12 {
    padding-top: 10px;
  }
}

@media screen and (min-width: 1350px) {
  ::v-deep .vxe-table--body-wrapper {
    overflow-x: hidden !important;
  }
}

::v-deep .vxe-table .vxe-cell {
  padding: 0 !important;
}

::v-deep .el-dialog--center .el-dialog__body {
  padding-bottom: 0px !important;
}

.manual-judge-title {
  text-align: center;
  font-weight: bolder;
  font-size: 15px;
  margin-bottom: 5px;
}

.reply-card {
  margin-bottom: 15px;
}

.reply-header {
  font-weight: bold;
  color: #303133;
}

.reply-content {
  text-align: left;
  color: #606266;
}

.answer-text {
  background-color: #f8f8f8;
  border: 1px solid #eee;
  border-radius: 3px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.apple-style-dialog {
  border-radius: 12px !important;
  overflow: hidden;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.apple-style-dialog .el-dialog__header {
  background-color: #f5f5f7;
  padding: 18px 20px;
  border-bottom: 1px solid #e6e6e6;
}

.apple-style-dialog .el-dialog__title {
  font-size: 18px;
  font-weight: 500;
  color: #1d1d1f;
}

.apple-style-dialog .el-dialog__body {
  padding: 24px;
}

.apple-style-dialog .el-dialog__footer {
  border-top: 1px solid #e6e6e6;
  padding: 16px 24px;
}

.answer-content-container {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 8px;
}

.apple-card {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.apple-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.apple-card .el-card__header {
  background-color: #f5f5f7;
  padding: 14px 16px;
  border-bottom: 1px solid #e6e6e6;
}

.problem-id {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  margin-right: 8px;
}

.problem-type {
  font-size: 14px;
  color: #6e6e73;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
}

.reply-content {
  padding: 6px 8px;
}

.choice-content, .text-content {
  padding: 8px;
}

.answer-row {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.answer-row:last-child {
  margin-bottom: 0;
}

.answer-label {
  width: 90px;
  color: #6e6e73;
  font-size: 14px;
  text-align: right;
  padding-right: 16px;
  flex-shrink: 0;
}

.answer-value {
  flex: 1;
  color: #1d1d1f;
  font-size: 14px;
  overflow-wrap: break-word;
  word-break: break-word;
}

.option-key {
  font-weight: 500;
  font-size: 16px;
  color: #0066cc;
}

.answer-text {
  background-color: #f5f5f7;
  border: none;
  border-radius: 8px;
  padding: 16px;
  margin: 8px 0 0 0;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
  overflow-x: auto;
  color: #1d1d1f;
  font-family: -apple-system, "SF Mono", SFMono-Regular, ui-monospace, Menlo, monospace;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.dialog-footer .el-button {
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  padding: 10px 20px;
  transition: all 0.3s;
}

.dialog-footer .el-button--primary {
  background-color: #0066cc;
  border-color: #0066cc;
}

.dialog-footer .el-button--primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}
</style>
