<template>
  <el-tag :type="tagType" effect="light">{{ statusName }}</el-tag>
</template>

<script>
export default {
  name: 'StatusTag',
  props: {
    status: {
      type: Number,
      required: true,
    },
  },
  computed: {
    statusInfo() {
      const key = this.status.toString();
      return this.statusMap[key] || { name: 'Unknown Status', type: 'info' };
    },
    statusName() {
      return this.statusInfo.name;
    },
    tagType() {
      return this.statusInfo.type;
    },
  },
  data() {
    return {
      statusMap: {
        '-10': { name: 'Not Submitted', type: 'info' },
        '-5': { name: 'Submitted Unknown Result', type: 'warning' },
        '-4': { name: 'Cancelled', type: 'info' },
        '-3': { name: 'Presentation Error', type: 'danger' },
        '-2': { name: 'Compile Error', type: 'danger' },
        '-1': { name: 'Wrong Answer', type: 'danger' },
        '0': { name: 'Accepted', type: 'success' },
        '1': { name: 'Time Limit Exceeded', type: 'warning' },
        '2': { name: 'Memory Limit Exceeded', type: 'warning' },
        '3': { name: 'Runtime Error', type: 'danger' },
        '4': { name: 'System Error', type: 'danger' },
        '5': { name: 'Pending', type: 'info' },
        '6': { name: 'Compiling', type: 'info' },
        '7': { name: 'Judging', type: 'info' },
        '8': { name: 'Partial Accepted', type: 'success' },
        '9': { name: 'Submitting', type: 'info' },
        '10': { name: 'Submitted Failed', type: 'danger' },
        '15': { name: 'No Status', type: 'info' },
        '-1002': { name: 'Judge SubmitId-ServerId:', type: 'info' },
      },
    };
  },
};
</script>
