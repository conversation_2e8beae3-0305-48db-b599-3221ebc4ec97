<template>
  <div class="flow-design-uploader">
    <!-- 左侧触发按钮 -->
    <div class="trigger-area">
      <el-button class="child-btn" icon="el-icon-magic-stick" type="primary" @click="dialogVisible = true">
        🎨 设计流程图
      </el-button>
    </div>

    <!-- 右侧预览区域 -->
    <div class="preview-area cloud-box">
      <el-image v-if="value" :src="value" class="preview-image" fit="scale-down">
        <div slot="error" class="image-slot">
          <i class="el-icon-picture-outline"></i>
        </div>
      </el-image>
      <div v-else class="empty-tip">
        🌈 快来这里画流程图吧~
      </div>
    </div>

    <!-- 流程图设计弹窗 -->
    <el-dialog :visible.sync="dialogVisible" custom-class="design-dialog" title="🎨 流程图设计师" top="3vh" width="85%">
      <div class="design-container">
        <Diagram v-model="value" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Diagram from '@/components/tuc/flow/components/Diagram.vue';


export default {
  name: 'FlowDesign',
  components: { Diagram },
  props: {
    value: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      dialogVisible: false,
    };
  },
  watch: {
    dialogVisible(val) {

    },
    value(val) {
      if (val) {
        this.dialogVisible = false;
      }
      // 关键修复：当value变化时通知父组件
      this.$emit('input', val); // 新增此行
    },
  },
  methods: {},
};
</script>

<style scoped>
.flow-design-uploader {
  display: flex;
  gap: 20px;
  padding: 15px;
}

.trigger-area, .preview-area {
  width: 220px;
  height: 180px;
  border: 2px dashed #a0d6f0;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fdff;
}

.child-btn {
  font-size: 16px;
  padding: 12px 25px;
  border-radius: 12px;
  background: #83c9e6;
  border-color: #7ec5e3;
  box-shadow: 0 4px 6px rgba(131, 201, 230, 0.3);
}

.cloud-box {
  background: #f8fdff;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(131, 201, 230, 0.2);
}

.design-container {
  display: flex;
  flex-direction: column;
  height: 75vh;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.shape i {
  font-size: 24px;
  color: #409EFF;
}


.diamond i {
  transform: rotate(-45deg);
}


.empty-tip {
  color: #83c9e6;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}


</style>
