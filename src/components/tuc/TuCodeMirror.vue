<template>
  <div>
    <div class="toolbar">
      <el-select v-model="selectedLanguage" class="language-select" size="small" @change="updateMode">
        <el-option v-for="lang in languageOptions" :key="lang" :value="lang">{{ lang }}</el-option>
      </el-select>
      <el-button size="small" @click="formatCode">Format Code</el-button>
      <el-button size="small" @click="clearCode">Clear Code</el-button>
      <el-button size="small" @click="penTestCode">Pen Test Code</el-button>
      <el-button size="small" @click="arrTestCode">Arr Test Code</el-button>
    </div>
    <codemirror ref="myEditor" v-model="code" :options="editorOptions" @change="onCodeChange"></codemirror>
    <template>
      <div class="code-templates">
        <div v-for="group in groupedTemplates" :key="group.name" class="template-group">
          <h3>{{ group.name }}</h3>
          <div class="button-grid">
            <el-button v-for="template in group.templates" :key="template.label" class="template-button" size="mini"
                       @click="insertCodeTemplate(template)">
              {{ template.label }}
            </el-button>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import {codemirror} from 'vue-codemirror-lite'
import 'codemirror/lib/codemirror.css'
import 'codemirror/mode/clike/clike.js'
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/addon/edit/closebrackets.js'
import 'codemirror/addon/selection/active-line.js'

export default {
  name: 'TuCodeMirror',
  components: {codemirror},
  props: {
    // 需要高亮的行号（从0开始计数）
    highlightLine: {
      type: Number,
      default: -1,
      validator(value) {
        // 验证器：允许-1或大于等于0的值
        return value === -1 || value >= 0
      }
    },
    // 编辑器初始代码内容
    code: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      selectedLanguage: 'C++', // 当前选择的编程语言
      languageOptions: ['C', 'C++', 'Java', 'JavaScript'], // 支持的语言列表
      // 编辑器配置项
      editorOptions: {
        mode: 'text/x-c++src', // 语法模式
        theme: 'default', // 主题样式
        lineNumbers: true, // 显示行号
        tabSize: 4, // 缩进大小
        autoCloseBrackets: true, // 自动闭合括号
        styleActiveLine: true // 高亮当前行
      },
      // 代码模板配置（按组分类）
      codeTemplates: [
        // 语法结构模板
        {
          label: 'if-else',
          content: 'if (condition) {\n    \n} else {\n    \n}',
          group: 'Syntax'
        },
        {label: 'for loop', content: 'for (int i = 0; i < ; i++) {\n    \n}', group: 'Syntax'},
        {
          label: 'for loop reversed',
          content: 'for (int i = n - 1; i >= 0; i--) {\n    \n}',
          group: 'Syntax'
        },
        {label: 'for loop i+=2', content: 'for (int i = 0; i < ; i += 2) {\n    \n}', group: 'Syntax'},
        {label: 'while loop', content: 'while (condition) {\n    \n}', group: 'Syntax'},
        {label: 'do while loop', content: 'do {\n    \n} while (condition);', group: 'Syntax'},

        // 基础组件模板
        {label: 'pen 画笔', content: 'pen', group: 'Components'},
        {label: 'arr 数组', content: 'arr', group: 'Components'},

        // 画笔操作模板
        {label: 'fd 前进', content: 'pen.fd();', group: 'Pen'},
        {label: 'rt 右转', content: 'pen.rt();', group: 'Pen'},
        {label: 'lt 左转', content: 'pen.lt();', group: 'Pen'},
        {label: 'move 坐标移动', content: 'pen.move();', group: 'Pen'},
        {label: 'moveFd 向前移动', content: 'pen.moveFd();', group: 'Pen'},
        {label: 'sleep 等待毫秒', content: 'pen.sleep();', group: 'Pen'},
        { label: 'finish 瞬间完成', content: 'pen.finish();', group: 'Pen' },


        {label: 'clear 清理画板', content: 'pen.clear();', group: 'Pen'},
        {label: 'circle 画圆', content: 'pen.circle();', group: 'Pen'},
        {label: 'circleF 画圆', content: 'pen.circleF();', group: 'Pen'},
        {label: 'ellipse 椭圆', content: 'pen.ellipse();', group: 'Pen'},
        {label: 'ellipseF 椭圆', content: 'pen.ellipseF();', group: 'Pen'},
        {label: 'square 正方形', content: 'pen.square();', group: 'Pen'},
        {label: 'squareF 正方形', content: 'pen.squareF();', group: 'Pen'},
        {label: 'orthogonal 长方形', content: 'pen.orthogonal();', group: 'Pen'},
        {label: 'orthogonalF 长方形', content: 'pen.orthogonalF();', group: 'Pen'},
        {label: 'parallelogram 平行四边形', content: 'pen.parallelogram();', group: 'Pen'},
        {label: 'parallelogramF 平行四边形', content: 'pen.parallelogramF();', group: 'Pen'},
        {label: 'triangle 等边三角形', content: 'pen.triangle();', group: 'Pen'},
        {label: 'triangleF 等边三角形', content: 'pen.triangleF();', group: 'Pen'},
        {label: 'rhombus 菱形', content: 'pen.rhombus();', group: 'Pen'},
        {label: 'rhombusF 菱形', content: 'pen.rhombusF();', group: 'Pen'},
        {label: 'color 修改颜色', content: 'pen.color();', group: 'Pen'},
        {label: 'background 修改背景颜色', content: 'pen.background();', group: 'Pen'},

        // 数组操作模板
        {label: 'clear 清理画板', content: 'arr.clear();', group: 'Array'},
        {label: 'build 创建格子', content: 'arr.build();', group: 'Array'},
        {label: 'begin 首个索引', content: 'arr.begin();', group: 'Array'},
        {label: 'getValue 获取容器内容', content: 'arr.getValue();', group: 'Array'},
        {label: 'end 尾部索引', content: 'arr.end();', group: 'Array'},
        {label: 'bright 高亮格子', content: 'arr.bright();', group: 'Array'},
        {label: 'setValue 格子设置内容', content: 'arr.setValue();', group: 'Array'}
      ]
    }
  },

  computed: {
    // 分组后的代码模板（用于侧边栏分类展示）
    groupedTemplates() {
      const groups = this.codeTemplates.reduce((acc, template) => {
        const groupName = template.group
        acc[groupName] = acc[groupName] || []
        acc[groupName].push(template)
        return acc
      }, {})

      return Object.keys(groups).map(groupName => ({
        name: groupName,
        templates: groups[groupName]
      }))
    }
  },

  methods: {
    /** 更新编辑器语法模式 */
    updateMode() {
      const modeMapping = {
        C: 'text/x-csrc',
        'C++': 'text/x-c++src',
        Java: 'text/x-java',
        JavaScript: 'text/javascript'
      }
      this.editorOptions.mode = modeMapping[this.selectedLanguage]
    },

    /** 代码变更事件处理 */
    onCodeChange(newCode) {
      this.code = newCode
    },

    /** 获取当前编辑器代码内容 */
    getCode() {
      return this.code
    },

    /** 设置编辑器代码内容 */
    setCode(code) {
      this.code = code
    },

    /** 格式化代码（去除首尾空白） */
    formatCode() {
      this.$refs.myEditor.editor.setValue(this.formatCppCode(this.code))
    },

    /**
     * 格式化c++代码
     * @param code
     * @returns {string}
     */
    formatCppCode(code) {
      // 定义格式化规则
      const indent = '    '; // 缩进4空格
      const operators = ['++', '--', '==', '!=', '<=', '>=']; // 需要加空格的操作符

      // 预处理：按行分割并去除空白
      let lines = code.split('\n').map(line => line.trim());
      let formatted = [];
      let indentLevel = 0;
      let inComment = false;

      lines.forEach((line, index) => {
        // 跳过空行
        if (line === '') return;

        // 处理多行注释
        if (inComment) {
          formatted.push(indent.repeat(indentLevel) + line);
          if (line.includes('*/')) inComment = false;
          return;
        } else if (line.startsWith('/*')) {
          inComment = true;
          formatted.push(indent.repeat(indentLevel) + line);
          return;
        }

        // 处理单行注释
        if (line.startsWith('//')) {
          formatted.push(indent.repeat(indentLevel) + line);
          return;
        }

        // 调整缩进层级
        let currentIndent = indent.repeat(indentLevel);
        if (line.startsWith('}') || line.endsWith('};')) {
          indentLevel = Math.max(indentLevel - 1, 0);
          currentIndent = indent.repeat(indentLevel);
        }

        // 添加操作符空格
        operators.forEach(op => {
          const regex = new RegExp(`\\s*${op.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*`, 'g');
          line = line.replace(regex, ` ${op} `);
        });

        // 格式化当前行
        formatted.push(currentIndent + line);

        // 更新缩进层级
        if (line.includes('{') || line.endsWith('{')) {
          indentLevel++;
        }
      });

      return formatted.join('\n');
    },

    /** 清空编辑器内容 */
    clearCode() {
      this.$refs.myEditor.editor.setValue('')
    },

    /** 插入数组测试代码 */
    arrTestCode() {
      this.$refs.myEditor.editor.setValue(
          'arr.build(10);\n' +
          'for(int i=0;i< 10; i++){\n' +
          '\tarr.bright(i);\n' +
          '}\n' +
          'int x = arr.getValue(1);\n' +
          'arr.build(x);\n'
      )
    },

    /** 插入画笔测试代码 */
    penTestCode() {
      this.$refs.myEditor.editor.setValue(
          'pen.rt(90);\n' +
          'for (int i = 0; i < 5; i++) {\n' +
          '  pen.sleep(1000);\n' +
          '  pen.color(i);\n' +
          '  pen.circle(10);\n' +
          '  pen.triangleF(15);\n' +
          '  pen.fd(100);\n' +
          '  pen.rt(144);\n' +
          '}'
      )
    },

    /** 插入代码模板到当前光标位置 */
    insertCodeTemplate(template) {
      const editor = this.$refs.myEditor.editor
      const doc = editor.getDoc()
      const cursor = doc.getCursor()
      doc.replaceRange(template.content, cursor)
    },

    /** 处理行高亮逻辑 */
    handleHighlight() {
      if (this.highlightLine === -1) {
        this.clearHighlight()
        return
      }
      if (this.highlightLine >= 0) {
        this.applyHighlight(this.highlightLine)
      }
    },

    /** 清除所有行高亮 */
    clearHighlight() {
      const editor = this.$refs.myEditor.editor
      if (!editor) {
        console.warn('编辑器实例未就绪!')
        return
      }
      const totalLines = editor.lineCount()
      for (let i = 0; i < totalLines; i++) {
        editor.removeLineClass(i, 'background', 'highlight-line')
      }
    },

    /** 应用指定行高亮 */
    applyHighlight(lineNumber) {
      const editor = this.$refs.myEditor.editor
      if (!editor) {
        console.warn('编辑器实例未就绪!')
        return
      }

      const totalLines = editor.lineCount()
      if (lineNumber >= 0 && lineNumber < totalLines) {
        // 先清除所有高亮
        this.clearHighlight()
        // 添加当前行高亮
        editor.addLineClass(lineNumber, 'background', 'highlight-line')
      } else {
        console.warn('行号超出有效范围!')
      }
    },

    /** 加载本地缓存的代码内容 */
    loadCacheCode() {
      if (this.code === '') {
        try {
          const cachedCode = localStorage.getItem('editorCache')
          if (cachedCode) {
            this.code = cachedCode
          }
        } catch (e) {
          console.error('读取本地存储失败:', e)
        }
      }
    }
  },

  watch: {
    // 监听代码变化自动保存到本地存储
    code(newVal) {
      try {
        localStorage.setItem('editorCache', newVal)
      } catch (e) {
        console.error('保存到本地存储失败:', e)
      }
    },

    // 监听高亮行号变化
    highlightLine(newVal) {
      this.handleHighlight()
    }
  },

  mounted() {
    // 初始化编辑器模式
    this.updateMode()

    // 确保DOM更新后执行高亮操作
    this.$nextTick(() => {
      this.handleHighlight()
    })

    // 加载缓存的代码内容
    this.loadCacheCode()
  }
}
</script>

<style>
.code-templates {
  margin-top: 10px;
}

.template-group {
  margin-bottom: 15px;
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 5px; /* Reduced gap for a more compact layout */
}

.template-button {
  text-align: center;
  font-size: 12px; /* Smaller font size to fit the compact style */
}

.template-group h3 {
  margin-bottom: 8px;
  font-size: 18px; /* Increased font size for headers */
  color: #333;
  text-align: left;
}


.highlight-line {
  background-color: #ff0000 !important;
}

.toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
