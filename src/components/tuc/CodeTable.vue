<template>
  <div>
    <el-table :data="codeList" size="mini" style="width: 100%">
      <el-table-column align="center" label="ID" prop="id" width="50"></el-table-column>
      <el-table-column align="center" label="UID" prop="uid"></el-table-column>
      <el-table-column align="center" label="Username" prop="username"></el-table-column>
      <el-table-column align="center" label="Nickname" prop="nickname"></el-table-column>
      <el-table-column align="center" label="Realname" prop="realname"></el-table-column>
      <el-table-column align="center" label="Code">
        <template slot-scope="scope">
          <div
              style="position: relative; text-align: center;"
              @mouseenter="showCode(scope.row.code, $event, scope.$index)"
              @mouseleave="hideCode"
          >
            <span>……</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="Actions" width="200">
        <template slot-scope="scope">
          <div class="action-buttons" style="display: flex; justify-content: center; gap: 10px;">
            <el-button
                icon="el-icon-document-copy"
                size="mini"
                style="padding: 5px 10px; font-size: 12px;"
                type="primary"
                @click="copyCode(scope.row.code)"
            >
            </el-button>

            <el-button
                v-if="buttonText !== null && buttonText !== ''"
                icon="el-icon-s-operation"
                size="mini"
                style="padding: 5px 10px; font-size: 12px;"
                type="success"
                @click="$emit('other-operation', scope.row)"
            >
              {{ buttonText }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div
        v-if="tooltipVisible"
        :style="tooltipStyle"
        class="code-tooltip"
        @mouseenter="keepTooltipVisible"
        @mouseleave="hideCode"
    >
      <codemirror
          :options="codeMirrorOptions"
          :value="currentCode"
      ></codemirror>
    </div>
  </div>
</template>

<script>
import "codemirror/lib/codemirror.css";
import {codemirror} from "vue-codemirror-lite";
import "codemirror/mode/clike/clike.js";
import "codemirror/mode/javascript/javascript.js";
import "codemirror/addon/edit/closebrackets.js";
import "codemirror/addon/selection/active-line.js";
import myMessage from "@/common/message";

export default {
  components: {codemirror},
  props: {
    codeList: {
      type: Array,
      required: true,
    },
    buttonText: {
      type: String,
      default: null,
    }
  },
  data() {
    return {
      tooltipVisible: false,
      tooltipStyle: {
        top: "0px",
        left: "0px",
      },
      currentCode: "",
      codeMirrorOptions: {
        mode: "text/x-c++src",
        theme: "default",
        lineNumbers: true,
        tabSize: 4,
        autoCloseBrackets: true,
        styleActiveLine: true,
        readOnly: true,
      },
    };
  },
  methods: {
    showCode(code, event, index) {
      this.currentCode = code;

      const target = event.target.closest("div");
      const rect = target.getBoundingClientRect();
      const tooltipWidth = 300;
      const tooltipHeight = 200;

      const top = rect.top - tooltipHeight - 10 + window.scrollY;
      const left = rect.left + rect.width / 2 - tooltipWidth / 2 + window.scrollX;

      this.tooltipStyle = {
        top: `${top}px`,
        left: `${left}px`,
      };

      this.tooltipVisible = true;
    },
    hideCode() {
      this.tooltipVisible = false;
    },
    keepTooltipVisible() {
      this.tooltipVisible = true;
    },
    copyCode(code) {
      const textArea = document.createElement("textarea");
      textArea.value = code;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand("copy");
        myMessage.success("Code copied successfully!");
      } catch (err) {
        myMessage.error("Failed to copy code.");
      }
      document.body.removeChild(textArea);
    },
  },
};
</script>

<style scoped>
.code-tooltip {
  position: absolute;
  z-index: 9999;
  border: 1px solid #ddd;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  overflow: hidden;
  width: 300px;
  height: 200px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.el-button {
  font-size: 12px;
  padding: 5px 10px;
}

.el-table th, .el-table td {
  text-align: center !important;
}

.el-table .cell {
  padding: 5px 10px;
}

.el-button {
  font-size: 12px;
  padding: 5px 10px;
}

.el-table-column .cell {
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}
</style>
