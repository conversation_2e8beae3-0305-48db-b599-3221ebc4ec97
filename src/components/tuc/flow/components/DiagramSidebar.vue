<template>
  <div class="diagram-sidebar">
    <div>
      <h1 class="node-category-title">图形</h1>
      <div class="node-category">
        <div class="node-item" @mousedown="dragInNode('pro-circle')">
          <icon-circle class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('pro-rect')">
          <icon-rect class="svg-node"/>
        </div>
        <div class="node-item" @mousedown="dragInNode('rect-radius')">
          <icon-rect-radius class="svg-node"/>
        </div>
        <div class="node-item" @mousedown="dragInNode('triangle')">
          <icon-triangle class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('pro-ellipse')">
          <icon-ellipse class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('pro-diamond')">
          <icon-diamond class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('cylinde')">
          <icon-cylinde class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('actor')">
          <icon-actor class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('parallelogram')">
          <icon-parallelogram class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('pro-text')">
          <icon-text class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('left-arrow')">
          <icon-left-arrow class="svg-node"/>
        </div>
        <div class="node-item" @mousedown="dragInNode('right-arrow')">
          <icon-right-arrow class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('horizontal-arrow')">
          <icon-horizontal-arrow class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('up-arrow')">
          <icon-up-arrow class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('down-arrow')">
          <icon-down-arrow class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('vertical-arrow')">
          <icon-vertical-arrow class="svg-node" />
        </div>
        <!-- <div class="node-item" @mousedown="dragInNode('star')">
          star
        </div> -->
        <div class="node-item" @mousedown="dragInNode('pentagon')">
          <icon-pentagon class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('hexagon')">
          <icon-hexagon class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('septagon')">
          <icon-septagon class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('heptagon')">
          <icon-heptagon class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('trapezoid')">
          <icon-trapezoid class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('cross')">
          <icon-cross class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('minus')">
          <icon-minus class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('times')">
          <icon-times class="svg-node" />
        </div>
        <div class="node-item" @mousedown="dragInNode('divide')">
          <icon-divide class="svg-node" />
        </div>
      </div>
    </div>
    <div>
      <h1 class="node-category-title">操作</h1>
      <div class="node-button-group child-friendly-buttons">
        <el-button-group>
          <el-button type="primary" size="small" @click="downloadFlow" class="bubble-button">
            <i class="el-icon-download cute-icon"></i>
            <span class="button-text">保存到电脑</span>
          </el-button>
          <el-button type="primary" size="small" @click="resetFlow" class="bubble-button">
            <i class="el-icon-delete cute-icon"></i>
            <span class="button-text">清空画布</span>
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="saveAndUpload"
            class="bubble-button upload-btn">
            <i class="el-icon-upload2 cute-icon"></i>
            <span class="button-text">上传到云端</span>
          </el-button>
        </el-button-group>
      </div>
    </div>
    <div v-if="false">
      <h1 class="node-category-title">图片</h1>
      <div class="image-node image-setting" @mousedown="dragInNode('image-setting')">
      </div>
      <div class="image-node image-user" @mousedown="dragInNode('image-user')">
      </div>
      <div class="image-node image-cloud" @mousedown="dragInNode('image-cloud')">
      </div>
    </div>
    <div v-if="false">
      <h1 class="node-category-title">ICON</h1>
      <div class="icon-node icon-message" @mousedown="dragInNode('icon-message')">
      </div>
    </div>
  </div>
</template>

<script>
import myMessage from '@/common/message';
import IconCircle from './icon/Circle.vue'
import IconRect from './icon/Rect.vue'
import IconRectRadius from './icon/RectRadius.vue'
import IconActor from './icon/Actor.vue'
import IconCylinde from './icon/Cylinde.vue'
import IconDiamond from './icon/Diamond.vue'
import IconEllipse from './icon/Ellipse.vue'
import IconParallelogram from './icon/Parallelogram.vue'
import IconText from './icon/Text.vue'
import IconTriangle from './icon/Triangle.vue'
import IconLeftArrow from './icon/LeftArrow.vue'
import IconRightArrow from './icon/RightArrow.vue'
import IconHorizontalArrow from './icon/HorizontalArrow.vue'
import IconUpArrow from './icon/UpArrow.vue'
import IconDownArrow from './icon/DownArrow.vue'
import IconVerticalArrow from './icon/VerticalArrow.vue'
import IconPentagon from './icon/Pentagon.vue'
import IconHexagon from './icon/Hexagon.vue'
import IconSeptagon from './icon/Septagon.vue'
import IconHeptagon from './icon/Heptagon.vue'
import IconTrapezoid from './icon/Trapezoid.vue'
import IconCross from './icon/Cross.vue'
import IconMinus from './icon/Minus.vue'
import IconTimes from './icon/Times.vue'
import IconDivide from './icon/Divide.vue'

export default {
  name: 'DiagramSidebar',
  methods: {
    dragInNode (type) {
      this.$emit('dragInNode', type)
    },
    resetFlow() {
      myMessage.success('画布已清空，开始新的创作吧~');
      this.$emit('resetFlow');
    },
    downloadFlow() {
      this.$emit('downloadFlow');
    },
    saveAndUpload() {
      this.$emit('saveAndUpload');
    }
  },
  components: {
    IconCircle,
    IconRect,
    IconRectRadius,
    IconActor,
    IconCylinde,
    IconDiamond,
    IconEllipse,
    IconParallelogram,
    IconText,
    IconTriangle,
    IconRightArrow,
    IconLeftArrow,
    IconHorizontalArrow,
    IconUpArrow,
    IconDownArrow,
    IconVerticalArrow,
    IconPentagon,
    IconHexagon,
    IconSeptagon,
    IconHeptagon,
    IconTrapezoid,
    IconCross,
    IconMinus,
    IconTimes,
    IconDivide
  }
}
</script>

<style scoped>
/* 按钮样式 */
.child-friendly-buttons {
  --main-color: #6dd0ff; /* 浅蓝色 */
  --hover-color: #4fb3e3; /* 稍深的浅蓝 */
  --shadow-color: rgba(109, 208, 255, 0.4);
}

/* 按钮基础样式 */
.bubble-button {
  border-radius: 10px !important;
  padding: 10px !important;
  margin: 5px 0px;
  border: 1px solid var(--main-color) !important;
  background: linear-gradient(145deg, #c6fdd1, var(--main-color)) !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 4px 8px var(--shadow-color) !important;
}

/* 图标样式 */
.cute-icon {
  font-family: 'Comic Sans MS', cursive;
  color: #fff !important;
  font-size: 10px !important;
  margin-right: 8px !important;
  vertical-align: middle;
}

/* 文字样式 */
.button-text {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* 悬停效果 */
.bubble-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 12px var(--shadow-color) !important;
  background: linear-gradient(145deg, var(--main-color), #4fb3e3) !important;
}

/* 按钮间距 */
.el-button-group {
  gap: 15px !important;
  padding: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 30px;
}

/* 上传按钮特殊样式 */
.upload-btn {
  animation: float 2s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* 按钮样式结束 */

.diagram-sidebar {
  user-select: none;
}
.node-category-title {
  margin: 0;
  font-size: 14px;
  display: block;
  border-bottom: 1px solid #e5e5e5;
  line-height: 30px;
  margin-bottom: 10px;
}
.node-item {
  width: 35px;
  height: 35px;
  margin-right: 5px;
  display: inline-block;
}
.node-category {
  border-bottom: 1px solid #e5e5e5;
}
.svg-node {
  left: 1px;
  top: 1px;
  width: 32px;
  height: 30px;
  display: block;
  position: relative;
  overflow: hidden;
}
.image-node, .icon-node {
  display: inline-block;
  width: 30px;
  height: 30px;
  margin: 10px;
  cursor: pointer;
}
.image-setting {
  background: url('https://dpubstatic.udache.com/static/dpubimg/UzI4AFUcfO/setting.png');
  background-size: cover;
}
.image-user {
  width: 40px;
  background: url('https://dpubstatic.udache.com/static/dpubimg/-6Fd2uIoJ-/user.png');
  background-size: cover;
}
.image-cloud {
  width: 40px;
  background: url('https://dpubstatic.udache.com/static/dpubimg/0oqFX1nvbD/cloud.png');
  background-size: cover;
}
.icon-message {
  height: 20px;
  background: url('https://dpubstatic.udache.com/static/dpubimg/1TZgBoaq8G/message.png');
  background-size: cover;
}
</style>
