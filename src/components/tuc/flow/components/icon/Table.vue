<template>
  <svg class="svg-node">
    <g>
      <g transform="translate(0.5,0.5)" style="visibility: visible">
        <path
          d="M 1.26 9.24 L 1.26 3.78 L 30.66 3.78 L 30.66 9.24"
          fill="#ffffff"
          stroke="#000000"
          stroke-width="1.3"
          stroke-miterlimit="10"
          pointer-events="all"
        ></path>
        <path
          d="M 1.26 9.24 L 1.26 25.62 L 30.66 25.62 L 30.66 9.24"
          fill="none"
          stroke="white"
          stroke-width="9.3"
          stroke-miterlimit="10"
          pointer-events="stroke"
          visibility="hidden"
        ></path>
        <path
          d="M 1.26 9.24 L 1.26 25.62 L 30.66 25.62 L 30.66 9.24"
          fill="none"
          stroke="#000000"
          stroke-width="1.3"
          stroke-miterlimit="10"
          pointer-events="none"
        ></path>
        <path
          d="M 1.26 9.24 L 30.66 9.24"
          fill="none"
          stroke="white"
          stroke-width="9.3"
          stroke-miterlimit="10"
          pointer-events="stroke"
          visibility="hidden"
        ></path>
        <path
          d="M 1.26 9.24 L 30.66 9.24"
          fill="none"
          stroke="#000000"
          stroke-width="1.3"
          stroke-miterlimit="10"
          pointer-events="none"
        ></path>
      </g>
      <g
        fill="#000000"
        font-family="Helvetica"
        text-anchor="middle"
        font-size="2.52px"
      >
        <text x="15.96" y="7.56">List</text>
      </g>
      <g transform="translate(0.5,0.5)" style="visibility: visible">
        <rect
          x="1.26"
          y="9.24"
          width="29.4"
          height="5.46"
          fill="none"
          stroke="white"
          pointer-events="stroke"
          visibility="hidden"
          stroke-width="9"
        ></rect>
        <rect
          x="1.26"
          y="9.24"
          width="29.4"
          height="5.46"
          fill="none"
          stroke="none"
          pointer-events="all"
        ></rect>
      </g>
      <g style="">
        <clipPath id="mx-clip-1-9-31-9-0">
          <rect x="1" y="9" width="31" height="9"></rect>
        </clipPath>
        <g
          fill="#000000"
          font-family="Helvetica"
          clip-path="url(https://app.diagrams.net/#mx-clip-1-9-31-9-0)"
          font-size="2.52px"
        >
          <text x="2.52" y="13.02">Item 1</text>
        </g>
      </g>
      <g transform="translate(0.5,0.5)" style="visibility: visible">
        <rect
          x="1.26"
          y="14.7"
          width="29.4"
          height="5.46"
          fill="none"
          stroke="white"
          pointer-events="stroke"
          visibility="hidden"
          stroke-width="9"
        ></rect>
        <rect
          x="1.26"
          y="14.7"
          width="29.4"
          height="5.46"
          fill="none"
          stroke="none"
          pointer-events="all"
        ></rect>
      </g>
      <g style="">
        <clipPath id="mx-clip-1-14-31-9-0">
          <rect x="1" y="14" width="31" height="9"></rect>
        </clipPath>
        <g fill="#000000" font-family="Helvetica" font-size="2.52px">
          <text x="2.52" y="18.48">Item 2</text>
        </g>
      </g>
      <g transform="translate(0.5,0.5)" style="visibility: visible">
        <rect
          x="1.26"
          y="20.16"
          width="29.4"
          height="5.46"
          fill="none"
          stroke="white"
          pointer-events="stroke"
          visibility="hidden"
          stroke-width="9"
        ></rect>
        <rect
          x="1.26"
          y="20.16"
          width="29.4"
          height="5.46"
          fill="none"
          stroke="none"
          pointer-events="all"
        ></rect>
      </g>
      <g style="">
        <clipPath id="mx-clip-1-20-31-9-0">
          <rect x="1" y="20" width="31" height="9"></rect>
        </clipPath>
        <g fill="#000000" font-family="Helvetica" font-size="2.52px">
          <text x="2.52" y="23.94">Item 3</text>
        </g>
      </g>
    </g>
  </svg>
</template>

<script>
export default {
}
</script>
