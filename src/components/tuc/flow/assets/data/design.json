{"nodes": [{"id": "a7ad9a63-5af4-462c-a2ba-e9ab4cfaa1e1", "type": "pro-rect", "x": 595, "y": 90, "properties": {"nodeSize": {"width": 890, "height": 40}, "borderStyle": "dashed", "fontSize": "16"}, "zIndex": 1002}, {"id": "39b2c1a6-9700-446f-94df-d49eb25f5b3d", "type": "pro-text", "x": 120, "y": 90, "properties": {"fontSize": "16"}, "zIndex": 1004, "text": {"x": 120, "y": 90, "value": "API"}}, {"id": "e5ff9b2d-0a87-4e36-a704-d0809dec3ffd", "type": "pro-rect", "x": 215, "y": 90, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgba(110,246,169,1)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1006, "text": {"x": 215, "y": 90, "value": "Render"}}, {"id": "303747b6-00e8-4a78-b555-dbfb2c948d1a", "type": "pro-rect", "x": 435, "y": 90, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgba(110,246,169,1)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1006, "text": {"x": 435, "y": 90, "value": "图形编辑API"}}, {"id": "fe9539ec-1bf1-4523-a843-9f928f7d7cc6", "type": "pro-rect", "x": 930, "y": 90, "properties": {"nodeSize": {"width": 130, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgba(110,246,169,1)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1006, "text": {"x": 930, "y": 90, "value": "节点、连线、插件注册"}}, {"id": "b92d5936-42e5-4e47-961d-5998934bf4c4", "type": "pro-rect", "x": 690, "y": 90, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgba(110,246,169,1)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1034, "text": {"x": 690, "y": 90, "value": "数据操作API"}}, {"id": "69dcbda8-4d4e-4d39-9ac5-6361ce5753c8", "type": "pro-rect", "x": 335, "y": 260, "properties": {"nodeSize": {"width": 370, "height": 260}, "borderStyle": "dashed"}, "zIndex": 1008}, {"id": "c8d58b41-32d9-4b00-8491-8898eed645c3", "type": "pro-text", "x": 180, "y": 150, "properties": {"fontSize": "16"}, "zIndex": 1010, "text": {"x": 180, "y": 150, "value": "View"}}, {"id": "ffab6fce-1510-403d-ab2f-2295ef129e19", "type": "pro-rect", "x": 210, "y": 180, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 210, "y": 180, "value": "RectNode"}}, {"id": "ba7b9e53-c7e4-4001-931d-2a6eb39366ae", "type": "pro-rect", "x": 455, "y": 180, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 455, "y": 180, "value": "Line"}}, {"id": "1e6c1cfe-180b-4a14-ae1e-f44c06540b8d", "type": "pro-rect", "x": 455, "y": 225, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 455, "y": 225, "value": "Polyline"}}, {"id": "3241f948-5b05-4802-9d89-85341a5a9c64", "type": "pro-rect", "x": 455, "y": 270, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 455, "y": 270, "value": "BezierLine"}}, {"id": "f33f66c3-548d-410b-8b50-b88db015cac2", "type": "pro-rect", "x": 465, "y": 320, "properties": {"nodeSize": {"width": 80, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 465, "y": 320, "value": "BaseLine"}}, {"id": "436905dc-4094-4ae5-a474-2d1e3dbdee30", "type": "pro-rect", "x": 390, "y": 320, "properties": {"nodeSize": {"width": 70, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)", "fontColor": "rgba(253,159,1,1)"}, "zIndex": 1013, "text": {"x": 390, "y": 320, "value": "Arrow"}}, {"id": "e24f347c-c41c-4721-9bb6-9e3bbc6f208d", "type": "pro-rect", "x": 320, "y": 180, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 320, "y": 180, "value": "CircleNode"}}, {"id": "16d0dac8-cff4-4529-a99b-305f69ef071d", "type": "pro-rect", "x": 210, "y": 225, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 210, "y": 225, "value": "EllipseNode"}}, {"id": "ba7b63f7-7f2c-446b-a0ac-573d8f564b5c", "type": "pro-rect", "x": 320, "y": 225, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 320, "y": 225, "value": "HtmlNodeNode"}}, {"id": "5f9b439f-481a-4a3e-a094-1bc9f43428fd", "type": "pro-rect", "x": 210, "y": 270, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 210, "y": 270, "value": "PolygonNode"}}, {"id": "f8baebf4-ea42-4151-8904-c818c3f2a129", "type": "pro-rect", "x": 320, "y": 270, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 320, "y": 270, "value": "DiamondNode"}}, {"id": "98111878-6ec7-4a36-bd21-22c65102bec9", "type": "pro-rect", "x": 200, "y": 320, "properties": {"nodeSize": {"width": 80, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 200, "y": 320, "value": "BaseNode"}}, {"id": "1d83b120-5e92-4bb9-9a30-a5f84f51a893", "type": "pro-rect", "x": 280, "y": 320, "properties": {"nodeSize": {"width": 80, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)", "fontColor": "rgba(252,159,3,1)"}, "zIndex": 1013, "text": {"x": 280, "y": 320, "value": "<PERSON><PERSON>"}}, {"id": "896fddec-a2ce-4af8-be34-f74c46293bc4", "type": "pro-rect", "x": 200, "y": 360, "properties": {"nodeSize": {"width": 80, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(255, 242, 204)", "borderWidth": "1px", "borderColor": "rgb(214, 182, 86)", "fontColor": "rgba(252,159,3,1)"}, "zIndex": 1013, "text": {"x": 200, "y": 360, "value": "背景层"}}, {"id": "0df63813-5164-4a94-8765-e559a4a043c0", "type": "pro-rect", "x": 285, "y": 360, "properties": {"nodeSize": {"width": 80, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(255, 242, 204)", "borderWidth": "1px", "borderColor": "rgb(214, 182, 86)", "fontColor": "rgba(252,159,3,1)"}, "zIndex": 1013, "text": {"x": 285, "y": 360, "value": "图形层"}}, {"id": "77ceabbc-eb68-4f40-92be-3616c0375ed7", "type": "pro-rect", "x": 380, "y": 360, "properties": {"nodeSize": {"width": 80, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(255, 242, 204)", "borderWidth": "1px", "borderColor": "rgb(214, 182, 86)", "fontColor": "rgba(252,159,3,1)"}, "zIndex": 1013, "text": {"x": 380, "y": 360, "value": "修饰层"}}, {"id": "cbcdbdc9-c6e7-46f6-9ea9-53c30843bea5", "type": "pro-rect", "x": 465, "y": 360, "properties": {"nodeSize": {"width": 80, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(255, 242, 204)", "borderWidth": "1px", "borderColor": "rgb(214, 182, 86)", "fontColor": "rgba(252,159,3,1)"}, "zIndex": 1013, "text": {"x": 465, "y": 360, "value": "组件层"}}, {"id": "f35b1931-5022-4315-98ae-4920c0ae3396", "type": "pro-rect", "x": 725, "y": 260, "properties": {"nodeSize": {"width": 370, "height": 260}, "borderStyle": "dashed"}, "zIndex": 1008}, {"id": "e21cfe76-25a6-4fd9-b265-d799cc7b6b74", "type": "pro-text", "x": 215, "y": 190, "properties": {"fontSize": "16"}, "zIndex": 1010, "text": {"x": 215, "y": 190, "value": "View"}}, {"id": "424152ef-22a4-4af9-bc98-1cf36bff4753", "type": "pro-text", "x": 570, "y": 150, "properties": {"fontSize": "14"}, "zIndex": 1018, "text": {"x": 570, "y": 150, "value": "Model"}}, {"id": "17d873cb-2391-465a-9e0b-1dc28c6d8eb5", "type": "pro-rect", "x": 600, "y": 180, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 600, "y": 180, "value": "RectNode"}}, {"id": "6ceb4605-b281-4587-a385-b4edc88e9857", "type": "pro-rect", "x": 710, "y": 180, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 710, "y": 180, "value": "CircleNode"}}, {"id": "97001a45-3c11-422b-a6ad-f3e24ea19e36", "type": "pro-rect", "x": 600, "y": 225, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 600, "y": 225, "value": "EllipseNode"}}, {"id": "17d35da2-924c-49f1-aad3-e6ec5e2d800a", "type": "pro-rect", "x": 710, "y": 225, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 710, "y": 225, "value": "HtmlNodeNode"}}, {"id": "7dbc3e4f-c6a7-48e5-a79c-66fdf519ef51", "type": "pro-rect", "x": 600, "y": 270, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 600, "y": 270, "value": "PolygonNode"}}, {"id": "c33545b6-fd1b-4173-8f49-f409cb7e0abe", "type": "pro-rect", "x": 710, "y": 270, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 710, "y": 270, "value": "DiamondNode"}}, {"id": "cdb3f43a-c3a9-4b8f-a499-310d88e8e61f", "type": "pro-rect", "x": 600, "y": 315, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 600, "y": 315, "value": "BaseNode"}}, {"id": "faad0562-8793-42ee-8c36-25e30ba6ad61", "type": "pro-rect", "x": 845, "y": 180, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 845, "y": 180, "value": "Line"}}, {"id": "e3f082f1-ed68-478b-898c-e64506b7767f", "type": "pro-rect", "x": 845, "y": 225, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 845, "y": 225, "value": "Polyline"}}, {"id": "4fa2d205-da39-41ef-8ac1-cb5fafcf8d0c", "type": "pro-rect", "x": 845, "y": 270, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 845, "y": 270, "value": "BezierLine"}}, {"id": "9a7aa891-93c2-4be1-a29d-cef1e26de202", "type": "pro-rect", "x": 845, "y": 315, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 845, "y": 315, "value": "BaseLine"}}, {"id": "874bded5-4d75-45f9-88c4-d32c62e4d6cf", "type": "pro-rect", "x": 710, "y": 315, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(255, 230, 204)", "borderWidth": "1px", "borderColor": "rgb(215, 155, 0)", "fontColor": "rgba(245,166,35,1)"}, "zIndex": 1013, "text": {"x": 710, "y": 315, "value": "BaseElement"}}, {"id": "07ee5161-2eb9-4b94-856b-1342092d8df4", "type": "pro-rect", "x": 600, "y": 360, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(255, 242, 204)", "borderWidth": "1px", "borderColor": "rgb(214, 182, 86)", "fontColor": "rgba(245,166,35,1)"}, "zIndex": 1013, "text": {"x": 600, "y": 360, "value": "GraphModel"}}, {"id": "bc1c64ae-0524-4146-87fa-6f9ab4c53e7e", "type": "pro-rect", "x": 710, "y": 360, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(255, 242, 204)", "borderWidth": "1px", "borderColor": "rgb(214, 182, 86)", "fontColor": "rgba(245,166,35,1)"}, "zIndex": 1013, "text": {"x": 710, "y": 360, "value": "EditConfigModel"}}, {"id": "da1e5afd-06b6-4d81-8f79-2caa1bdc4f05", "type": "pro-rect", "x": 845, "y": 360, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(255, 242, 204)", "borderWidth": "1px", "borderColor": "rgb(214, 182, 86)", "fontColor": "rgba(245,166,35,1)"}, "zIndex": 1013, "text": {"x": 845, "y": 360, "value": "TransformModel"}}, {"id": "bb1f7250-37cb-4389-9b68-eb57424462b9", "type": "pro-rect", "x": 985, "y": 260, "properties": {"nodeSize": {"width": 120, "height": 260}, "borderStyle": "dashed", "fontSize": "16"}, "zIndex": 1008}, {"id": "015bcf65-b082-494e-949c-064037b9d37a", "type": "pro-rect", "x": 985, "y": 180, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 985, "y": 180, "value": "event"}}, {"id": "19165b16-8a71-4c20-8db6-ecf6d6b846b2", "type": "pro-rect", "x": 985, "y": 225, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 985, "y": 225, "value": "keyboard"}}, {"id": "6f4018f2-0ef9-48e8-bd52-dcfd3cd29036", "type": "pro-rect", "x": 985, "y": 270, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 985, "y": 270, "value": "History"}}, {"id": "a574be8f-d496-4c53-b47f-273e93f8818f", "type": "pro-rect", "x": 985, "y": 315, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 985, "y": 315, "value": "Dnd"}}, {"id": "3cce39be-a48b-4958-9341-bc714a2be7b1", "type": "pro-rect", "x": 985, "y": 360, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1013, "text": {"x": 985, "y": 360, "value": "Utils"}}, {"id": "f321276a-c15a-4ec9-aede-4d30c7b28050", "type": "pro-text", "x": 950, "y": 150, "properties": {"fontSize": "16"}, "zIndex": 1020, "text": {"x": 950, "y": 150, "value": "Tool"}}, {"id": "6f9e24ab-2dd3-4244-87d8-a48b78e4997d", "type": "pro-text", "x": 520, "y": 30, "properties": {"fontSize": "16"}, "zIndex": 1024, "text": {"x": 520, "y": 30, "value": "核心包"}}, {"id": "e4c641bb-1d84-4bd0-b481-04682e0d3f58", "type": "pro-text", "x": 520, "y": 50, "properties": {"fontSize": "14"}, "zIndex": 1026, "text": {"x": 520, "y": 50, "value": "@logicflow/core"}}, {"id": "52a800ad-05cc-4c74-bd0f-675465a2953c", "type": "pro-rect", "x": 1135, "y": 230, "properties": {"nodeSize": {"width": 120, "height": 320}, "borderStyle": "dashed"}, "zIndex": 1008}, {"id": "fd0e1338-4ead-4dd3-bd59-c12449205e01", "type": "pro-text", "x": 1125, "y": 85, "properties": {"fontSize": "16"}, "zIndex": 1028, "text": {"x": 1125, "y": 85, "value": "extensions"}}, {"id": "ae746378-1df8-4ed6-997e-16fde49b4967", "type": "pro-text", "x": 1130, "y": 35, "properties": {"fontSize": "16"}, "zIndex": 1024, "text": {"x": 1130, "y": 35, "value": "拓展包"}}, {"id": "c02505bf-ffaa-4891-a0d5-235cc66dd691", "type": "pro-text", "x": 1130, "y": 55, "properties": {"fontSize": "14"}, "zIndex": 1026, "text": {"x": 1130, "y": 55, "value": "@logicflow/extension"}}, {"id": "2d0626fe-8307-445e-a161-94450ea60c8c", "type": "pro-rect", "x": 1135, "y": 115, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(245, 245, 245)", "borderWidth": "1px", "borderColor": "rgb(102, 102, 102)"}, "zIndex": 1013, "text": {"x": 1135, "y": 115, "value": "菜单"}}, {"id": "ba1673cc-5105-41c0-852a-72968fd9a18b", "type": "pro-rect", "x": 1135, "y": 155, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(245, 245, 245)", "borderWidth": "1px", "borderColor": "rgb(102, 102, 102)"}, "zIndex": 1013, "text": {"x": 1135, "y": 155, "value": "小地图"}}, {"id": "91a634cf-1246-4f52-b587-674cc160d2d4", "type": "pro-rect", "x": 1135, "y": 195, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(245, 245, 245)", "borderWidth": "1px", "borderColor": "rgb(102, 102, 102)"}, "zIndex": 1013, "text": {"x": 1135, "y": 195, "value": "小地图"}}, {"id": "d471237b-5ada-48ca-963b-fcfa50870bcf", "type": "pro-text", "x": 1135, "y": 365, "properties": {"fontSize": "18"}, "zIndex": 1031, "text": {"x": 1135, "y": 365, "value": "..."}}, {"id": "7e216801-27ee-43b6-aa46-1a9888b5e7b7", "type": "pro-rect", "x": 1135, "y": 240, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(255, 230, 204)", "borderWidth": "1px", "borderColor": "rgb(215, 155, 0)"}, "zIndex": 1013, "text": {"x": 1135, "y": 240, "value": "缩放节点"}}, {"id": "c0cf0eb9-911d-403e-9112-6fd2fbeee9cd", "type": "pro-rect", "x": 1135, "y": 280, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(255, 230, 204)", "borderWidth": "1px", "borderColor": "rgb(215, 155, 0)"}, "zIndex": 1013, "text": {"x": 1135, "y": 280, "value": "组节点"}}, {"id": "78a12209-2306-4861-96dc-055c3abb3350", "type": "pro-rect", "x": 1135, "y": 325, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(255, 242, 204)", "borderWidth": "1px", "borderColor": "rgb(214, 182, 86)"}, "zIndex": 1013, "text": {"x": 1135, "y": 325, "value": "Bpmn插件"}}, {"id": "9517c208-2790-4a80-b5b6-f6dcf773f707", "type": "pro-text", "x": 105, "y": 150, "properties": {"fontSize": "16"}, "zIndex": 1033, "text": {"x": 105, "y": 150, "value": "核心模块"}}, {"id": "fd7e8fc5-af00-4b0d-8a8a-0f6192f2e26e", "type": "pro-rect", "x": 600, "y": 435, "properties": {"nodeSize": {"width": 900, "height": 50}, "borderStyle": "dashed"}, "zIndex": 1008}, {"id": "da8a9930-4281-478a-b0ca-1230dcf4933d", "type": "pro-rect", "x": 1135, "y": 430, "properties": {"nodeSize": {"width": 120, "height": 50}, "borderStyle": "dashed"}, "zIndex": 1008}, {"id": "ba8f64b8-c9ab-4ee1-a26d-21feb3868a40", "type": "pro-rect", "x": 215, "y": 435, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(220, 210, 230)", "borderWidth": "1px", "borderColor": "rgb(150, 115, 166)"}, "zIndex": 1035, "text": {"x": 215, "y": 435, "value": "Svg"}}, {"id": "980d85a7-c9bb-4f75-81dc-6e4f81b20b73", "type": "pro-rect", "x": 355, "y": 435, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(220, 210, 230)", "borderWidth": "1px", "borderColor": "rgb(150, 115, 166)"}, "zIndex": 1035, "text": {"x": 355, "y": 435, "value": "Preact"}}, {"id": "c1f212aa-6cae-42c7-9331-3395c42080ca", "type": "pro-rect", "x": 495, "y": 435, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(220, 210, 230)", "borderWidth": "1px", "borderColor": "rgb(150, 115, 166)"}, "zIndex": 1035, "text": {"x": 495, "y": 435, "value": "Mobx"}}, {"id": "653155fa-804c-4231-af5b-fa7c637b8802", "type": "pro-rect", "x": 1135, "y": 430, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(220, 210, 230)", "borderWidth": "1px", "borderColor": "rgb(150, 115, 166)"}, "zIndex": 1035, "text": {"x": 1135, "y": 430, "value": "@logicflow/core"}}, {"id": "36985733-2290-4a79-9981-de9d43765a3a", "type": "pro-rect", "x": 630, "y": 435, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(220, 210, 230)", "borderWidth": "1px", "borderColor": "rgb(150, 115, 166)"}, "zIndex": 1035, "text": {"x": 630, "y": 435, "value": "TS"}}, {"id": "77ce12e4-97b4-4627-866c-a8033c945fdf", "type": "pro-text", "x": 100, "y": 435, "properties": {"fontSize": "16"}, "zIndex": 1037, "text": {"x": 100, "y": 435, "value": "底层依赖"}}, {"id": "0c4e48ab-4d5e-4379-aefe-a65ad50b4f87", "type": "pro-rect", "x": 770, "y": 435, "properties": {"nodeSize": {"width": 100, "height": 30}, "borderStyle": "hidden", "backgroundColor": "rgb(220, 210, 230)", "borderWidth": "1px", "borderColor": "rgb(150, 115, 166)"}, "zIndex": 1035, "text": {"x": 770, "y": 435, "value": "mousetrap"}}, {"id": "a9280373-61ec-427b-bd10-6baff4000418", "type": "pro-rect", "x": 220, "y": 575, "properties": {"nodeSize": {"width": 110, "height": 30}, "borderStyle": "solid", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 220, "y": 575, "value": "RectNode"}}, {"id": "a5c20109-7887-4443-b8a7-aa0d29ea7f80", "type": "pro-rect", "x": 220, "y": 665, "properties": {"nodeSize": {"width": 110, "height": 30}, "borderStyle": "solid", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 220, "y": 665, "value": "ResizeNode"}}, {"id": "056459da-158d-44a4-b4d1-a39246e3572f", "type": "pro-rect", "x": 220, "y": 740, "properties": {"nodeSize": {"width": 110, "height": 30}, "borderStyle": "solid", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 220, "y": 740, "value": "ProcessNode"}}, {"id": "1e14c255-a9bd-4a07-ae95-7f41927c50bb", "type": "pro-text", "x": 100, "y": 545, "properties": {}, "zIndex": 1012, "text": {"x": 100, "y": 545, "value": "@logicflow/core"}}, {"id": "5ec8c58d-4fde-48d5-a3a4-869dd8370348", "type": "pro-text", "x": 100, "y": 660, "properties": {}, "zIndex": 1012, "text": {"x": 100, "y": 660, "value": "@logicflow/extension"}}, {"id": "4c022b06-8e34-4dec-ac52-9c671789a192", "type": "pro-text", "x": 95, "y": 745, "properties": {}, "zIndex": 1012, "text": {"x": 95, "y": 745, "value": "业务项目"}}, {"id": "accd35b5-6f41-48a1-a60e-6ad057506e93", "type": "pro-rect", "x": 220, "y": 510, "properties": {"nodeSize": {"width": 110, "height": 30}, "borderStyle": "solid", "backgroundColor": "rgb(213, 232, 212)", "borderWidth": "1px", "borderColor": "rgb(130, 179, 102)"}, "zIndex": 1013, "text": {"x": 220, "y": 510, "value": "BaseNode"}}, {"id": "2baf2f9b-e084-4c73-9bde-6d056c8cb9c6", "type": "pro-rect", "x": 415, "y": 540, "properties": {"nodeSize": {"width": 100, "height": 70}}, "zIndex": 1016}, {"id": "428c34e0-c6d4-4763-b1a8-98d9dd59ed9e", "type": "pro-rect", "x": 415, "y": 655, "properties": {"nodeSize": {"width": 100, "height": 70}, "backgroundColor": "rgb(255, 242, 204)", "borderWidth": "1px", "borderColor": "rgb(214, 182, 86)"}, "zIndex": 1018}, {"id": "78a7ecae-8062-48fe-9514-aebc4d42e91a", "type": "pro-rect", "x": 415, "y": 815, "properties": {"nodeSize": {"width": 180, "height": 130}, "backgroundColor": "rgb(248, 206, 204)", "borderWidth": "1px", "borderColor": "rgb(184, 84, 80)"}, "zIndex": 1018}, {"id": "ce1e8363-ea52-4178-8e02-f55b7e55dce8", "type": "pro-rect", "x": 280, "y": 540, "properties": {"nodeSize": {"width": 490, "height": 110}, "borderStyle": "dashed"}, "zIndex": 1008}, {"id": "e3a10dda-df3d-4116-95b7-5ebb3b23ade6", "type": "pro-rect", "x": 280, "y": 645, "properties": {"nodeSize": {"width": 490, "height": 100}, "borderStyle": "dashed"}, "zIndex": 1008}, {"id": "7f0b7315-1d2a-4b9b-8640-dfe08807255e", "type": "pro-rect", "x": 280, "y": 790, "properties": {"nodeSize": {"width": 490, "height": 190}, "borderStyle": "dashed"}, "zIndex": 1008}, {"id": "d3eee9ab-2bc8-44fc-9b3a-72545dd1be5c", "type": "pro-rect", "x": 415, "y": 685, "properties": {"nodeSize": {"width": 230, "height": 400}, "borderStyle": "dashed", "backgroundColor": "rgb(245, 245, 245)", "borderWidth": "1px", "borderColor": "rgb(102, 102, 102)"}, "zIndex": 1008}], "edges": [{"id": "305781e0-c703-47bb-b7f4-057556913fcb", "type": "polyline", "sourceNodeId": "a9280373-61ec-427b-bd10-6baff4000418", "targetNodeId": "a5c20109-7887-4443-b8a7-aa0d29ea7f80", "startPoint": {"x": 220, "y": 590}, "endPoint": {"x": 220, "y": 650}, "properties": {}, "zIndex": 1009, "pointsList": [{"x": 220, "y": 590}, {"x": 220, "y": 650}]}, {"id": "e94927d7-ab85-4085-ae3e-45f7e9d422f4", "type": "polyline", "sourceNodeId": "a5c20109-7887-4443-b8a7-aa0d29ea7f80", "targetNodeId": "056459da-158d-44a4-b4d1-a39246e3572f", "startPoint": {"x": 220, "y": 680}, "endPoint": {"x": 220, "y": 725}, "properties": {}, "zIndex": 1010, "pointsList": [{"x": 220, "y": 680}, {"x": 220, "y": 710}, {"x": 220, "y": 710}, {"x": 220, "y": 695}, {"x": 220, "y": 695}, {"x": 220, "y": 725}]}, {"id": "c2b19512-429c-4dbd-908e-3b8f47ca1954", "type": "polyline", "sourceNodeId": "accd35b5-6f41-48a1-a60e-6ad057506e93", "targetNodeId": "a9280373-61ec-427b-bd10-6baff4000418", "startPoint": {"x": 220, "y": 525}, "endPoint": {"x": 220, "y": 560}, "properties": {}, "zIndex": 1013, "pointsList": [{"x": 220, "y": 525}, {"x": 220, "y": 555}, {"x": 220, "y": 555}, {"x": 220, "y": 530}, {"x": 220, "y": 530}, {"x": 220, "y": 560}]}, {"id": "f647f078-c1ea-475b-92af-4fd02db7c028", "type": "polyline", "sourceNodeId": "2baf2f9b-e084-4c73-9bde-6d056c8cb9c6", "targetNodeId": "428c34e0-c6d4-4763-b1a8-98d9dd59ed9e", "startPoint": {"x": 415, "y": 575}, "endPoint": {"x": 415, "y": 620}, "properties": {}, "zIndex": 1022, "pointsList": [{"x": 415, "y": 575}, {"x": 415, "y": 605}, {"x": 415, "y": 605}, {"x": 415, "y": 590}, {"x": 415, "y": 590}, {"x": 415, "y": 620}]}, {"id": "e71d346e-3f08-49c5-ba6c-403b990c67e1", "type": "polyline", "sourceNodeId": "428c34e0-c6d4-4763-b1a8-98d9dd59ed9e", "targetNodeId": "78a7ecae-8062-48fe-9514-aebc4d42e91a", "startPoint": {"x": 415, "y": 690}, "endPoint": {"x": 415, "y": 750}, "properties": {}, "zIndex": 1023, "pointsList": [{"x": 415, "y": 690}, {"x": 415, "y": 750}]}]}