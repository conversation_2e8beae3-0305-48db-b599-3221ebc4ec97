<template>
  <div class="oss-uploader">

    <!-- 需保留action属性，实际使用自定义上传-->
    <el-upload
      ref="upload"
      :accept="accept"
      :before-upload="beforeUpload"
      :class="{ 'has-file': value }"
      :file-list=fileList
      :http-request="handleUpload"
      :limit="maxCount"
      :list-type:="listType"
      :on-error="handleError"
      :on-exceed="handleExceed"
      :show-file-list="true"
      action="#"
      class="upload-wrapper"
      drag
    >

      <!-- 操作遮罩层（正确位置） -->
      <div>
        <el-tooltip content="删除文件" effect="dark" placement="top">
          <el-button
            circle
            class="operate-btn"
            icon="el-icon-delete"
            type="danger"
            @click.stop="handleRemove"
          ></el-button>
        </el-tooltip>
      </div>

      <!-- 已上传图片预览 -->
      <el-image
        v-if="false"
        :preview-src-list="fileList"
        :src="value"
        class="preview-wrapper"
      >
        <!-- 错误状态 -->
        <div slot="error" class="image-slot">
          <i class="el-icon-picture-outline"></i>
        </div>

      </el-image>

      <!-- 未上传时的展示 -->
      <div v-else class="upload-area">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
          <div class="el-upload__tip">支持格式：{{ allowedExtensions.join(', ') }}</div>
        </div>
      </div>
    </el-upload>
  </div>
</template>

<script>
import myMessage from '@/common/message';
import api from '@/common/api';

export default {
  name: 'OssUpload',
  model: {
    prop: 'value',
    event: 'change',
  },
  props: {
    value: { type: String, default: '' },
    allowedExtensions: {
      type: Array,
      default: () => ['jpg', 'jpeg', 'png', 'gif'],
    },
    maxCount: {
      type: Number,
      default: 1,
    },
    accept: {
      type: String,
      default: 'image/*',
    },
    // text/picture/picture-card
    listType: {
      type: String,
      default: 'picture-card',
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    // 文件验证
    beforeUpload(file) {
      const extension = file.name.split('.').pop().toLowerCase();
      const isValid = this.allowedExtensions.includes(extension);
      if (!isValid) {
        myMessage.error(`仅支持上传 ${this.allowedExtensions.join(', ')} 格式文件`);
      }
      return isValid;
    },

    // 自定义上传逻辑
    async handleUpload({ file }) {
      try {
        // 1. 获取签名数据
        const res = await api.getOssSignature({ fileName: file.name });
        const signatureData = res.data.data;

        // 2. 准备表单数据
        const formData = new FormData();
        formData.append('key', signatureData.key);
        formData.append('policy', signatureData.policy);
        formData.append('OSSAccessKeyId', signatureData.accessId);
        formData.append('signature', signatureData.signature);
        formData.append('file', file);

        console.log('表单数据:', formData, signatureData.host);
        // 3. 直传OSS
        await this.$http.post(signatureData.host, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        });

        // 4. 更新绑定值
        this.$emit('change', signatureData.url);
        myMessage.success('上传成功');
      } catch (error) {
        myMessage.error('上传失败');
        console.error('上传错误:', error);
        throw error;
      }
    },

    // 处理文件超出限制
    handleExceed() {
      myMessage.warning(`最多允许上传 ${this.maxCount} 个文件`);
    },

    // 处理上传错误
    handleError(err) {
      console.error('上传错误:', err);
      myMessage.error('文件上传失败');
    },

    // 删除文件
    handleRemove() {
      this.$confirm('确定删除该文件吗？', '提示', {
        type: 'warning',
      }).then(() => {
        this.fileList = [];
        this.$emit('change', '');
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.oss-uploader {
  .upload-wrapper {
    ::v-deep .el-upload {
      width: 100%;
      height: 100%;
    }

    ::v-deep .el-upload-dragger {
      width: 100%;
      height: 100%;
      padding: 10px;
    }

    &.has-file ::v-deep .el-upload-dragger {
      border: none;
      padding: 0;
    }
  }

  .preview-wrapper {
    position: relative; /* 新增 */
    width: 100%;
    height: 300px;
    /* 关键修复：定位遮罩层 */
    .mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.3);
      opacity: 0;
      transition: opacity 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;
      pointer-events: none; /* 允许穿透点击 */
      .operate-btn {
        pointer-events: auto; /* 启用按钮点击 */
        transform: scale(0.8);
        transition: all 0.3s;
      }
    }

    &:hover .mask {
      opacity: 1;

      .operate-btn {
        transform: scale(1);
      }
    }

    /* 修复图片层级 */
    ::v-deep .el-image__inner {
      position: relative;
      z-index: 1;
    }
  }

  .upload-area {
    padding: 20px 0;

    .el-icon-upload {
      font-size: 48px;
      color: #909399;
      margin-bottom: 10px;
    }

    .el-upload__text {
      color: #606266;
      font-size: 14px;

      em {
        color: #409eff;
        font-style: normal;
      }
    }

    .el-upload__tip {
      color: #999;
      font-size: 12px;
      margin-top: 5px;
    }
  }
}
</style>
