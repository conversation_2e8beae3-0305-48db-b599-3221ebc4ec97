<template>
  <div v-if="isVisible" class="canvas-container">
    <canvas id="brushCanvas" :height="height" :width="width"></canvas>
    <canvas id="drawingCanvas" :height="height" :width="width"></canvas>
  </div>
</template>

<script>
import {createPen} from '@/components/tuc/js/pen';
import {createArr} from '@/components/tuc/js/arr';

export default {
  name: 'CodeRunner',
  props: {
    speed: {
      type: Number,
      default: 1,
    },
    width: {
      type: Number,
      default: 600,
    },
    height: {
      type: Number,
      default: 600,
    },
    isVisible: {
      type: Boolean,
      default: true,
    },
  },
  mounted() {
    this.runCode();
  },

  methods: {
    translateCppToJs(cppCode) {
      cppCode = cppCode.replace(/#include\s*<.*>/g, '');
      cppCode = cppCode.replace(/\bint\b/g, 'let');
      cppCode = cppCode.replace(/\bdouble\b/g, 'let');
      cppCode = cppCode.replace(/\bstring\b/g, 'let');
      cppCode = cppCode.replace(/\bchar\b/g, 'let');
      return cppCode.trim();
    },
    
    translateAwaitCode(code, ...keys) {
      for (let i = 0; i < keys.length; i++) {
        code = this.translateAwait(code, keys[i]);
      }
      return code;
    },

    translateAwait(code, key = 'arr.') {
      const escapedKey = key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

      const regex = new RegExp(
        `(?<!\\bawait\\s+)(\\b|\\s)(\\s*)(${escapedKey})`,
        'g',
      );

      return code.replace(regex, (match, boundary, whitespace, keyPart) => {
        return `${boundary}${whitespace}await ${keyPart}`;
      });
    },

    convertCppInput(cppLine) {
      const indent = cppLine.match(/^\s*/)[0];

      const cinPattern = /\bcin(\s*>>\s*[a-zA-Z_]+\s*)+;?/g;

      return cppLine.replace(cinPattern, (match) => {
        const hasSemicolon = match.trim().endsWith(';');

        const variables = [];
        const varExtractor = />>\s*([a-zA-Z_]\w*)/g;
        let varMatch;

        while ((varMatch = varExtractor.exec(match)) !== null) {
          variables.push(varMatch[1]);
        }

        return variables.map(varName =>
            `${indent}${varName} = pen.getUserInput()${hasSemicolon ? ';' : ''}`
        ).join('\n');
      });
    },

    async reloadComponent() {
      this.isVisible = false;
      await this.$nextTick();
      this.isVisible = true;
    },
    async runCode(inCode) {
      await this.reloadComponent();

      console.log('提交的代码 (inCode):', inCode);
      let runCode = inCode?.trim() ? inCode : '';
      runCode = this.translateCppToJs(runCode);
      console.log('翻译后的代码 (translatedCode):', runCode);
      runCode = this.convertCppInput(runCode);
      console.log('翻译后的代码 (convertCppInput):', runCode);
      runCode = this.translateAwaitCode(runCode, 'pen.', 'arr.');
      console.log('翻译后的代码 (translateAwaitCode):', runCode);


      const canvas = document.getElementById('drawingCanvas');
      const ctx = canvas.getContext('2d');

      const brushCanvas = document.getElementById('brushCanvas');
      const brushCtx = brushCanvas.getContext('2d');

      const pen = createPen(ctx, canvas, brushCtx, brushCanvas, () => this.speed);
      const arr = createArr(ctx, canvas, () => this.speed);

      ctx.clearRect(0, 0, canvas.width, canvas.height);
      brushCtx.clearRect(0, 0, brushCanvas.width, brushCanvas.height);

      pen.brushDraw();

      try {
        const asyncWrapper = `
            return (async (pen, arr) => {
                ${runCode}
            })(pen, arr);
        `;

        const asyncFn = new Function('pen, arr', asyncWrapper);
        await asyncFn(pen, arr);
      } catch (e) {
        console.error('Execution error:', e);
      }

    },
  },
};
</script>

<style scoped>
.canvas-container {
  position: relative;
  width: 600px;
  height: 600px;
  margin: 20px auto;
}

canvas {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  border: 2px solid #3498db;
  border-radius: 8px;
  background-color: transparent;
}
</style>
