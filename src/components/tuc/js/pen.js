/**
 * 绘图工具模块
 * @module Pen
 */

import {sleepMs} from "@/components/tuc/js/utils";

/** 画笔默认配置常量 */
const PEN_CONFIG = Object.freeze({
    BRUSH_RADIUS: 5,        // 画笔指示器半径
    BRUSH_LINE_WIDTH: 2,    // 画笔指示器线宽
    BRUSH_ANIMATION_DURATION: 10, // 笔刷旋转 基础动画时长(ms) 越大转的越慢
    FD_ANIMATION_DURATION: 100, // 笔刷前进速度 越小越快
    INITIAL_ANGLE: 0,     // 0 初始角度(度) 正上方
    ARROW_LENGTH: 20,       // 箭头长度
    ARROW_WIDTH: 10         // 箭头宽度
});

/** 颜色映射表 (数字到HEX颜色) */
const COLOR_MAP = Object.freeze({
    1: '#FF0000',
    2: '#FFA500',
    3: '#FFFF00',
    4: '#00FF00',
    5: '#00FFFF',
    6: '#0000FF',
    7: '#800080',
    8: '#FFC0CB',
    9: '#FFB6C1',
    10: '#FFFF66',
    11: '#87CEEB',
    12: '#B0C4DE',
    13: '#FFD700',
    14: '#5C3317',
    15: '#808080',
    16: '#000000'
});


/**
 * 几何计算工具类
 * @class Geometry
 */
class Geometry {
    /**
     * 角度转弧度（修正版：0°指向垂直向上）
     * @param {number} degrees - 用户坐标系角度（0°指向上方）
     * @returns {number} 数学坐标系弧度
     */
    static toRadians(degrees) {
        // 将用户角度转换为数学坐标系角度（减去90度实现坐标系旋转）
        return (degrees - 90) * Math.PI / 180;
    }

    /**
     * 计算终点坐标（适配修正后的坐标系）
     * @param {number} x - 起点X
     * @param {number} y - 起点Y
     * @param {number} angle - 数学坐标系弧度（已通过toRadians转换）
     * @param {number} distance - 移动距离
     * @returns {{x: number, y: number}} 终点坐标
     */
    static getEndPoint(x, y, angle, distance) {
        // 计算坐标时考虑canvas的Y轴向下特性
        return {
            x: x + Math.cos(angle) * distance,
            y: y + Math.sin(angle) * distance
        };
    }
}

/**
 * 创建画笔对象
 * @param {CanvasRenderingContext2D} ctx - 主画布上下文
 * @param {HTMLCanvasElement} canvas - 主画布元素
 * @param {CanvasRenderingContext2D} brushCtx - 画笔指示器画布上下文
 * @param {HTMLCanvasElement} brushCanvas - 画笔指示器画布元素
 * @param {Function} speedGetter - 获取动画速度的函数
 * @returns {PenObject} 画笔对象
 */
export function createPen(ctx, canvas, brushCtx, brushCanvas, speedGetter) {

    /** 画笔对象 */
    const pen = {
        /** @type {number} 当前X坐标 */
        x: canvas.width / 2,

        /** @type {number} 当前Y坐标 */
        y: canvas.height / 2,

        center: {
            x: canvas.width / 2,
            y: canvas.height / 2
        },

        /** @type {number} 当前笔刷颜色 */
        colorValue: COLOR_MAP[16],

        /** @type {number} 当前角度(度) */
        angle: PEN_CONFIG.INITIAL_ANGLE,

        /** @type {number} 当前速度 */
        speed: 90,

        /**
         * 异步绘制画笔方向指示器（箭头图案）
         * @param {number} x - 箭头笔尖的X坐标
         * @param {number} y - 箭头笔尖的Y坐标
         * @param {CanvasRenderingContext2D} brushCtx2d - 画布上下文对象
         * @param {number} [angle=this.angle] - 旋转角度（单位：度）
         * @param {string} [color='#FF0000'] - 指示器颜色（含透明度）
         * @returns {Promise<void>} - 异步绘制完成的Promise
         */
        async brushDraw(x = this.x, y = this.y, brushCtx2d = brushCtx, angle = this.angle, color = '#FF0000') {
            return new Promise((resolve) => {
                brushCtx2d.clearRect(0, 0, brushCanvas.width, brushCanvas.height);

                // 计算箭头各点坐标
                const radians = Geometry.toRadians(angle);
                const end = Geometry.getEndPoint(x, y, radians, -PEN_CONFIG.ARROW_LENGTH);
                const left = Geometry.getEndPoint(x, y, radians + Math.PI / 6, -PEN_CONFIG.ARROW_WIDTH);
                const right = Geometry.getEndPoint(x, y, radians - Math.PI / 6, -PEN_CONFIG.ARROW_WIDTH);

                // 绘制箭头图形
                brushCtx2d.beginPath();
                brushCtx2d.moveTo(end.x, end.y);
                brushCtx2d.lineTo(x, y);
                brushCtx2d.lineTo(left.x, left.y);
                brushCtx2d.moveTo(x, y);
                brushCtx2d.lineTo(right.x, right.y);

                // 设置笔触颜色
                brushCtx2d.strokeStyle = color;
                brushCtx2d.lineWidth = PEN_CONFIG.BRUSH_LINE_WIDTH

                brushCtx2d.stroke();
                brushCtx2d.closePath();

                // 立即解析Promise，表示绘制完成
                resolve();
            });
        },


        /**
         * 向前移动并画出指定距离,注意如果是斜线 不要有马赛克
         *
         * 根据上述注释 完成这个js函数，要求可以使用 await来等待 函数执行完成，
         * 例如 await this.fd(); this.runAbc(); 会等待 fd（）绘画完成后再执行下一步
         * 无论速度多快 前进多少距离，角度多偏多斜，都要保持线条平滑，不要有马赛克
         *
         * @param {number} distance - 前进距离(像素)
         * @param {CanvasRenderingContext2D} ctx2d - 画布对象
         * @param {number} [angle=this.angle] - 运行角度，例如 0 为垂直向上,90为垂直往右
         * @param {number} [speed=50] - 绘画速度，值越大则绘画速度越快，取值范围是1～100，若为100 则代表瞬间完成
         */
        async fd(distance, ctx2d = ctx, angle = this.angle) {
            distance = await Promise.resolve(distance);
            const startX = this.x;
            const startY = this.y;

            // 角度转弧度（0度指向正上方，顺时针方向）
            const radian = angle * Math.PI / 180;
            // 计算总位移分量（注意Y轴方向向下）
            const dxTotal = distance * Math.sin(radian);
            const dyTotal = -distance * Math.cos(radian);

            // 抗锯齿设置
            ctx2d.lineCap = 'round';
            ctx2d.lineJoin = 'round';
            ctx2d.imageSmoothingEnabled = true;

            // 速度参数处理（1~100映射为3000ms~0ms）
            const clampedSpeed = Math.min(Math.max(this.speed, 1), 100);
            const duration = clampedSpeed === 100 ? 0 : 3000 * (1 - clampedSpeed / 100);
            return new Promise(resolve => {
                if (duration === 0) {
                    // 瞬时完成模式
                    ctx2d.beginPath();
                    ctx2d.moveTo(startX, startY);
                    ctx2d.lineTo(startX + dxTotal, startY + dyTotal);
                    ctx2d.stroke();
                    this.x += dxTotal;
                    this.y += dyTotal;
                    resolve();
                    return;
                }

                let lastX = startX;
                let lastY = startY;
                const startTime = Date.now();

                const animate = () => {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // 计算当前坐标（使用线性插值）
                    const currentX = startX + dxTotal * progress;
                    const currentY = startY + dyTotal * progress;

                    // 绘制增量线段
                    ctx2d.beginPath();
                    ctx2d.moveTo(lastX, lastY);
                    ctx2d.lineTo(currentX, currentY);
                    ctx2d.stroke();
                    this.brushDraw()

                    // 更新记录点
                    lastX = currentX;
                    lastY = currentY;

                    // 更新坐标点
                    this.x = currentX;
                    this.y = currentY;

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        // 确保最终坐标精度
                        this.x = startX + dxTotal;
                        this.y = startY + dyTotal;
                        resolve();
                    }
                };

                requestAnimationFrame(animate);
            });
        },

        /**
         * 使速度为100，即瞬间完成
         * @returns {Promise<void>}
         */
        async finish() {
            this.speed = 100;
        },

        /**
         * 右转指定角度，带有可调速的旋转动画
         * @param {number} degree - 旋转角度(度)
         * @param {number} [speed=70] - 旋转速度 (1~100，越大越快，100为瞬间完成)
         */
        async rt(degree) {
            degree = await Promise.resolve(degree);

            // 参数边界约束
            const speed = Math.max(1, Math.min(100, this.speed));

            // 瞬时完成模式
            if (speed === 100) {
                this.angle += degree;
                await this.brushDraw();
                console.log("rt (instant)", this.angle);
                return;
            }

            // 动画参数
            const startAngle = this.angle;
            const startTime = performance.now();
            const BASE_MS_PER_DEG = 5; // 基础动画速度 (数值越小整体越快)
            const duration = (Math.abs(degree) * (100 - speed) / 100) * BASE_MS_PER_DEG;

            return new Promise((resolve) => {
                const animate = (currentTime) => {
                    const elapsed = currentTime - startTime;
                    let progress = duration > 0 ? Math.min(elapsed / duration, 1) : 1;

                    // 使用缓动函数提升动画效果
                    const smoothProgress = 1 - Math.pow(1 - progress, 2);
                    this.angle = startAngle + degree * smoothProgress;
                    this.brushDraw();

                    progress < 1 ? requestAnimationFrame(animate) : resolve();
                };
                requestAnimationFrame(animate);
            });
        },

        /**
         * 左转指定角度
         * @param {number} degree - 旋转角度(度)
         */
        async lt(degree) {
            degree = await Promise.resolve(degree);
            await this.rt(-degree);
        },


        /**
         * 设置画笔颜色
         * @param {number} colorCode - 颜色代码（1-16）
         */
        async color(colorCode) {
            colorCode = await Promise.resolve(colorCode);
            return new Promise((resolve) => {
                if (COLOR_MAP[colorCode]) {
                    this.colorValue = COLOR_MAP[colorCode];
                    ctx.strokeStyle = COLOR_MAP[colorCode];
                } else {
                    console.warn(`无效颜色代码: ${colorCode}`);
                }
                // 立即解析Promise，表示绘制完成
                resolve();
            });
        },

        /**
         * 弹窗获取用户输入内容
         * @returns {Promise<unknown>}
         */
        async getUserInput() {
            // return prompt('请输入内容：');
            return await new Promise((resolve) => {
                // 创建遮罩层
                const overlay = document.createElement('div');
                Object.assign(overlay.style, {
                    position: 'fixed',
                    top: '0',
                    left: '0',
                    width: '100%',
                    height: '100%',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                });
                // 创建对话框容器
                const dialog = document.createElement('div');
                Object.assign(dialog.style, {
                    backgroundColor: 'white',
                    padding: '20px',
                    borderRadius: '5px',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '10px',
                });
                // 创建输入框
                const input = document.createElement('input');
                input.type = 'text';
                input.placeholder = '请输入内容';
                input.style.padding = '5px';
                // 创建保存按钮
                const saveBtn = document.createElement('button');
                saveBtn.textContent = '保存';
                saveBtn.style.alignSelf = 'flex-end';
                // 组装元素
                dialog.appendChild(input);
                dialog.appendChild(saveBtn);
                overlay.appendChild(dialog);
                document.body.appendChild(overlay);
                // 处理保存操作
                const handleSave = () => {
                    const value = Number(input.value);
                    console.log('用户输入内容:', value); // 输出到控制台
                    document.body.removeChild(overlay);
                    resolve(value);
                };
                // 事件监听
                saveBtn.addEventListener('click', handleSave);
                input.addEventListener('keypress', (e) => e.key === 'Enter' && handleSave());
                overlay.addEventListener('click', (e) => {
                    if (e.target === overlay) {
                        document.body.removeChild(overlay);
                        resolve(null);
                    }
                });
                // 自动聚焦输入框
                input.focus();
            });
        },

        /**
         * 设置背景颜色
         * @param {number} colorCode - 颜色代码（1-16）
         */
        async background(colorCode) {
            colorCode = await Promise.resolve(colorCode);

            return new Promise((resolve) => {
                if (COLOR_MAP[colorCode]) {
                    const originalFill = ctx.fillStyle;
                    ctx.fillStyle = COLOR_MAP[colorCode];
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    ctx.fillStyle = originalFill;
                } else {
                    console.warn(`无效背景颜色代码: ${colorCode}`);
                }
                // 立即解析Promise，表示绘制完成
                resolve();
            });
        },

        /**
         * 绘制圆形（空心）
         * @param {number} radius - 圆形的半径
         */
        async circle(radius) {
            radius = await Promise.resolve(radius);

            return new Promise((resolve) => {
                ctx.beginPath();
                ctx.arc(this.x, this.y, radius, 0, Math.PI * 2);
                ctx.stroke();
                // 立即解析Promise，表示绘制完成
                resolve();
            });
        },

        /**
         * 绘制实心圆形
         * @param {number} radius - 圆形的半径
         */
        async circleF(radius) {
            radius = await Promise.resolve(radius);

            return new Promise((resolve) => {
                ctx.beginPath();
                ctx.fillStyle = this.colorValue; // 设置填充颜色
                ctx.arc(this.x, this.y, radius, 0, Math.PI * 2);
                ctx.fill();
                // 立即解析Promise，表示绘制完成
                resolve();
            });
        },

        /**
         * 绘制正方形（空心）
         * @param {number} size - 正方形的边长
         */
        async square(size) {
            size = await Promise.resolve(size);

            return new Promise((resolve) => {
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle * Math.PI / 180);
                const half = size / 2;
                ctx.beginPath();
                ctx.rect(-half, -half, size, size);
                ctx.stroke();
                ctx.restore();
                resolve();
            });
        },

        /**
         * 绘制实心正方形
         * @param {number} size - 正方形的边长
         */
        async squareF(size) {
            size = await Promise.resolve(size);

            return new Promise((resolve) => {
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle * Math.PI / 180);
                ctx.fillStyle = this.colorValue;
                const half = size / 2;
                ctx.beginPath();
                ctx.rect(-half, -half, size, size);
                ctx.fill();
                ctx.restore();
                resolve();
            });
        },

        /**
         * 绘制椭圆（空心）
         * @param {number} radius - 长轴半径
         * @param {number} height - 短轴半径
         */
        async ellipse(radius, height) {
            radius = await Promise.resolve(radius);
            height = await Promise.resolve(height);

            return new Promise((resolve) => {
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle * Math.PI / 180);
                ctx.beginPath();
                // 椭圆旋转90度使长轴朝上，与正方形方向一致
                ctx.ellipse(0, 0, radius, height, Math.PI / 2, 0, Math.PI * 2);
                ctx.stroke();
                ctx.restore();
                resolve();
            });
        },

        /**
         * 绘制实心椭圆
         * @param {number} radius - 长轴半径
         * @param {number} height - 短轴半径
         */
        async ellipseF(radius, height) {
            radius = await Promise.resolve(radius);
            height = await Promise.resolve(height);
            return new Promise((resolve) => {
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle * Math.PI / 180);
                ctx.fillStyle = this.colorValue;
                ctx.beginPath();
                ctx.ellipse(0, 0, radius, height, Math.PI / 2, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
                resolve();
            });
        },

        /**
         * 绘制空心矩形（正交方向）
         * @param {number} width - 矩形宽度
         * @param {number} height - 矩形高度
         */
        async orthogonal(width, height) {
            width = await Promise.resolve(width);
            height = await Promise.resolve(height);
            return new Promise((resolve) => {
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle * Math.PI / 180);
                ctx.beginPath();
                ctx.rect(-width / 2, -height / 2, width, height);
                ctx.stroke();
                ctx.restore();
                resolve();
            });
        },

        /**
         * 绘制实心矩形（正交方向）
         * @param {number} width - 矩形宽度
         * @param {number} height - 矩形高度
         */
        async orthogonalF(width, height) {
            width = await Promise.resolve(width);
            height = await Promise.resolve(height);
            return new Promise((resolve) => {
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle * Math.PI / 180);
                ctx.fillStyle = this.colorValue;
                ctx.fillRect(-width / 2, -height / 2, width, height);
                ctx.restore();
                resolve();
            });
        },

        /**
         * 绘制空心平行四边形
         * @param {number} width - 底边宽度
         * @param {number} height - 垂直高度
         */
        async parallelogram(width, height) {
            width = await Promise.resolve(width);
            height = await Promise.resolve(height);
            return new Promise((resolve) => {
                const skew = width / 4;
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle * Math.PI / 180);
                ctx.beginPath();
                ctx.moveTo(-width / 2 + skew / 2, height / 2);
                ctx.lineTo(-width / 2 - skew / 2, -height / 2);
                ctx.lineTo(width / 2 - skew / 2, -height / 2);
                ctx.lineTo(width / 2 + skew / 2, height / 2);
                ctx.closePath();
                ctx.stroke();
                ctx.restore();
                resolve();
            });
        },

        /**
         * 绘制实心平行四边形
         * @param {number} width - 底边宽度
         * @param {number} height - 垂直高度
         */
        async parallelogramF(width, height) {
            width = await Promise.resolve(width);
            height = await Promise.resolve(height);
            return new Promise((resolve) => {
                const skew = width / 4;
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle * Math.PI / 180);
                ctx.fillStyle = this.colorValue;
                ctx.beginPath();
                ctx.moveTo(-width / 2 + skew / 2, height / 2);
                ctx.lineTo(-width / 2 - skew / 2, -height / 2);
                ctx.lineTo(width / 2 - skew / 2, -height / 2);
                ctx.lineTo(width / 2 + skew / 2, height / 2);
                ctx.closePath();
                ctx.fill();
                ctx.restore();
                resolve();
            });
        },

        /**
         * 绘制空心三角形
         * @param {number} length - 底边长度
         */
        async triangle(length) {
            length = await Promise.resolve(length);
            return new Promise((resolve) => {
                const height = length * (Math.sqrt(3) / 2);
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle * Math.PI / 180);
                ctx.beginPath();
                ctx.moveTo(-length / 2, height / 3);
                ctx.lineTo(length / 2, height / 3);
                ctx.lineTo(0, -(2 * height) / 3);
                ctx.closePath();
                ctx.stroke();
                ctx.restore();
                resolve();
            });
        },

        /**
         * 绘制实心三角形
         * @param {number} length - 底边长度
         */
        async triangleF(length) {
            length = await Promise.resolve(length);
            return new Promise((resolve) => {
                const height = length * (Math.sqrt(3) / 2);
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle * Math.PI / 180);
                ctx.fillStyle = this.colorValue;
                ctx.beginPath();
                ctx.moveTo(-length / 2, height / 3);
                ctx.lineTo(length / 2, height / 3);
                ctx.lineTo(0, -(2 * height) / 3);
                ctx.closePath();
                ctx.fill();
                ctx.restore();
                resolve();
            });
        },

        /**
         * 绘制空心菱形
         * @param {number} length - 对角线长度
         */
        async rhombus(length) {
            length = await Promise.resolve(length);
            return new Promise((resolve) => {
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle * Math.PI / 180);
                ctx.beginPath();
                ctx.moveTo(0, -length / 2);
                ctx.lineTo(length / 2, 0);
                ctx.lineTo(0, length / 2);
                ctx.lineTo(-length / 2, 0);
                ctx.closePath();
                ctx.stroke();
                ctx.restore();
                resolve();
            });
        },

        /**
         * 绘制实心菱形
         * @param {number} length - 对角线长度
         */
        async rhombusF(length) {
            length = await Promise.resolve(length);

            return new Promise((resolve) => {
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle * Math.PI / 180);
                ctx.fillStyle = this.colorValue;
                ctx.beginPath();
                ctx.moveTo(0, -length / 2);
                ctx.lineTo(length / 2, 0);
                ctx.lineTo(0, length / 2);
                ctx.lineTo(-length / 2, 0);
                ctx.closePath();
                ctx.fill();
                ctx.restore();
                resolve();
            });
        },

        /**
         * 瞬时移动画笔位置
         * @param {number} x - 新坐标
         * @param {number} y - 新坐标
         */
        async move(x, y) {
            console.log(x);
            console.log(y);
            x = await Promise.resolve(x);
            y = await Promise.resolve(y);


            this.x = x;
            this.y = y;
            this.x = this.center.x + x;
            this.y = this.center.y - y;
            await this.brushDraw(this.x, this.y);
        },

        /**
         * 睡眠 n 毫秒
         * @param ms
         * @returns {Promise<void>}
         */
        async sleep(ms) {
            ms = await Promise.resolve(ms);

            console.log('睡眠 n 毫秒 开始', ms)
            await sleepMs(ms)
            console.log('睡眠 n 毫秒 结束', ms)
        },

        /**
         * 向前移动画笔位置
         * @param {number} distance - 移动距离
         */
        /**
         * 向前移动画笔位置（带动画效果）
         * @param {number} distance - 移动距离
         * @param {number} [speed=70] - 移动速度 (1~100，越大越快，100为瞬间完成)
         */
        async moveFd(distance, speed = this.speed) {
            distance = await Promise.resolve(distance);

            // 参数有效性检查
            speed = Math.max(1, Math.min(100, speed));

            const startX = this.x;
            const startY = this.y;
            const radian = Geometry.toRadians(this.angle);
            const targetX = startX + Math.cos(radian) * distance;
            const targetY = startY + Math.sin(radian) * distance;

            // 当速度设为100时直接完成
            if (speed >= 100) {
                this.x = targetX;
                this.y = targetY;
                await this.brushDraw(this.x, this.y);
                return;
            }

            // 动画参数计算
            const duration = (100 - speed) * 10; // 基础动画时长（毫秒）
            const startTime = performance.now();

            return new Promise((resolve) => {
                const animate = (currentTime) => {
                    // 计算动画进度（0到1之间）
                    const elapsed = currentTime - startTime;
                    let progress = Math.min(elapsed / duration, 1);

                    // 更新坐标
                    this.x = startX + Math.cos(radian) * distance * progress;
                    this.y = startY + Math.sin(radian) * distance * progress;

                    // 绘制当前帧
                    this.brushDraw(this.x, this.y);

                    // 继续动画或结束
                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    } else {
                        resolve();
                    }
                };

                // 启动动画
                requestAnimationFrame(animate);
            });
        },

        /**
         * 设置填充颜色
         * @param {number} colorCode - 颜色代码（1-16）
         */
        async fillColor(colorCode) {
            colorCode = await Promise.resolve(colorCode);

            return new Promise((resolve) => {
                if (COLOR_MAP[colorCode]) {
                    ctx.fillStyle = COLOR_MAP[colorCode];
                } else {
                    console.warn(`无效填充颜色代码: ${colorCode}`);
                }
                // 立即解析Promise，表示绘制完成
                resolve();
            });
        },

        /**
         * 设置线条宽度
         * @param {number} width - 线宽（像素）
         */
        async lineWidth(width) {
            width = await Promise.resolve(width);

            return new Promise((resolve) => {
                ctx.lineWidth = Math.max(1, width);
                // 立即解析Promise，表示绘制完成
                resolve();
            });
        },

        /**
         * 清除画布
         */
        async clear() {
            return new Promise((resolve) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                // 立即解析Promise，表示绘制完成
                resolve();
            });
        },

    };

    return pen;
}

/**
 * 画笔对象类型定义
 * @typedef {Object} PenObject
 * @property {number} x - X坐标
 * @property {number} y - Y坐标
 * @property {number} angle - 当前角度
 * // 基础方法
 * @property {Function} fd - 前进方法
 * @property {Function} rt - 右转方法
 * @property {Function} lt - 左转方法
 * @property {Function} brushDraw - 绘制画笔方向指示器
 * // 图形绘制
 * @property {Function} circle - 空心圆
 * @property {Function} circleF - 实心圆
 * @property {Function} ellipse - 空心椭圆
 * @property {Function} ellipseF - 实心椭圆
 * @property {Function} square - 空心正方形
 * @property {Function} squareF - 实心正方形
 * @property {Function} orthogonal - 空心矩形
 * @property {Function} orthogonalF - 实心矩形
 * @property {Function} parallelogram - 空心平行四边形
 * @property {Function} parallelogramF - 实心平行四边形
 * @property {Function} triangle - 空心三角形
 * @property {Function} triangleF - 实心三角形
 * @property {Function} rhombus - 空心菱形
 * @property {Function} rhombusF - 实心菱形
 * // 样式控制
 * @property {Function} color - 设置描边颜色
 * @property {Function} fillColor - 设置填充颜色
 * @property {Function} background - 设置背景颜色
 * @property {Function} lineWidth - 设置线宽
 * // 画布控制
 * @property {Function} clear - 清除画布
 * @property {Function} move - 移动到指定坐标
 * @property {Function} moveFd - 向前移动画笔
 */
