const CPlusTemplate = (code = '') =>
    `
#include <iostream>
#include <vector>
#include <functional>
using namespace std; // 自动识别 std 命名空间


struct Context {
    void beginPath() {}
    void moveTo(double, double) {}
    void lineTo(double, double) {}
    void stroke() {}
    void fill() {}
    void clear() {}
    void arc(double, double, double, double, double) {}
    void ellipse(double, double, double, double, double, double, double) {}
    void fillRect(double, double, double, double) {}
    void strokeRect(double, double, double, double) {}
};

void drawSquare(double, double, int, bool) {}

class Pen {
public:
    Pen() : x(0), y(0), angle(0) {}

    void fd(double distance) {}
    void rt(double degree) {}
    void lt(double degree) {}
    void rotateL(double num) {}
    void circle(double radius) {}
    void circleF(double radius) {}
    void move(int x, int y) {}
    void moveFd(double num) {}
    void color(int num) {}
    void sleep(int ms) {}
    void background(int num) {}
    void ellipse(double radius, double height) {}
    void ellipseF(double radius, double height) {}
    void square(double size) {}
    void squareF(double size) {}
    void orthogonal(double width, double height) {}
    void orthogonalF(double width, double height) {}
    void parallelogram(double width, double height) {}
    void parallelogramF(double width, double height) {}
    void triangle(double length) {}
    void triangleF(double length) {}
    void rhombus(double length) {}
    void rhombusF(double length) {}
    void finish() {}
    void clear() {}

private:
    double x, y, angle;
};

class Arr {
public:
    void build(int num, int value = 0) {}
    int begin() { return 0; }
    int end() { return 0; }
    void clear() {}
    void bright(int index) {}
    void setValue(int index, char c) {}
    int getValue(int index){ return 0;}

private:
    std::vector<int> grid;
};

int main() {
    Pen pen;
    Arr arr;

    ${code}

    return 0;
}
`

export default CPlusTemplate;
