import {sleepMs} from "@/components/tuc/js/utils";

/**
 * 数组可视化工具模块
 * @module ArrayVisualizer
 */


/** 默认配置常量 */
const ARRAY_CONFIG = Object.freeze({
    GRID_SIZE: 50,           // 网格单元尺寸
    HIGHLIGHT_DURATION: 2000, // 高亮持续时间(ms)
    SET_DURATION: 1000, // set值等待时间

    COLORS: {
        BACKGROUND: '#FFFFFF',  // 背景色
        HIGHLIGHT: '#FFD700',   // 高亮色
        TEXT: '#000000',        // 文字颜色
        BORDER: '#000000'       // 边框颜色
    },
    FONT: '20px Arial'        // 字体设置
});

/** 网格单元类型定义 */
const GridCell = Object.freeze({
    x: 0,          // X坐标
    y: 0,          // Y坐标
    value: null,   // 显示值
    highlighted: false // 高亮状态
});

/**
 * 几何计算工具类
 * @class Geometry
 */
class ArrayGeometry {
    /**
     * 计算网格位置
     * @param {number} index - 单元索引
     * @param {number} canvasWidth - 画布宽度
     * @param {number} gridSize - 网格尺寸
     * @returns {{x: number, y: number}} 坐标对象
     */
    static getGridPosition(index, canvasWidth, gridSize) {
        const column = index % Math.floor(canvasWidth / gridSize);
        const row = Math.floor(index / Math.floor(canvasWidth / gridSize));
        return {
            x: column * gridSize,
            y: row * gridSize
        };
    }
}

/**
 * 创建数组可视化器
 * @param {CanvasRenderingContext2D} ctx - 画布上下文
 * @param {HTMLCanvasElement} canvas - 画布元素
 * @param {Function} speedGetter - 返回动画延迟时间的函数
 * @param {typeof ARRAY_CONFIG} config - 数组可视化配置项
 * @returns {ArrayVisualizer} 可视化器对象
 */
export function createArr(ctx, canvas, speedGetter, config = ARRAY_CONFIG) {
    /** @type {Array<GridCell>} 网格数据存储数组 */
    const grid = [];
    /** @type {Array<Function>} 异步任务队列 */
    const tasks = [];

    // 初始化画布固定样式（优化重复设置）
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.font = config.FONT;

    /**
     * 绘制单个网格单元（优化了状态管理）
     * @private
     * @param {GridCell} cell - 需要绘制的网格单元
     */
    function drawCell(cell) {
        // 使用变量缓存区域减少重复计算
        const {x, y} = cell;
        const size = config.GRID_SIZE;
        const center = size / 2;

        // 清除旧内容
        ctx.clearRect(x, y, size, size);

        // 绘制背景
        ctx.fillStyle = cell.highlighted
            ? config.COLORS.HIGHLIGHT
            : config.COLORS.BACKGROUND;
        ctx.fillRect(x, y, size, size);

        // 绘制边框
        ctx.strokeStyle = config.COLORS.BORDER;
        ctx.strokeRect(x, y, size, size);

        // 绘制文本（使用已设置的全局字体样式）
        ctx.fillStyle = config.COLORS.TEXT;
        ctx.fillText(cell.value.toString(), x + center, y + center);
    }

    return {
        /**
         * 构建初始化数组
         * @param {number} size - 数组长度
         * @param {number} [initialValue] - 初始化数值（未指定时生成随机数）
         */
        async build(size, initialValue) {
            initialValue = await Promise.resolve(initialValue)
            size = await Promise.resolve(size)

            console.log('build', size, initialValue)

            grid.length = 0; // 清空现有数据
            for (let i = 0; i < size; i++) {
                const position = ArrayGeometry.getGridPosition(i, canvas.width, config.GRID_SIZE);
                const cell = {
                    ...position,
                    value: initialValue ?? Math.floor(Math.random() * 100),
                    highlighted: false
                };
                grid.push(cell);
            }
            console.log('build...  grid', grid)
            const batchCanvas = document.createElement('canvas'); // 离屏渲染优化
            batchCanvas.width = canvas.width;
            batchCanvas.height = canvas.height;
            const batchCtx = batchCanvas.getContext('2d');

            for (let i = 0; i < grid.length; i++) {
                const cell = grid[i];
                // 在离屏画布绘制
                drawCell.call({ctx: batchCtx}, cell);
            }

            // 批量绘制到主画布
            ctx.drawImage(batchCanvas, 0, 0);
        },

        /**
         * 获取起始迭代索引
         * @returns {number} 起始索引0
         */
        async begin() {
            return 0;
        },

        /**
         * 获取结束迭代索引
         * @returns {number} 数组长度
         */
        async end() {
            return grid.length;
        },

        /**
         * 清空画布及数据
         */
        async clear() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        },

        /**
         * 获取指定位置数值
         * @param {number} index - 元素索引
         * @returns {number}
         * @throws {Error} 索引越界时抛出错误
         */
        async getValue(index) {
            const resolvedIndex = await Promise.resolve(index)

            if (resolvedIndex < 0 || resolvedIndex >= grid.length) {
                throw new Error(`索引越界: ${resolvedIndex}`);
            }
            const value = grid[resolvedIndex].value;
            console.log('获取指定位置数值 getValue ', resolvedIndex, value)

            return value
        },

        /**
         * 高亮指定元素（支持动画队列）
         * @param {number} index - 元素索引
         * @returns {Promise<void>} 高亮动画完成的Promise
         */
        async bright(index) {
            const resolvedIndex = await Promise.resolve(index)

            const toggleHighlight = (state) => {
                grid[resolvedIndex].highlighted = state;
                drawCell(grid[resolvedIndex]);
            };
            console.log('高亮指定元素 bright', resolvedIndex)

            // 高亮状态切换
            toggleHighlight(true);
            await sleepMs(ARRAY_CONFIG.HIGHLIGHT_DURATION); // 等待 2 秒
            toggleHighlight(false);
        },

        /**
         * 设置元素值
         * @param {number} index - 元素索引
         * @param {string|number} value - 新值
         */
        async setValue(index, value) {
            // 无论 value 是否是 Promise，都等待解析
            const resolvedValue = await Promise.resolve(value)
            const resolvedIndex = await Promise.resolve(index)
            console.log('设置元素值 setValue', index, value, typeof value)

            if (resolvedIndex < 0 || resolvedIndex >= grid.length) {
                console.warn(`无效索引: ${resolvedIndex}`);
                return
            }
            await sleepMs(ARRAY_CONFIG.SET_DURATION)

            grid[resolvedIndex].value = resolvedValue;
            drawCell(grid[resolvedIndex]);
        },

    };
}


/**
 * 网格单元类型定义
 * @typedef {Object} GridCell
 * @property {number} x - 横坐标
 * @property {number} y - 纵坐标
 * @property {number} value - 存储的数值
 * @property {boolean} highlighted - 高亮状态
 */

/**
 * 数组可视化器类型定义
 * @typedef {Object} ArrayVisualizer
 * @property {Function} build
 * @property {Function} begin
 * @property {Function} end
 * @property {Function} clear
 * @property {Function} getValue
 * @property {Function} setValue
 * @property {Function} bright
 */
