<template>
  <el-card>
    <problem-base
        :all-tags-tmp="allTagsTmp"
        :contest-id="contestID"
        :contest-problem="contestProblem"
        :input-visible.sync="inputVisible"
        :problem="problem"
        :problem-tags.sync="problemTags"
        :rules="rules"
        :tag-input.sync="tagInput"
        :title="title"
    />
    <el-form ref="form" :model="problem" :rules="rules" label-position="top" label-width="70px">

      <el-row v-if="contestID" :gutter="20">
        <el-col :md="12" :xs="24">
          <el-form-item :label="$t('m.Contest_Display_Title')" required>
            <el-input v-model="contestProblem.displayTitle" :placeholder="$t('m.Contest_Display_Title')"></el-input>
          </el-form-item>
        </el-col>

        <el-col :md="12" :xs="24">
          <el-form-item :label="$t('m.Contest_Display_ID')" required>
            <el-input v-model="contestProblem.displayId" :placeholder="$t('m.Contest_Display_ID')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>


      <el-row :gutter="20">
        <el-col :md="6" :xs="24">
          <el-form-item :label="$t('m.Time_Limit') + '(ms)'" required>
            <el-input v-model="problem.timeLimit" :disabled="problem.isRemote" :placeholder="$t('m.Time_Limit')"
                      type="Number"></el-input>
          </el-form-item>
        </el-col>
        <el-col :md="6" :xs="24">
          <el-form-item :label="$t('m.Memory_Limit') + '(mb)'" required>
            <el-input v-model="problem.memoryLimit" :disabled="problem.isRemote" :placeholder="$t('m.Memory_Limit')"
                      type="Number"></el-input>
          </el-form-item>
        </el-col>
        <el-col :md="6" :xs="24">
          <el-form-item :label="$t('m.Stack_Limit') + '(mb)'" required>
            <el-input v-model="problem.stackLimit" :disabled="problem.isRemote" :placeholder="$t('m.Stack_Limit')"
                      type="Number"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="$t('m.Input')" prop="input_description" required>
            <Editor :value.sync="problem.input"></Editor>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('m.Output')" prop="output_description" required>
            <Editor :value.sync="problem.output"></Editor>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="$t('m.Hint')" style="margin-top: 20px">
            <Editor :value.sync="problem.hint"></Editor>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">

        <el-col :md="4" :xs="24">
          <el-form-item :label="$t('m.Type')">
            <el-radio-group v-model="problem.type" :disabled="disableRuleType || problem.isRemote"
                            @change="problemTypeChange">
              <el-radio :label="0">ACM</el-radio>
              <el-radio :label="1">OI</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :md="4" :xs="24">
          <el-form-item :label="$t('m.Code_Shareable')">
            <el-switch v-model="problem.codeShare" active-text="" inactive-text="">
            </el-switch>
          </el-form-item>
        </el-col>

      </el-row>
      <el-row>
        <el-col :md="24" :xs="24">
          <el-form-item :error="error.languages" :label="$t('m.Languages')" required>
            <el-checkbox-group v-model="problemLanguages">
              <el-tooltip v-for="lang in allLanguage" :key="lang.name" :content="lang.description" class="spj-radio"
                          effect="dark" placement="top-start">
                <el-checkbox :label="lang.name"></el-checkbox>
              </el-tooltip>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>

      <div>
        <div class="panel-title home-title">
          {{ $t('m.Problem_Examples') }}
          <el-popover placement="right" trigger="hover">
            <p>
              {{ $t('m.Problem_Examples_Desc') }}
            </p>
            <i slot="reference" class="el-icon-question"></i>
          </el-popover>
        </div>
        <el-form-item v-for="(example, index) in problem.examples" :key="'example' + index">
          <Accordion :index="index" :isOpen="example.isOpen ? true : false"
                     :title="$t('m.Problem_Example') + (index + 1)" @changeVisible="changeExampleVisible">
            <el-button slot="header" icon="el-icon-delete" size="small" type="danger" @click="deleteExample(index)">
              {{ $t('m.Delete') }}
            </el-button>
            <el-row :gutter="20">
              <el-col :md="12" :xs="24">
                <el-form-item :label="$t('m.Example_Input')" required>
                  <el-input v-model="example.input" :placeholder="$t('m.Example_Input')" :rows="5"
                            style="white-space: pre-line" type="textarea">
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :md="12" :xs="24">
                <el-form-item :label="$t('m.Example_Output')" required>
                  <el-input v-model="example.output" :placeholder="$t('m.Example_Output')" :rows="5" type="textarea">
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </Accordion>
        </el-form-item>
      </div>

      <div class="add-example-btn">
        <el-button class="add-examples" icon="el-icon-plus" type="small" @click="addExample()">{{
            $t('m.Add_Example')
          }}
        </el-button>
      </div>

      <template v-if="!problem.isRemote">
        <div class="panel-title home-title">
          {{ $t('m.Judge_Extra_File') }}
          <el-popover placement="right" trigger="hover">
            <p>{{ $t('m.Judge_Extra_File_Tips1') }}</p>
            <p>{{ $t('m.Judge_Extra_File_Tips2') }}</p>
            <i slot="reference" class="el-icon-question"></i>
          </el-popover>
        </div>

        <el-row :gutter="20">
          <el-col :md="12" :xs="24">
            <el-form-item>
              <el-checkbox v-model="addUserExtraFile">{{
                  $t('m.User_Program')
                }}
              </el-checkbox>
            </el-form-item>
            <el-form-item v-if="addUserExtraFile">
              <AddExtraFile :files.sync="userExtraFile" type="user" @deleteFile="deleteFile"
                            @upsertFile="upsertFile"></AddExtraFile>
            </el-form-item>
          </el-col>
          <el-col :md="12" :xs="24">
            <el-form-item>
              <el-checkbox v-model="addJudgeExtraFile">{{
                  $t('m.SPJ_Or_Interactive_Program')
                }}
              </el-checkbox>
            </el-form-item>
            <el-form-item v-if="addJudgeExtraFile">
              <AddExtraFile :files.sync="judgeExtraFile" type="judge" @deleteFile="deleteFile"
                            @upsertFile="upsertFile"></AddExtraFile>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <template v-if="!problem.isRemote">
        <div class="panel-title home-title">
          {{ $t('m.Read_Write_Mode') }}
        </div>
        <el-row :gutter="20">
          <el-col :md="8" :xs="24">
            <el-form-item required>
              <el-radio-group v-model="problem.isFileIO">
                <el-radio :label="false">
                  {{ $t('m.Standard_IO') }}
                </el-radio>
                <el-radio :label="true">
                  {{ $t('m.File_IO') }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :md="8" :xs="24">
            <el-form-item v-if="problem.isFileIO">
              <el-input v-model="problem.ioReadFileName" size="small">
                <template slot="prepend">{{ $t('m.Input_File_Name') }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :md="8" :xs="24">
            <el-form-item v-if="problem.isFileIO">
              <el-input v-model="problem.ioWriteFileName" size="small">
                <template slot="prepend">{{ $t('m.Output_File_Name') }}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <template v-if="!problem.isRemote">
        <div class="panel-title home-title">
          {{ $t('m.Judge_Mode') }}
          <el-popover placement="right" trigger="hover">
            <p>1. {{ $t('m.General_Judge_Mode_Tips') }}</p>
            <p>2. {{ $t('m.Special_Judge_Mode_Tips') }}</p>
            <p>3. {{ $t('m.Interactive_Judge_Mode_Tips') }}</p>
            <i slot="reference" class="el-icon-question"></i>
          </el-popover>
        </div>
        <el-form-item :error="error.spj" label="">
          <el-col :span="24">
            <el-radio-group v-model="problem.judgeMode" @change="switchMode">
              <el-radio label="default">{{ $t('m.General_Judge') }}</el-radio>
              <el-radio label="spj">{{ $t('m.Special_Judge') }}</el-radio>
              <el-radio label="interactive">{{
                  $t('m.Interactive_Judge')
                }}
              </el-radio>
            </el-radio-group>
          </el-col>
        </el-form-item>
        <el-form-item v-if="problem.judgeMode != 'default'">
          <Accordion :title="
                problem.judgeMode == 'spj'
                  ? $t('m.Special_Judge_Code')
                  : $t('m.Interactive_Judge_Code')
              ">
            <template slot="header">
                <span style="margin-right:5px;">{{
                    problem.judgeMode == 'spj'
                        ? $t('m.SPJ_Language')
                        : $t('m.Interactive_Language')
                  }}：</span>
              <el-radio-group v-model="problem.spjLanguage">
                <el-tooltip v-for="lang in allSpjLanguage" :key="lang.name" :content="lang.description"
                            class="spj-radio" effect="dark" placement="top-start">
                  <el-radio :label="lang.name">{{ lang.name }}</el-radio>
                </el-tooltip>
              </el-radio-group>
              <el-button :loading="loadingCompile" icon="el-icon-folder-checked" size="small" style="margin-left:10px"
                         type="primary" @click="compileSPJ">{{ $t('m.Compile') }}
              </el-button>
            </template>
            <code-mirror v-model="problem.spjCode" :mode="spjMode"></code-mirror>
          </Accordion>
        </el-form-item>
      </template>

      <div class="panel-title home-title">{{ $t('m.Code_Template') }}</div>
      <el-form-item>
        <el-row>
          <el-col v-for="(v, k) in codeTemplate" :key="'template' + k" :span="24">
            <el-form-item>
              <el-checkbox v-model="v.status">{{ k }}</el-checkbox>
              <div v-if="v.status">
                <code-mirror v-model="v.code" :mode="v.mode"></code-mirror>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <el-row v-if="!problem.isRemote" :gutter="20">
        <div class="panel-title home-title">
          {{ $t('m.Judge_Samples') }}
          <el-popover placement="right" trigger="hover">
            <p>{{ $t('m.Sample_Tips') }}</p>
            <i slot="reference" class="el-icon-question"></i>
          </el-popover>
        </div>

        <el-form-item required>
          <el-radio-group v-model="problem.judgeCaseMode" @change="switchJudgeCaseMode">
            <el-radio :label="JUDGE_CASE_MODE.DEFAULT">
              {{ problem.type == 1 ? $t('m.OI_Judge_Case_Default_Mode') : $t('m.ACM_Judge_Case_Default_Mode') }}
            </el-radio>
            <template v-if="problem.type == 1">
              <el-radio :label="JUDGE_CASE_MODE.SUBTASK_LOWEST">{{
                  $t('m.Judge_Case_Subtask_Lowest_Mode')
                }}
              </el-radio>
              <el-radio :label="JUDGE_CASE_MODE.SUBTASK_AVERAGE">{{
                  $t('m.Judge_Case_Subtask_Average_Mode')
                }}
              </el-radio>
            </template>
            <template v-else>
              <el-radio :label="JUDGE_CASE_MODE.ERGODIC_WITHOUT_ERROR">
                {{ $t('m.Judge_Case_Ergodic_Without_Error_Mode') }}
              </el-radio>
            </template>
          </el-radio-group>
        </el-form-item>

        <el-form-item required>
          <el-switch v-model="problem.isUploadCase" :active-text="$t('m.Use_Upload_File')"
                     :inactive-text="$t('m.Use_Manual_Input')" style="margin: 10px 0">
          </el-switch>
        </el-form-item>

        <div v-show="problem.isUploadCase">
          <el-col :span="24">
            <el-form-item :error="error.testcase">
              <el-upload :action="uploadFileUrl+'?mode='+problem.judgeCaseMode" :on-error="uploadFailed"
                         :on-success="uploadSucceeded" :show-file-list="true" name="file">
                <el-button icon="el-icon-upload" size="small" type="primary">{{ $t('m.Choose_File') }}
                </el-button>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <vxe-table ref="xTable" :data="problem.testCaseScore" :sort-config="{trigger: 'cell',
                defaultSort: {field: 'groupNum', order: 'asc'},
                orders: ['desc', 'asc', null],
                sortMethod: customSortMethod}" align="center" auto-resize stripe>
              <vxe-table-column field="index" title="#" width="60"></vxe-table-column>
              <vxe-table-column :title="$t('m.Sample_Input_File')" field="input" min-width="100">
              </vxe-table-column>
              <vxe-table-column :title="$t('m.Sample_Output_File')" field="output" min-width="100">
              </vxe-table-column>
              <vxe-table-column v-if="problem.judgeCaseMode == JUDGE_CASE_MODE.SUBTASK_LOWEST
                    || problem.judgeCaseMode == JUDGE_CASE_MODE.SUBTASK_AVERAGE" :title="$t('m.Sample_Group_Num')"
                                field="groupNum" min-width="100" sortable>
                <template v-slot="{ row }">
                  <el-input v-model="row.groupNum" size="small" type="number" @change="sortTestCaseList"></el-input>
                </template>
              </vxe-table-column>
              <vxe-table-column v-if="problem.type == 1" :title="$t('m.Score')" field="score" min-width="100">
                <template v-slot="{ row }">
                  <el-input v-model="row.score" :disabled="problem.type != 1" :placeholder="$t('m.Score')"
                            size="small" type="number">
                  </el-input>
                </template>
              </vxe-table-column>
            </vxe-table>
          </el-col>
        </div>

        <div v-show="!problem.isUploadCase">
          <el-form-item v-for="(sample, index) in problemSamples" :key="'sample' + index">
            <Accordion :index="index" :isOpen="sample.isOpen ? true : false"
                       :title="$t('m.Problem_Sample') + (sample.index)" @changeVisible="changeSampleVisible">
              <el-button slot="header" icon="el-icon-delete" size="small" type="danger" @click="deleteSample(index)">
                {{ $t('m.Delete') }}
              </el-button>
              <el-row :gutter="20">
                <el-col :md="12" :xs="24">
                  <el-form-item :label="$t('m.Sample_Input')" required>
                    <el-input v-model="sample.input" :placeholder="$t('m.Sample_Input')" :rows="5" type="textarea">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="12" :xs="24">
                  <el-form-item :label="$t('m.Sample_Output')" required>
                    <el-input v-model="sample.output" :placeholder="$t('m.Sample_Output')" :rows="5" type="textarea">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col v-if="problem.type == 1" :span="24">
                  <el-form-item :label="$t('m.Score')">
                    <el-input v-model="sample.score" :placeholder="$t('m.Score')" size="small" type="number">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col v-show="problem.judgeCaseMode == JUDGE_CASE_MODE.SUBTASK_LOWEST
                       || problem.judgeCaseMode == JUDGE_CASE_MODE.SUBTASK_AVERAGE" :span="24">
                  <el-form-item :label="$t('m.Sample_Group_Num')">
                    <el-input v-model="sample.groupNum" :placeholder="$t('m.Sample_Group_Num')" size="small"
                              type="number" @change="sortManualProblemSampleList">
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </Accordion>
          </el-form-item>

          <div class="add-sample-btn">
            <el-button class="add-samples" icon="el-icon-plus" type="small" @click="addSample()">{{
                $t('m.Add_Sample')
              }}
            </el-button>
          </div>
        </div>
      </el-row>

      <el-form-item :label="$t('m.Source')">
        <el-input v-model="problem.source" :placeholder="$t('m.Source')"></el-input>
      </el-form-item>

      <el-form-item v-if="!problem.isRemote" :label="$t('m.Auto_Remove_the_Blank_at_the_End_of_Code')">
        <el-switch v-model="problem.isRemoveEndBlank" active-text="" inactive-text="">
        </el-switch>
      </el-form-item>

      <el-form-item :label="$t('m.Publish_the_Judging_Result_of_Test_Data')">
        <el-switch v-model="problem.openCaseResult" active-text="" inactive-text="">
        </el-switch>
      </el-form-item>

      <el-button size="small" type="primary" @click.native="submit()">{{
          $t('m.Save')
        }}
      </el-button>
    </el-form>
  </el-card>
</template>

<script>
import Accordion from "@/components/admin/Accordion.vue";
import AddExtraFile from "@/components/admin/AddExtraFile.vue";
import CodeMirror from "@/components/admin/CodeMirror.vue";
import Editor from "@/components/admin/Editor.vue";
import {JUDGE_CASE_MODE} from "@/common/constants";
import utils from "@/common/utils";
import api from "@/common/api";
import myMessage from "@/common/message";
import ProblemBase from "@/components/tuc/problem/ProblemBase.vue";

export default {
  name: "ProgrammerProblem",
  components: {
    ProblemBase,
    Editor,
    Accordion,
    AddExtraFile,
    CodeMirror,
  },
  props: {
    allTagsTmp: Array,
    contestID: [String, Number],
    contestProblem: Object,
    problem: Object,
    allTags: [],

    problemTags: Array,
    // 添加这个prop定义
    problemLanguages: {
      type: Array,
      required: true
    },
    tagInput: {
      type: String,
      default: ''
    },
    inputVisible: {
      type: Boolean,
      default: false
    },
    rules: Object,
    title: String,
    allLanguage: Array,
    allSpjLanguage: Array,
    userExtraFile: Object,
    judgeExtraFile: Object,
    addUserExtraFile: Boolean,
    addJudgeExtraFile: Boolean,
    problemSamples: Array,
    codeTemplate: Object,
  },
  data() {
    return {
      JUDGE_CASE_MODE: Object.assign({}, JUDGE_CASE_MODE),
      loadingCompile: false,
      spjMode: '',
      error: {
        tags: '',
        spj: '',
        languages: '',
        testCase: '',
      },
      sampleIndex: 1,
    };
  },
  watch: {
    $route() {
      this.routeName = this.$route.name;
      if (
          this.routeName === 'admin-edit-problem' ||
          this.routeName === 'admin-edit-contest-problem'
      ) {
        this.mode = 'edit';
      } else {
        this.mode = 'add';
      }
      this.$refs.form.resetFields();
      this.problem = this.reProblem;
      this.problemTags = []; //指定问题的标签列表
      this.problemLanguages = []; //指定问题的编程语言列表
      this.problemSamples = [];
      this.problemCodeTemplate = [];
      this.codeTemplate = [];
      this.init();
    },

    problemLanguages(newVal) {
      let data = {};
      // use deep copy to avoid infinite loop
      let languages = JSON.parse(JSON.stringify(newVal)).sort();
      for (let item of languages) {
        if (this.codeTemplate[item] === undefined) {
          let langConfig = this.allLanguage.find((lang) => {
            return lang.name === item;
          });
          let codeTemp;
          let problemCodeTemplate = this.problemCodeTemplate;
          if (problemCodeTemplate) {
            codeTemp = problemCodeTemplate.find((temp) => {
              return temp.lid == langConfig.id;
            });
          }
          if (codeTemp == undefined) {
            data[item] = {
              id: null,
              status: false,
              code: langConfig.codeTemplate,
              mode: langConfig.contentType,
            };
          } else {
            data[item] = {
              id: codeTemp.id,
              status: true,
              code: codeTemp.code,
              mode: langConfig.contentType,
            };
          }
        } else {
          data[item] = this.codeTemplate[item];
        }
      }
      this.codeTemplate = data;
    },

    'problem.spjLanguage'(newVal) {
      if (this.allSpjLanguage.length && this.problem.judgeMode != 'default') {
        this.spjMode = this.allSpjLanguage.find((item) => {
          return item.name == this.problem.spjLanguage && item.isSpj == true;
        })['contentType'];
      }
    },
  },
  methods: {
    spjRecord: undefined,
    init() {
      this.sampleIndex = 1;
      if (this.mode === 'edit') {
        this.pid = this.$route.params.problemId;
        this.backPath = this.$route.query.back;
        this.title = this.$i18n.t('m.Edit_Problem');
        let funcName = {
          'admin-edit-problem': 'admin_getProblem',
          'admin-edit-contest-problem': 'admin_getContestProblem',
        }[this.routeName];
        api[funcName](this.pid).then((problemRes) => {
          let data = problemRes.data.data;
          data.spjCompileOk = false;
          data.uploadTestcaseDir = '';
          data.testCaseScore = [];
          if (!data.spjCode) {
            data.spjCode = '';
          }
          data.spjLanguage = data.spjLanguage || 'C';
          this.spjRecord.spjLanguage = data.spjLanguage;
          this.spjRecord.spjCode = data.spjCode;
          this.judgeCaseModeRecord = data.judgeCaseModeRecord;
          this.problem = data;
          this.problem['examples'] = utils.stringToExamples(data.examples);
          if (this.problem['examples'].length > 0) {
            this.problem['examples'][0]['isOpen'] = true;
          }
          this.testCaseUploaded = true;
          if (this.problem.userExtraFile) {
            this.addUserExtraFile = true;
            this.userExtraFile = JSON.parse(this.problem.userExtraFile);
          }
          if (this.problem.judgeExtraFile) {
            this.addJudgeExtraFile = true;
            this.judgeExtraFile = JSON.parse(this.problem.judgeExtraFile);
          }
          api
              .admin_getProblemCases(this.pid, this.problem.isUploadCase)
              .then((res) => {
                if (this.problem.isUploadCase) {
                  this.problem.testCaseScore = res.data.data;
                  this.problem.testCaseScore.forEach((item, index) => {
                    item.index = index + 1;
                  });
                  if (this.$refs.xTable != undefined) {
                    this.$refs.xTable.sort('groupNum', 'asc');
                  }
                } else {
                  this.problemSamples = res.data.data;
                  if (
                      this.problemSamples != null &&
                      this.problemSamples.length > 0
                  ) {
                    this.problemSamples[0]['isOpen'] = true;
                    this.problemSamples.forEach((item, index) => {
                      item.index = index + 1;
                    });
                    this.sampleIndex = this.problemSamples.length + 1;
                  }
                }
              });
        });
        if (funcName === 'admin_getContestProblem') {
          api
              .admin_getContestProblemInfo(this.pid, this.contestID)
              .then((res) => {
                this.contestProblem = res.data.data;
              });
        }
        this.getProblemCodeTemplateAndLanguage();

        api.admin_getProblemTags(this.pid).then((res) => {
          this.problemTags = res.data.data;
        });
      } else {
        this.addExample();
        this.testCaseUploaded = false;
        this.title = this.$i18n.t('m.Create_Problem');
        for (let item of this.allLanguage) {
          this.problemLanguages.push(item.name);
        }
      }
    },

    async getProblemCodeTemplateAndLanguage() {
      const that = this;
      await api.getProblemCodeTemplate(that.pid).then((res) => {
        that.problemCodeTemplate = res.data.data;
      });
      api.getProblemLanguages(that.pid).then((res) => {
        let Languages = res.data.data;
        for (let i = 0; i < Languages.length; i++) {
          that.problemLanguages.push(Languages[i].name);
        }
      });
    },

    switchMode(mode) {
      let modeName = 'General_Judge';
      let modeTips = 'General_Judge_Mode_Tips';
      if (mode == 'spj') {
        modeName = 'Special_Judge';
        modeTips = 'Special_Judge_Mode_Tips';
      } else if (mode == 'interactive') {
        modeName = 'Interactive_Judge';
        modeTips = 'Interactive_Judge_Mode_Tips';
      }
      const h = this.$createElement;
      this.$msgbox({
        title: this.$i18n.t('m.' + modeName),
        message: h('div', null, [
          h(
              'p',
              {style: 'text-align: center;font-weight:bolder;color:red'},
              this.$i18n.t('m.Change_Judge_Mode'),
          ),
          h('br', null, null),
          h(
              'p',
              {style: 'font-weight:bolder'},
              this.$i18n.t('m.' + modeTips),
          ),
        ]),
      });
    },
    querySearch(queryString, cb) {
      var ojName = 'ME';
      if (this.problem.isRemote) {
        ojName = this.problem.problemId.split('-')[0];
      }
      var restaurants = this.allTagsTmp.filter((item) => item.oj == ojName);
      var results = queryString
          ? restaurants.filter(
              (item) =>
                  item.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0,
          )
          : restaurants;
      cb(results);
    },
    changeContent(newVal) {
      this.announcement.content = newVal;
    },
    getLevelName(difficulty) {
      return utils.getLevelName(difficulty);
    },

    selectTag(item) {
      for (var i = 0; i < this.problemTags.length; i++) {
        if (this.problemTags[i].name == item.value) {
          myMessage.warning(this.$i18n.t('m.Add_Tag_Error'));
          this.tagInput = '';
          return;
        }
      }
      this.tagInput = item.value;
    },
    addTag(item) {
      let newTag = {
        name: this.tagInput,
      };
      if (this.tagInput) {
        for (var i = 0; i < this.problemTags.length; i++) {
          if (this.problemTags[i].name == this.tagInput) {
            myMessage.warning(this.$i18n.t('m.Add_Tag_Error'));
            this.tagInput = '';
            return;
          }
        }
        this.problemTags.push(newTag);
        this.inputVisible = false;
        this.tagInput = '';
      }
    },

    // 根据tag name从题目的tags列表中移除
    closeTag(tag) {
      this.problemTags.splice(
          this.problemTags.map((item) => item.name).indexOf(tag),
          1,
      );
    },

    deleteFile(type, name) {
      if (type == 'user') {
        this.$delete(this.userExtraFile, name);
      } else {
        this.$delete(this.judgeExtraFile, name);
      }
    },

    upsertFile(type, name, oldname, content) {
      if (type == 'user') {
        if (oldname && oldname != name) {
          this.$delete(this.userExtraFile, oldname);
        }
        if (!this.userExtraFile) {
          this.userExtraFile = {};
        }
        this.userExtraFile[name] = content;
      } else {
        if (oldname && oldname != name) {
          this.$delete(this.judgeExtraFile, name);
        }
        if (!this.judgeExtraFile) {
          this.judgeExtraFile = {};
        }
        this.judgeExtraFile[name] = content;
      }
    },

    problemTypeChange(type) {
      if (type == 1) {
        let length = this.problemSamples.length;
        let aver = parseInt(100 / length);
        let add_1_num = 100 - aver * length;
        for (let i = 0; i < length; i++) {
          if (i >= length - add_1_num) {
            this.problemSamples[i].score = aver + 1;
          } else {
            this.problemSamples[i].score = aver;
          }
        }
      }
    },

    // 添加题目样例
    addExample() {
      this.problem.examples.push({input: '', output: '', isOpen: true});
    },
    changeExampleVisible(index, isOpen) {
      this.problem.examples[index]['isOpen'] = isOpen;
    },
    // 添加判题机的测试样例
    addSample() {
      let len = this.sampleIndex;
      if (this.mode === 'edit') {
        this.problemSamples.push({
          input: '',
          output: '',
          score: this.problem.type == 0 ? null : 0,
          groupNum: this.problem.type == 0 ? null : len,
          pid: this.pid,
          isOpen: true,
          index: len,
        });
      } else {
        this.problemSamples.push({
          input: '',
          output: '',
          score: this.problem.type == 0 ? null : 0,
          groupNum: this.problem.type == 0 ? null : len,
          pid: this.pid,
          isOpen: true,
          index: len,
        });
      }
      this.sampleIndex = len + 1;
      this.sortManualProblemSampleList();
    },
    //根据下标删除特定的题目样例
    deleteExample(index) {
      this.problem.examples.splice(index, 1);
    },
    //根据下标删除特定的判题机测试样例
    deleteSample(index) {
      this.problemSamples.splice(index, 1);
    },
    changeSampleVisible(index, isOpen) {
      this.problemSamples[index]['isOpen'] = isOpen;
    },
    uploadSucceeded(response) {
      if (response.status != 200) {
        myMessage.error(response.msg);
        this.testCaseUploaded = false;
        return;
      }
      myMessage.success(this.$i18n.t('m.Upload_Testcase_Successfully'));
      let fileList = response.data.fileList;
      let averSorce = parseInt(100 / fileList.length);
      let add_1_num = 100 - averSorce * fileList.length;
      for (let i = 0; i < fileList.length; i++) {
        if (averSorce) {
          if (i >= fileList.length - add_1_num) {
            fileList[i].score = averSorce + 1;
          } else {
            fileList[i].score = averSorce;
          }
        }
        if (!fileList[i].output) {
          fileList[i].output = '-';
        }
        fileList[i].pid = this.problem.id;
      }
      this.problem.testCaseScore = fileList;
      this.problem.testCaseScore.forEach((item, index) => {
        item.index = index + 1;
      });
      this.testCaseUploaded = true;
      this.problem.uploadTestcaseDir = response.data.fileListDir;
    },
    uploadFailed() {
      myMessage.error(this.$i18n.t('m.Upload_Testcase_Failed'));
    },

    compileSPJ() {
      let data = {
        pid: this.problem.id,
        code: this.problem.spjCode,
        language: this.problem.spjLanguage,
        extraFiles: this.judgeExtraFile,
      };
      this.loadingCompile = true;
      let apiMethodName = 'compileSPJ';
      if (this.problem.judgeMode == 'interactive') {
        apiMethodName = 'compileInteractive';
      }
      api[apiMethodName](data).then(
          (res) => {
            this.loadingCompile = false;
            this.problem.spjCompileOk = true;
            this.error.spj = '';
            myMessage.success(this.$i18n.t('m.Compiled_Successfully'));
          },
          (err) => {
            this.loadingCompile = false;
            this.problem.spjCompileOk = false;
            const h = this.$createElement;
            this.$msgbox({
              title: 'Compile Error',
              type: 'error',
              message: h('pre', err.data.msg),
              showCancelButton: false,
              closeOnClickModal: false,
              customClass: 'dialog-compile-error',
            });
          },
      );
    },
    sortTestCaseList() {
      this.$refs.xTable.clearSort();
      this.$refs.xTable.sort('groupNum', 'asc');
    },
    customSortMethod({data, sortList}) {
      const sortItem = sortList[0];
      const {property, order} = sortItem;
      let list = [];
      list = data.sort(function (a, b) {
        var value1 = a.groupNum,
            value2 = b.groupNum;
        if (value1 === value2) {
          return a.index - b.index;
        }
        if (order == 'desc') {
          return value2 - value1;
        } else {
          return value1 - value2;
        }
      });
      return list;
    },
    sortManualProblemSampleList() {
      this.problemSamples = this.problemSamples.sort(function (a, b) {
        var value1 = a.groupNum,
            value2 = b.groupNum;
        if (value1 === value2) {
          return a.index - b.index;
        }
        return value1 - value2;
      });
    },
    submit() {
      console.log("submit", this.problem)
      if (!this.problem.problemId) {
        myMessage.error(
            this.$i18n.t('m.Problem_Display_ID') +
            ' ' +
            this.$i18n.t('m.is_required'),
        );
        return;
      }

      if (this.contestID) {
        if (!this.contestProblem.displayId) {
          myMessage.error(
              this.$i18n.t('m.Contest_Display_ID') +
              ' ' +
              this.$i18n.t('m.is_required'),
          );
          return;
        }
        if (!this.contestProblem.displayTitle) {
          myMessage.error(
              this.$i18n.t('m.Contest_Display_Title') +
              ' ' +
              this.$i18n.t('m.is_required'),
          );
          return;
        }
      }

      if (this.problem.isFileIO && (!this.problem.ioReadFileName || !this.problem.ioWriteFileName)) {
        myMessage.error(this.$i18n.t('m.When_the_read_write_mode_is_File_IO_the_input_file_name_or_output_file_name_cannot_be_empty'));
        return;
      }

      // // 不强制校验题目样例不能为空
      // if (!this.problem.examples.length && !this.problem.isRemote) {
      //   myMessage.error(
      //     this.$i18n.t('m.Problem_Examples') +
      //       ' ' +
      //       this.$i18n.t('m.is_required')
      //   );
      //   return;
      // }

      if (!this.problem.isRemote) {
        // 选择手动输入
        if (!this.problem.isUploadCase) {
          if (!this.problemSamples.length) {
            myMessage.error(
                this.$i18n.t('m.Judge_Samples') +
                ' ' +
                this.$i18n.t('m.is_required'),
            );
            return;
          }

          for (let sample of this.problemSamples) {
            if (!sample.input && !sample.output) {
              myMessage.error(
                  this.$i18n.t('m.Sample_Input') +
                  ' or ' +
                  this.$i18n.t('m.Sample_Output') +
                  ' ' +
                  this.$i18n.t('m.is_required'),
              );
              return;
            }
          }

          // 同时是oi题目，则对应的每个测试样例的io得分不能为空或小于0
          if (this.problem.type == 1) {
            for (let i = 0; i < this.problemSamples.length; i++) {
              if (this.problemSamples[i].score == '') {
                myMessage.error(
                    this.$i18n.t('m.Problem_Sample') +
                    (this.problemSamples[i].index) +
                    ' ' +
                    this.$i18n.t('m.Score_must_be_an_integer'),
                );
                return;
              }
              try {
                if (parseInt(this.problemSamples[i].score) < 0) {
                  myMessage.error(
                      this.$i18n.t('m.Problem_Sample') +
                      (this.problemSamples[i].index) +
                      ' ' +
                      this.$i18n.t('m.Score_must_be_greater_than_or_equal_to_0'),
                  );
                  return;
                }
              } catch (e) {
                myMessage.error(this.$i18n.t('m.Score_must_be_an_integer'));
                return;
              }
              if (
                  (this.problem.judgeCaseMode == this.JUDGE_CASE_MODE.SUBTASK_LOWEST
                      || this.problem.judgeCaseMode == this.JUDGE_CASE_MODE.SUBTASK_AVERAGE
                  ) && this.problemSamples[i].groupNum == ''
              ) {
                myMessage.error(
                    this.$i18n.t('m.Problem_Sample') +
                    (this.problemSamples[i].index) +
                    '：' +
                    this.$i18n.t(
                        'm.Non_Default_Judge_Case_Mode_And_Group_Num_IS_NULL',
                    ),
                );
                return;
              }
            }
          }
        } else {
          // 选择上传文件
          // 两种情况：create模式是需要校验是否上传成功了，edit模式获取题目数据已经默认为true了，若是edit又重新上传数据，需要校验
          if (!this.testCaseUploaded) {
            this.error.testCase =
                this.$i18n.t('m.Judge_Samples') +
                ' ' +
                this.$i18n.t('m.is_required');
            myMessage.error(this.error.testCase);
            return;
          }

          // 如果是oi题目，需要检查上传的数据的得分
          if (this.problem.type == 1) {
            let problemSamples = this.problem.testCaseScore;
            for (let i = 0; i < problemSamples.length; i++) {
              if (problemSamples[i].score == '') {
                myMessage.error(
                    this.$i18n.t('m.Problem_Sample') +
                    (i + 1) +
                    ' ' +
                    this.$i18n.t('m.Score_must_be_an_integer'),
                );
                return;
              }
              try {
                if (parseInt(problemSamples[i].score) < 0) {
                  myMessage.error(
                      this.$i18n.t('m.Problem_Sample') +
                      (i + 1) +
                      ' ' +
                      this.$i18n.t('m.Score_must_be_greater_than_or_equal_to_0'),
                  );
                  return;
                }
              } catch (e) {
                myMessage.error(this.$i18n.t('m.Score_must_be_an_integer'));
                return;
              }
              if (
                  (this.problem.judgeCaseMode == this.JUDGE_CASE_MODE.SUBTASK_LOWEST
                      || this.problem.judgeCaseMode == this.JUDGE_CASE_MODE.SUBTASK_AVERAGE
                  ) && problemSamples[i].groupNum == ''
              ) {
                myMessage.error(
                    this.$i18n.t('m.Problem_Sample') +
                    (i + 1) +
                    '：' +
                    this.$i18n.t(
                        'm.Non_Default_Judge_Case_Mode_And_Group_Num_IS_NULL',
                    ),
                );
                return;
              }
            }
          }
        }
      }
      // 运行题目标签为空
      // if (!this.problemTags.length) {
      //   this.error.tags =
      //     this.$i18n.t('m.Tags') + ' ' + this.$i18n.t('m.is_required');
      //   myMessage.error(this.error.tags);
      //   return;
      // }
      let isChangeModeCode =
          this.spjRecord.spjLanguage != this.problem.spjLanguage ||
          this.spjRecord.spjCode != this.problem.spjCode;
      if (!this.problem.isRemote) {
        if (this.problem.judgeMode != 'default') {
          if (!this.problem.spjCode) {
            this.error.spj =
                this.$i18n.t('m.Spj_Or_Interactive_Code') +
                ' ' +
                this.$i18n.t('m.is_required');
            myMessage.error(this.error.spj);
          } else if (!this.problem.spjCompileOk && isChangeModeCode) {
            this.error.spj = this.$i18n.t(
                'm.Spj_Or_Interactive_Code_not_Compile_Success',
            );
          }
          if (this.error.spj) {
            myMessage.error(this.error.spj);
            return;
          }
        }
      }

      if (!this.problemLanguages.length) {
        this.error.languages =
            this.$i18n.t('m.Language') + ' ' + this.$i18n.t('m.is_required');
        myMessage.error(this.error.languages);
        return;
      }

      let funcName = {
        'admin-create-problem': 'admin_createProblem',
        'admin-edit-problem': 'admin_editProblem',
        'admin-create-contest-problem': 'admin_createContestProblem',
        'admin-edit-contest-problem': 'admin_editContestProblem',
      }[this.routeName];
      // edit contest problem 时, contest_id会被后来的请求覆盖掉
      if (funcName === 'editContestProblem') {
        this.problem.cid = this.contest.id;
      }
      if (
          funcName === 'admin_createProblem' ||
          funcName === 'admin_createContestProblem'
      ) {
        this.problem.author = this.userInfo.username;
      }

      var ojName = 'ME';
      if (this.problem.isRemote) {
        ojName = this.problem.problemId.split('-')[0];
      }

      let problemTagList = [];
      if (this.problemTags.length > 0) {
        problemTagList = Object.assign([], this.problemTags);
        for (let i = 0; i < problemTagList.length; i++) {
          //避免后台插入违反唯一性
          for (let tag2 of this.allTags) {
            if (problemTagList[i].name == tag2.name && tag2.oj == ojName) {
              problemTagList[i] = tag2;
              break;
            }
          }
        }
      }
      this.problemCodeTemplate = [];
      let problemLanguageList = Object.assign([], this.problemLanguages); // 深克隆 防止影响
      for (let i = 0; i < problemLanguageList.length; i++) {
        problemLanguageList[i] = {name: problemLanguageList[i]};
        for (let lang of this.allLanguage) {
          if (problemLanguageList[i].name == lang.name) {
            problemLanguageList[i] = lang;
            if (this.codeTemplate[lang.name].status) {
              if (this.codeTemplate[lang.name].code == null
                  || this.codeTemplate[lang.name].code.length == 0) {
                myMessage.error(
                    lang.name +
                    '：' +
                    this.$i18n.t('m.Code_template_of_the_language_cannot_be_empty'),
                );
                return;
              }
              this.problemCodeTemplate.push({
                id: this.codeTemplate[lang.name].id,
                pid: this.pid,
                code: this.codeTemplate[lang.name].code,
                lid: lang.id,
                status: this.codeTemplate[lang.name].status,
              });
            }
            break;
          }
        }
      }
      let problemDto = {}; // 上传给后台的数据
      if (!this.problem.isRemote) {
        if (this.problem.judgeMode != 'default') {
          if (isChangeModeCode) {
            problemDto['changeModeCode'] = true;
          }
        } else {
          // 原本是spj或交互，但现在关闭了
          if (!this.spjRecord.spjCode) {
            problemDto['changeModeCode'] = true;
            this.problem.spjCode = null;
            this.problem.spjLanguage = null;
          }
        }

        if (this.userExtraFile && Object.keys(this.userExtraFile).length != 0) {
          this.problem.userExtraFile = JSON.stringify(this.userExtraFile);
        } else {
          this.problem.userExtraFile = null;
        }

        if (
            this.judgeExtraFile &&
            Object.keys(this.judgeExtraFile).length != 0
        ) {
          this.problem.judgeExtraFile = JSON.stringify(this.judgeExtraFile);
        } else {
          this.problem.judgeExtraFile = null;
        }
      }

      problemDto['problem'] = Object.assign({}, this.problem); // 深克隆
      problemDto.problem.examples = utils.examplesToString(
          this.problem.examples,
      ); // 需要转换格式

      problemDto['codeTemplates'] = this.problemCodeTemplate;
      problemDto['tags'] = problemTagList;
      problemDto['languages'] = problemLanguageList;
      problemDto['isUploadTestCase'] = this.problem.isUploadCase;
      problemDto['uploadTestcaseDir'] = this.problem.uploadTestcaseDir;
      problemDto['judgeMode'] = this.problem.judgeMode;

      // 如果选择上传文件，则使用上传后的结果
      if (this.problem.isUploadCase) {
        problemDto['samples'] = this.problem.testCaseScore;
      } else {
        problemDto['samples'] = this.problemSamples;
      }

      if (this.judgeCaseModeRecord != this.problem.judgeCaseModeRecord) {
        problemDto['changeJudgeCaseMode'] = true;
      } else {
        problemDto['changeJudgeCaseMode'] = false;
      }

      api[funcName](problemDto)
          .then((res) => {
            if (
                this.routeName === 'admin-create-contest-problem' ||
                this.routeName === 'admin-edit-contest-problem'
            ) {
              if (res.data.data) {
                // 新增题目操作 需要使用返回来的pid
                this.contestProblem['pid'] = res.data.data.pid;
                this.contestProblem['cid'] = this.$route.params.contestId;
              }
              api.admin_setContestProblemInfo(this.contestProblem).then((res) => {
                myMessage.success('success');
                this.$router.push({
                  name: 'admin-contest-problem-list',
                  params: {contestId: this.$route.params.contestId},
                });
              });
            } else {
              myMessage.success('success');
              if (this.backPath) {
                this.$router.push({path: this.backPath});
              } else {
                this.$router.push({name: 'admin-problem-list'});
              }
            }
          })
          .catch(() => {
          });
    },
  },
}
</script>
