<template>
  <div class="fill-blank-wrapper">
    <problem-base
        ref="problemBase"
        :all-tags-tmp="allTagsTmp"
        :contest-id="contestID"
        :contest-problem="contestProblem"
        :input-visible.sync="inputVisible"
        :problem="problem"
        :problem-tags.sync="problemTags"
        :rules="rules"
        :tag-input.sync="tagInput"
        :title="title"
    />
    <el-tag type="primary">三个以上的下划线("_")代表一个空位，如"计算机诞生于____年，名字叫______什么？"</el-tag>
    <!-- 答案输入区域 -->
    <div class="answer-section">
      <el-tag type="success">每一个空的答案注意不要留前后空格</el-tag>
      <div v-for="(blank, index) in blanks" :key="blank.optionKey" class="blank-item">
        <span class="blank-label">填空 {{ index + 1 }}：</span>
        <el-input
            v-model="blank.optionContent"
            clearable
            placeholder="请输入正确答案"
            @input="handleAnswerChange"
        />
      </div>
    </div>
    <el-button size="medium" type="primary" @click.native="$emit('submit')">{{ $t('m.Save') }}</el-button>

  </div>
</template>

<script>
import ProblemBase from "@/components/tuc/problem/ProblemBase.vue";

export default {
  name: 'FillInBlankProblem',
  components: {ProblemBase},
  props: {
    problem: {
      type: Object,
      required: true
    },
    options: {
      type: Array,
      default: () => []
    },
    contestProblem: {
      type: Object,
      required: true
    },
    contestID: {
      type: [String, Number],
      default: null
    },
    rules: {
      type: Object,
      required: true
    },
    problemTags: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    allTagsTmp: {
      type: Array,
      default: () => []
    },
    inputVisible: {
      type: Boolean,
      default: false
    },
    tagInput: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      blanks: []
    };
  },
  watch: {
    'problem.description': {
      handler(newVal) {
        this.$nextTick(() => {
          this.parseContent(newVal);
        });
      },
      immediate: true,
      deep: true
    },
    options: {
      handler(newVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(this.blanks)) {
          this.syncOptions(newVal.length, newVal);
        }
      },
      deep: true
    }
  },
  methods: {
    parseContent(html = this.problem.description) {
      const text = this.extractTextContent(html);
      const blankCount = (text.match(/_{3,}/g) || []).length;
      this.syncOptions(blankCount);
    },

    extractTextContent(html) {
      const div = document.createElement('div');
      div.innerHTML = html;
      return div.textContent || div.innerText || '';
    },

    syncOptions(targetCount, sourceOptions = this.options) {
      const newBlanks = [];

      // 保留已有答案
      for (let i = 0; i < targetCount; i++) {
        newBlanks.push({
          optionKey: `填空_${i + 1}`,
          optionContent: sourceOptions[i]?.optionContent || this.blanks[i]?.optionContent || ''
        });
      }

      if (JSON.stringify(this.blanks) !== JSON.stringify(newBlanks)) {
        this.blanks = newBlanks;
        this.$emit('update:options', [...this.blanks]);
      }
    },

    handleAnswerChange() {
      this.$emit('update:options', [...this.blanks]);
    }
  }
};
</script>

<style scoped>
.fill-blank-wrapper {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 20px;
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.answer-section {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  margin-top: 20px;
  border: 1px solid #ebeef5;
}

.blank-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.blank-item:hover {
  transform: translateX(5px);
}

.blank-label {
  width: 90px;
  flex-shrink: 0;
  font-weight: 600;
  color: #606266;
  font-size: 14px;
}

::v-deep .el-input__inner {
  border-radius: 4px;
  transition: border-color 0.3s;
}

::v-deep .el-input__inner:focus {
  border-color: #409EFF;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
}

/* 下划线视觉增强 */
.fill-blank-wrapper ::v-deep .markdown-body u {
  text-decoration: none !important;
  border-bottom: 2px dashed #67C23A;
  padding-bottom: 2px;
  display: inline-block;
  line-height: 1.5;
}

.fill-blank-wrapper ::v-deep .markdown-body span[style*="text-decoration: underline"] {
  border-bottom: 2px dashed #E6A23C !important;
  padding-bottom: 2px;
  display: inline-block !important;
}

/* 处理加粗样式 */
.fill-blank-wrapper ::v-deep .markdown-body strong u {
  border-bottom-width: 3px;
}
</style>
