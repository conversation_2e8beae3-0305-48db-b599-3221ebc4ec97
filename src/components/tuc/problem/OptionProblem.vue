<template>
  <div>
    <el-card v-if="activeTab === 'SingleChoice'">
      <div slot="header">
        <span class="panel-title home-title">{{ title }}</span>
      </div>
      <el-form ref="form" :model="problem" :rules="rules" label-position="top" label-width="70px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('m.Problem_Display_ID')" prop="problemId" required>
              <el-input v-model="problem.problemId" :disabled="problem.isRemote" :placeholder="$t('m.Problem_Display_ID')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('m.Title')" prop="title" required>
              <el-input v-model="problem.title" :placeholder="$t('m.Title')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="contestID" :gutter="20">
          <el-col :md="12" :xs="24">
            <el-form-item :label="$t('m.Contest_Display_Title')" required>
              <el-input v-model="contestProblem.displayTitle" :placeholder="$t('m.Contest_Display_Title')"></el-input>
            </el-form-item>
          </el-col>

          <el-col :md="12" :xs="24">
            <el-form-item :label="$t('m.Contest_Display_ID')" required>
              <el-input v-model="contestProblem.displayId" :placeholder="$t('m.Contest_Display_ID')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('m.Description')" prop="description" required>
              <Editor :value.sync="problem.description"></Editor>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :md="6" :xs="24">
            <el-form-item :label="$t('m.Level')" required>
              <el-select v-model="problem.difficulty" class="difficulty-select" placeholder="Enter the level of problem">
                <el-option v-for="(value, key, index) in PROBLEM_LEVEL" :key="index" :label="getLevelName(key)" :value="parseInt(key)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="4" :xs="24">
            <el-form-item :label="$t('m.Auth')">
              <el-select v-model="problem.auth" size="small">
                <el-option :label="$t('m.Public_Problem')" :value="1"></el-option>
                <el-option :label="$t('m.Private_Problem')" :value="2"></el-option>
                <el-option :label="$t('m.Contest_Problem')" :value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :md="12" :xs="24">
            <el-form-item :label="$t('m.Tags')">
              <el-tag v-for="tag in problemTags" :key="tag.name" :close-transition="false" closable size="small" style="margin-right: 7px;margin-top:4px" @close="closeTag(tag.name)">{{ tag.name }}
              </el-tag>
              <!-- 输入时建议，回车，选择，光标消失触发更新 -->
              <el-autocomplete v-if="inputVisible" v-model="tagInput" :fetch-suggestions="querySearch" :trigger-on-focus="true" class="input-new-tag" size="mini" @click="selectTag" @select="addTag"
                               @keyup.enter.native="addTag">
              </el-autocomplete>
              <el-tooltip v-else :content="$t('m.Add')" effect="dark" placement="top">
                <el-button class="button-new-tag" icon="el-icon-plus" size="small" @click="inputVisible = true"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>

        <el-button size="small" type="primary" @click.native="submit()">{{ $t('m.Save') }}
        </el-button>
      </el-form>
    </el-card>
  </div>
</template>
<script lang="js">
</script>
