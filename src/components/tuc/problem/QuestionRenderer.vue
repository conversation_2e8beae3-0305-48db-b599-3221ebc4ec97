<template>
  <div class="question-renderer">
    <!-- 单选题/判断题 -->
    <div v-if="isSingleChoice || isTrueFalse" class="options-grid">
      <div
          v-for="option in options"
          :key="option.id"
          :class="{ 'selected': selectedAnswers.includes(option.id) }"
          class="option-card"
          @click="handleSingleSelect(option)"
      >
        <div class="option-header">
          <el-radio
              :label="option.id"
              :value="selectedAnswers[0]"
              class="option-radio"
              @click.native.stop
          >
            <span class="option-key">{{ option.optionKey }}</span>
          </el-radio>
        </div>
        <div class="option-content">{{ option.optionContent }}</div>
      </div>
    </div>
    <!-- 多选题 -->
    <div v-if="isMultipleChoice" class="options-grid">
      <div
          v-for="option in options"
          :key="option.id"
          :class="{ 'selected': selectedAnswers.includes(option.id) }"
          class="option-card"
          @click="handleMultipleSelect(option)"
      >
        <div class="option-header">
          <el-checkbox
              v-model="selectedAnswers"
              :label="option.id"
              class="option-checkbox"
              @click.native.stop
          >
            <span class="option-key">{{ option.optionKey }}</span>
          </el-checkbox>
        </div>
        <div class="option-content">{{ option.optionContent }}</div>
      </div>
    </div>
    <!-- 填空题 -->
    <div v-if="isFillInBlank" class="fill-options">
      <div
          v-for="option in options"
          :key="option.id"
          class="fill-option"
      >
        <span class="fill-label">{{ option.optionKey }}.</span>
        <el-input
            v-model="fillAnswers[option.id]"
            class="fill-input"
            placeholder="请输入答案"
            @input="handleFillInput(option)"
        />
      </div>
    </div>
  </div>
</template>
<script>
/**
 * 题目渲染组件
 * @displayName QuestionRenderer
 * @version 1.1.0
 *
 * 功能特性：
 * 1. 支持多种题型：单选、多选、判断、填空
 * 2. 支持双向数据绑定（通过answersOptions属性和answerOptions事件）
 * 3. 自动初始化答案存储结构
 *
 * 事件说明：
 * @event answerOptions 答案更新事件，返回格式与answersOptions属性一致
 */
export default {
  name: 'QuestionRenderer',
  props: {
    /**
     * 题目选项数组
     * @type {Array<{id: string, optionKey: string, optionContent: string}>}
     */
    options: {
      type: Array,
      default: () => [],
      required: true
    },
    /**
     * 题目类型标识
     * @values 'SingleChoice', 'MultipleChoice', 'TrueFalse', 'FillInBlank'
     */
    problemType: {
      type: String,
      default: 'SingleChoice',
      validator: (value) => [
        'SingleChoice',
        'MultipleChoice',
        'TrueFalse',
        'FillInBlank'
      ].includes(value)
    },
    /**
     * 用户已选择的答案选项（需与answerOptions事件参数格式一致）
     * @type {Array<{id: string, answer: boolean|string}>}
     */
    replyOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedAnswers: [],     // 选中答案ID集合（单选/多选/判断）
      fillAnswers: {}         // 填空题答案存储（格式: {选项ID: 答案内容}）
    }
  },
  computed: {
    /** 判断当前是否是单选题型 */
    isSingleChoice() {
      return this.problemType === 'SingleChoice'
    },
    /** 判断当前是否多选题型 */
    isMultipleChoice() {
      return this.problemType === 'MultipleChoice'
    },
    /** 判断当前是否判断题型 */
    isTrueFalse() {
      return this.problemType === 'TrueFalse'
    },
    /** 判断当前是否填空题型 */
    isFillInBlank() {
      return this.problemType === 'FillInBlank'
    }
  },
  watch: {
    /** 监听题目选项变化初始化答案结构 */
    options: {
      immediate: true,
      handler() {
        this.initAnswers()
      }
    },
    /** 监听外部传入的答案变化同步更新 */
    answersOptions: {
      immediate: true,
      handler() {
        this.initAnswers()
      }
    }
  },
  methods: {
    /**
     * 初始化答案数据结构
     * @desc 根据题型和传入的答案选项初始化组件内部状态
     */
    initAnswers() {
      if (this.isFillInBlank) {
        this.initFillAnswers()
      } else {
        this.initSelectedAnswers()
      }
    },
    /**
     * 初始化填空题答案
     * @desc 建立选项ID与用户输入的映射
     */
    initFillAnswers() {
      this.fillAnswers = this.options.reduce((acc, cur) => {
        const answerOption = this.replyOptions.find(opt => opt.id === cur.id);
        acc[cur.id] = answerOption?.optionContent || ''
        return acc
      }, {})
    },
    /**
     * 初始化选择题选中状态
     * @desc 根据传入答案直接提取选项ID
     */
    initSelectedAnswers() {
      this.selectedAnswers = this.replyOptions.map(opt => opt.id);
    },

    /** 处理单选题选择 */
    handleSingleSelect(option) {
      this.selectedAnswers = [option.id]
      this.emitAnswers()
    },
    /** 处理多选题选择 */
    handleMultipleSelect(option) {
      const index = this.selectedAnswers.indexOf(option.id)
      index > -1
          ? this.selectedAnswers.splice(index, 1)
          : this.selectedAnswers.push(option.id)
      this.emitAnswers()
    },
    /** 处理填空题输入 */
    handleFillInput() {
      this.emitAnswers()
    },
    /**
     * 答案更新事件处理
     * @desc 生成与options结构一致的答案数据
     */
    emitAnswers() {
      let replyOptions;

      if (this.isFillInBlank) {
        // 填空题型：保留所有选项结构，替换用户输入内容
        replyOptions = this.options.map(option => ({
          ...option,
          optionContent: this.fillAnswers[option.id] || ''
        }))
      } else {
        // 选择题型：直接过滤出选中项原始数据
        replyOptions = this.options.filter(option =>
            this.selectedAnswers.includes(option.id)
        )
      }

      this.$emit('replyOptions', replyOptions);
    },
  }
}
</script>


<style lang="scss" scoped>
$primary-color: #409EFF;
$hover-color: #ecf5ff;
$selected-color: #f0f7ff;
.question-renderer {
  padding: 20px;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.option-card {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba($primary-color, 0.1);
    border-color: $primary-color;
  }

  &.selected {
    background: $selected-color;
    border-color: $primary-color;
  }
}

.option-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.option-key {
  font-weight: 500;
  color: $primary-color;
  margin-right: 8px;
}

.option-content {
  color: #606266;
}

.fill-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.fill-option {
  display: flex;
  align-items: center;
}

.fill-label {
  width: 40px;
  color: $primary-color;
  font-weight: 500;
}

.fill-input {
  flex: 1;

  ::v-deep .el-input__inner {
    border-radius: 20px;
    padding-left: 20px;
    transition: all 0.3s;

    &:focus {
      border-color: $primary-color;
      box-shadow: 0 2px 8px rgba($primary-color, 0.2);
    }
  }
}

.option-radio ::v-deep,
.option-checkbox ::v-deep {
  .el-radio__label,
  .el-checkbox__label {
    display: none;
  }
}
</style>
