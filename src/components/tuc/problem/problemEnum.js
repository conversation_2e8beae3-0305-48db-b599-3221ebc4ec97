export const ProblemType = {
    // unknown: {code: "unknown", name: "未知题型", maxOptions: 0},
    // ShortAnswer: {code: "ShortAnswer", name: "简答题", maxOptions: 1}
    Programmer: {code: "Programmer", name: "编程题", maxOptions: 0},
    SingleChoice: {code: "SingleChoice", name: "单选题", maxOptions: 4},
    FillInBlank: {code: "FillInBlank", name: "填空题", maxOptions: 4},
    MultipleChoice: {code: "MultipleChoice", name: "多选题", maxOptions: 6},
    TrueFalse: {code: "TrueFalse", name: "判断题", maxOptions: 2},
};

export function isChoiceType(code) {
    return [
        ProblemType.SingleChoice.code,
        ProblemType.MultipleChoice.code,
        ProblemType.TrueFalse.code,
    ].includes(code);
};

// 遍历示例
Object.entries(ProblemType).forEach(([key, value]) => {
    console.log(`Code: ${value.code}, Name: ${value.name}, MaxOptions: ${value.maxOptions}`);
});
