<template>
  <div>
    <el-card>
      <problem-base :all-tags-tmp="allTagsTmp" :contest-id="contestID" :contest-problem="contestProblem"
                    :input-visible.sync="inputVisible" :problem="problem" :problem-tags.sync="problemTags" :rules="rules"
                    :tag-input.sync="tagInput" :title="title"/>
      <div class="options-section">
        <div v-for="(option, index) in localOptions" :key="index" class="option-item">
          <el-row :gutter="20" align="middle" type="flex">
            <el-col :span="1">
              <span class="option-key">{{ option.optionKey }}.</span>
            </el-col>
            <el-col :span="18">
              <el-input v-model="option.optionContent" :disabled="isOptionContentDisabled(index)"
                        :placeholder="getOptionPlaceholder(index)" @input="emitUpdate"/>
            </el-col>
            <el-col :span="3">
              <el-checkbox v-model="option.answer" :disabled="isCheckboxDisabled(option)"
                           @change="(val) => handleAnswerChange(val, index)">正确答案
              </el-checkbox>
            </el-col>
            <el-col :span="2">
              <el-button v-if="showDeleteButton(index)" icon="el-icon-delete" type="danger"
                         @click="removeOption(index)"/>
            </el-col>
          </el-row>
        </div>
        <el-button :disabled="localOptions.length >= problemType.maxOptions" icon="el-icon-plus" plain type="success"
                   @click="addOption">添加选项（{{ localOptions.length }}/{{ problemType.maxOptions }}）
        </el-button>
        <el-button size="medium" type="primary" @click.native="$emit('submit')">{{ $t('m.Save') }}</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import {PROBLEM_LEVEL} from '@/common/constants';
import utils from '@/common/utils';
import ProblemBase from "@/components/tuc/problem/ProblemBase.vue";
import {ProblemType} from "@/components/tuc/problem/problemEnum";

const Editor = () => import('@/components/admin/Editor.vue');

export default {
  components: {
    ProblemBase,
    Editor
  },
  props: {
    options: {
      type: Array,
      default: () => []
    },

    // 基础组件参数
    problem: {
      type: Object,
      required: true
    },
    problemType: {
      type: ProblemType,
      required: true
    },
    contestProblem: {
      type: Object,
      required: true
    },
    contestID: {
      type: [String, Number],
      default: null
    },
    rules: {
      type: Object,
      required: true
    },
    problemTags: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    allTagsTmp: {
      type: Array,
      default: () => []
    },
    inputVisible: {
      type: Boolean,
      default: false
    },
    tagInput: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      PROBLEM_LEVEL: Object.assign({}, PROBLEM_LEVEL),
      localOptions: JSON.parse(JSON.stringify(this.options)),
    };
  },
  watch: {
    options(newVal) {
      this.localOptions = JSON.parse(JSON.stringify(newVal));
    },
    problemType: {
      immediate: true,
      handler(newType) {
        if (newType.code === 'TrueFalse' && this.localOptions.length === 0) {
          this.initTrueFalseOptions();
        }
      }
    }
  },
  methods: {
    getLevelName(difficulty) {
      return utils.getLevelName(difficulty);
    },
    querySearch(queryString, cb) {
      const ojName = 'ME';
      const restaurants = this.allTagsTmp.filter(item => item.oj === ojName);
      const results = queryString
          ? restaurants.filter(item => item.value.toLowerCase().includes(queryString.toLowerCase()))
          : restaurants;
      cb(results);
    },
    selectTag(item) {
      if (this.problemTags.some(tag => tag.name === item.value)) {
        this.$message.warning(this.$t('m.Add_Tag_Error'));
        this.tagInput = '';
        return;
      }
      this.tagInput = item.value;
    },
    addTag() {
      if (this.tagInput && !this.problemTags.some(tag => tag.name === this.tagInput)) {
        this.problemTags.push({name: this.tagInput});
        this.inputVisible = false;
        this.tagInput = '';
      }
    },
    closeTag(tagName) {
      this.problemTags = this.problemTags.filter(tag => tag.name !== tagName);
    },
    // addOption() {
    //   const newOptionKey = this.generateNextOptionKey();
    //   this.localOptions.push({
    //     optionKey: newOptionKey,
    //     optionContent: '',
    //     answer: false,
    //   });
    //   this.emitUpdate();
    // },
    generateNextOptionKey() {
      if (this.localOptions.length === 0) return 'A';
      const charCodes = this.localOptions
          .map(opt => opt.optionKey.toUpperCase().charCodeAt(0))
          .filter(code => code >= 65 && code <= 90); // 只处理A-Z
      const maxCode = Math.max(...charCodes, 64); // 64是@，保证空数组时从A开始
      return String.fromCharCode(maxCode + 1);
    },
    // removeOption(index) {
    //   this.localOptions.splice(index, 1);
    //   this.emitUpdate();
    // },
    emitUpdate() {
      this.$emit('update:options', JSON.parse(JSON.stringify(this.localOptions)));
    },


    // 原有方法保持不变，新增以下方法

    initTrueFalseOptions() {
      this.localOptions = [
        {optionKey: 'A', optionContent: '正确', answer: false},
        {optionKey: 'B', optionContent: '错误', answer: false}
      ];
      this.emitUpdate();
    },

    handleAnswerChange(checked, index) {
      if (checked && this.isSingleAnswerType) {
        this.localOptions.forEach((option, i) => {
          if (i !== index) option.answer = false;
        });
      }
      this.emitUpdate();
    },

    getOptionPlaceholder(index) {
      if (this.problemType.code === 'TrueFalse') {
        return index === 0 ? '正确' : '错误';
      }
      return '请输入选项内容';
    },

    isOptionContentDisabled(index) {
      return this.problemType.code === 'TrueFalse' && index < 2;
    },

    isCheckboxDisabled(option) {
      return this.problemType.code === 'FillInBlank' ||
          this.problemType.code === 'ShortAnswer';
    },

    showDeleteButton(index) {
      if (this.problemType.code === 'TrueFalse') {
        return this.localOptions.length > 2 && index >= 2;
      }
      return true;
    },

    addOption() {
      if (this.localOptions.length >= this.problemType.maxOptions) {
        this.$message.warning(`选项数量不能超过${this.problemType.maxOptions}个`);
        return;
      }

      const newOptionKey = this.generateNextOptionKey();
      this.localOptions.push({
        optionKey: newOptionKey,
        optionContent: '',
        answer: false,
      });
      this.emitUpdate();
    },

    removeOption(index) {
      if (this.problemType.code === 'TrueFalse' && this.localOptions.length <= 2) {
        this.$message.warning('判断题必须保留两个基本选项');
        return;
      }
      this.localOptions.splice(index, 1);
      this.emitUpdate();
    },


  },
  computed: {
    ProblemType() {
      return ProblemType
    },
    isSingleAnswerType() {
      return [ProblemType.SingleChoice.code, ProblemType.TrueFalse.code]
          .includes(this.problemType.code);
    },
    isChoiceType() {
      return [
        ProblemType.SingleChoice.code,
        ProblemType.MultipleChoice.code,
        ProblemType.TrueFalse.code
      ].includes(this.problemType.code);
    }
  }
};
</script>

<style scoped>
.options-section {
  margin-top: 20px;
}

.option-item {
  margin-bottom: 15px;
}

.option-key {
  font-weight: bold;
  margin-right: 5px;
}

/* 判断题特殊样式 */
.option-item.true-false-option {
  .el-input {
    width: 200px;
  }
}

.el-button[disabled] {
  cursor: not-allowed;
}
</style>
