<template>
  <div>
    <div slot="header">
      <span class="panel-title home-title">{{ title }}</span>
    </div>
    <el-form ref="form" :model="problem" :rules="rules" label-position="top" label-width="70px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="$t('m.Problem_Display_ID')" prop="problemId" required>
            <el-input v-model="problem.problemId" :disabled="problem.isRemote"
                      :placeholder="$t('m.Problem_Display_ID')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="$t('m.Title')" prop="title" required>
            <el-input v-model="problem.title" :placeholder="$t('m.Title')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="contestID" :gutter="20">
        <el-col :md="12" :xs="24">
          <el-form-item :label="$t('m.Contest_Display_Title')" required>
            <el-input v-model="contestProblem.displayTitle" :placeholder="$t('m.Contest_Display_Title')"></el-input>
          </el-form-item>
        </el-col>

        <el-col :md="12" :xs="24">
          <el-form-item :label="$t('m.Contest_Display_ID')" required>
            <el-input v-model="contestProblem.displayId" :placeholder="$t('m.Contest_Display_ID')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="'题目内容'" prop="description" required>
            <Editor :value.sync="problem.description"></Editor>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :md="6" :xs="24">
          <el-form-item :label="$t('m.Level')" required>
            <el-select v-model="problem.difficulty" class="difficulty-select" placeholder="Enter the level of problem">
              <el-option v-for="(value, key, index) in PROBLEM_LEVEL" :key="index" :label="getLevelName(key)"
                         :value="parseInt(key)"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :md="4" :xs="24">
          <el-form-item :label="$t('m.Auth')">
            <el-select v-model="problem.auth" size="small">
              <el-option :label="$t('m.Public_Problem')" :value="1"></el-option>
              <el-option :label="$t('m.Private_Problem')" :value="2"></el-option>
              <el-option :label="$t('m.Contest_Problem')" :value="3"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :md="12" :xs="24">
          <el-form-item :label="$t('m.Tags')">
            <el-tag v-for="tag in localProblemTags" :key="tag.name" :close-transition="false" closable size="small"
                    style="margin-right: 7px;margin-top:4px" @close="closeTag(tag.name)">{{ tag.name }}
            </el-tag>
            <el-autocomplete v-if="localInputVisible" v-model="localTagInput" :fetch-suggestions="querySearch"
                             :trigger-on-focus="true" class="input-new-tag" size="mini" @click="selectTag"
                             @select="addTag"
                             @keyup.enter.native="addTag">
            </el-autocomplete>
            <el-tooltip v-else :content="$t('m.Add')" effect="dark" placement="top">
              <el-button class="button-new-tag" icon="el-icon-plus" size="small"
                         @click="showTagInput"></el-button>
            </el-tooltip>
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
  </div>
</template>

<script>
import {PROBLEM_LEVEL} from '@/common/constants';
import utils from '@/common/utils';

const Editor = () => import('@/components/admin/Editor.vue');


export default {
  components: {
    Editor
  },
  props: {
    problem: {
      type: Object,
      required: true
    },
    contestProblem: {
      type: Object,
      required: true
    },
    contestID: {
      type: [String, Number],
      default: null
    },
    rules: {
      type: Object,
      required: true
    },
    problemTags: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    allTagsTmp: {
      type: Array,
      default: () => []
    },
    inputVisible: {
      type: Boolean,
      default: false
    },
    tagInput: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      PROBLEM_LEVEL: Object.assign({}, PROBLEM_LEVEL),
      localProblemTags: [],
      localInputVisible: false,
      localTagInput: ''
    };
  },
  watch: {
    problemTags: {
      immediate: true,
      handler(newVal) {
        this.localProblemTags = JSON.parse(JSON.stringify(newVal));
      }
    },
    inputVisible: {
      immediate: true,
      handler(newVal) {
        this.localInputVisible = newVal;
      }
    },
    tagInput: {
      immediate: true,
      handler(newVal) {
        this.localTagInput = newVal;
      }
    }
  },
  methods: {
    getLevelName(difficulty) {
      return utils.getLevelName(difficulty);
    },
    querySearch(queryString, cb) {
      const ojName = 'ME';
      const restaurants = this.allTagsTmp.filter(item => item.oj === ojName);
      const results = queryString
          ? restaurants.filter(item => item.value.toLowerCase().includes(queryString.toLowerCase()))
          : restaurants;
      cb(results);
    },
    selectTag(item) {
      if (this.localProblemTags.some(tag => tag.name === item.value)) {
        this.$message.warning(this.$t('m.Add_Tag_Error'));
        this.localTagInput = '';
        return;
      }
      this.localTagInput = item.value;
    },
    addTag() {
      if (this.localTagInput && !this.localProblemTags.some(tag => tag.name === this.localTagInput)) {
        const newTag = {name: this.localTagInput};
        this.localProblemTags.push(newTag);
        this.$emit('update:problemTags', this.localProblemTags);
        this.localInputVisible = false;
        this.localTagInput = '';
        this.$emit('update:inputVisible', false);
        this.$emit('update:tagInput', '');
      }
    },
    closeTag(tagName) {
      this.localProblemTags = this.localProblemTags.filter(tag => tag.name !== tagName);
      this.$emit('update:problemTags', this.localProblemTags);
    },
    showTagInput() {
      this.localInputVisible = true;
      this.$emit('update:inputVisible', true);
    }
  }
};
</script>

<style scoped>
/* 保持原有样式 */
</style>
