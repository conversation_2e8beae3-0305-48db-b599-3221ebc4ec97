<template>
  <div class="redemption-history">
    <el-table
        v-loading="loading"
        :cell-style="{textAlign:'center'}"
        :data="tableData"
        :header-cell-style="{background:'#f0f9ff', color:'#409EFF'}"
        border
        header-align="center"
        stripe
        style="width: 100%"
    >
      <el-table-column
          align="center"
          label="奖品名称"
          prop="prizeName"
          width="120"
      />
      <el-table-column align="center" label="奖品图片" width="100">
        <template slot-scope="{row}">
          <el-image
              :preview-src-list="[row.prizeUrl]"
              :src="row.prizeUrl"
              fit="cover"
              style="width: 50px; height: 50px; border-radius: 8px;"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" label="消耗积分" prop="pointsUsed" width="100">
        <template slot-scope="{row}">
          <i class="el-icon-coin" style="color:#67C23A; margin-right:5px;"></i>
          {{ row.pointsUsed }}
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="兑换用户"
          prop="username"
          width="120"
      />
      <el-table-column
          align="center"
          label="收货人"
          prop="name"
          width="100"
      />
      <el-table-column
          align="center"
          label="联系方式"
          prop="contact"
          width="120"
      />
      <el-table-column
          align="center"
          label="收货地址"
          prop="address"
          width="200"
      >
        <template slot-scope="{row}">
          <el-tooltip v-if="row.address" :content="row.address" effect="light" placement="top">
            <div class="address-text">{{ row.address }}</div>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="兑换时间" prop="redeemTime" width="180">
        <template slot-scope="{row}">
          {{ formatTime(row.redeemTime) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="状态" prop="status" width="100">
        <template slot-scope="{row}">
          <el-tag
              :type="row.status ? 'success' : 'warning'"
              style="border-radius: 12px;"
          >
            {{ row.status ? '已发放' : '待发放' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="{row}">
          <slot :row="row" name="actions"/>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-wrapper">
      <el-pagination
          :current-page="pagination.current"
          :page-size="pagination.size"
          :page-sizes="[5, 10, 20]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script>
import api from '@/common/api'
import {formatDateTime} from "@/common/date";

export default {
  name: 'RedemptionHistory',
  props: {
    data: {
      type: Object,
      default: null
    },
    autoLoad: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      pagination: {
        current: 1,
        size: 10,
        total: 0,
        pages: 1
      }
    }
  },
  watch: {
    data: {
      immediate: true,
      handler(val) {
        if (val) {
          this.tableData = val.records
          this.pagination = {
            current: val.current,
            size: val.size,
            total: val.total,
            pages: val.pages
          }
        }
      }
    }
  },
  mounted() {
    if (!this.data && this.autoLoad) {
      this.fetchData()
    }
  },
  methods: {
    formatTime(time) {
      return formatDateTime(time)
    },
    async fetchData() {
      this.loading = true;
      api.getRedemptionHistory({
        currentPage: this.pagination.current,
        limit: this.pagination.size,
      }, {})
          .then(res => {
            this.tableData = res.data.data.records;
            this.pagination.total = res.data.data.total;
          })
          .finally(() => this.loading = false);
    },
    handleSizeChange(size) {
      this.pagination.size = size
      if (!this.data) this.fetchData()
    },
    handleCurrentChange(current) {
      this.pagination.current = current
      if (!this.data) this.fetchData()
    }
  }
}
</script>
<style scoped>
.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.redemption-history {
  padding: 20px;
  background: white;
  border-radius: 8px;
}

.el-table {
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(100, 181, 246, 0.15);
}

.el-table__header th {
  background: #e6f7ff !important;
  font-size: 15px;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f5fbff;
}

.el-table__body tr:hover > td {
  background-color: #ebf7ff !important;
}

.address-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}
</style>
