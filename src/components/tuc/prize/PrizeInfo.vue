<!-- PrizeInfo.vue -->
<template>
  <div class="kids-prize-dialog">
    <!-- 管理员操作栏 -->
    <div v-if="admin" class="admin-controls">
      <el-button type="primary" @click="handleAddPrize">
        <i class="el-icon-circle-plus"></i> 新增奖品
      </el-button>
      <el-button type="danger" @click="toggleDeleteMode">
        <i class="el-icon-delete"></i> {{ showDelete ? '取消删除' : '删除奖品' }}
      </el-button>
      <el-button type="warning" @click="toggleEditMode">
        <i class="el-icon-edit"></i> {{ showEdit ? '取消编辑' : '编辑奖品' }}
      </el-button>
    </div>
    <!-- 奖品列表 -->
    <el-row :gutter="20" class="prize-grid">
      <el-col
          v-for="prize in processedPrizes"
          :key="prize.id"
          :span="24/columns"
          class="prize-col"
      >
        <div class="prize-card">
          <!-- 删除图标 -->
          <div v-if="showDelete" class="action-icon delete" @click="handleDelete(prize)">
            <i class="el-icon-delete-solid"></i>
          </div>

          <!-- 编辑图标 -->
          <div v-if="showEdit" class="action-icon edit" @click="handleEdit(prize)">
            <i class="el-icon-edit"></i>
          </div>
          <div class="prize-image-wrapper">
            <img :src="prize.imageUrl" :alt="prize.name" class="prize-image">
            <div class="points-bubble">{{ prize.points }} 积分</div>
          </div>

          <h3 class="prize-name">{{ prize.name }}</h3>

          <!-- 兑换按钮 -->
          <el-button
              v-if="showExchangeButton"
              :disabled="currentPoints < prize.points"
              class="exchange-btn"
              @click="handleExchange(prize)"
          >
            <i class="el-icon-shopping-cart-2"></i>
            立即兑换
          </el-button>
        </div>
      </el-col>
    </el-row>
    <!-- 奖品表单弹窗 -->
    <el-dialog
        :visible.sync="showFormDialog"
        :title="formTitle"
        width="500px"
        append-to-body
    >
      <el-form :model="currentPrize" label-width="100px">
        <el-form-item label="奖品名称" required>
          <el-input v-model="currentPrize.name" />
        </el-form-item>
        <el-form-item label="所需积分" required>
          <el-input-number v-model="currentPrize.points" :min="1" />
        </el-form-item>
        <el-form-item label="图片地址" required>
          <oss-upload
              v-model="currentPrize.imageUrl"
              :allowed-extensions="['pdf', 'png']"
              :max-count="3"
          />
        </el-form-item>
        <el-form-item label="库存数量" required>
          <el-input-number v-model="currentPrize.stock" :min="0" />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch
              v-model="currentPrize.status"
              :active-value="1"
              :inactive-value="0"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showFormDialog = false">取消</el-button>
        <el-button type="primary" @click="submitPrize">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import api from '@/common/api'
import OssUpload from "@/components/tuc/OssUpload.vue";
export default {
  name: 'PrizeInfo',
  components: {OssUpload},
  props: {
    visible: Boolean,
    showExchangeButton: {
      type: Boolean,
      default: false
    },
    currentPoints: {
      type: Number,
      default: 0
    },
    admin: {
      type: Boolean,
      default: false
    },
    columns: {
      type: Number,
      default: 3
    },
    prizeList: {
      type: Array,
      default: null
    }
  },
  data() {
    return {
      showDelete: false,
      showEdit: false,
      showFormDialog: false,
      currentPrize: this.getEmptyPrize(),
      processedPrizes: [],
      formTitle: '新增奖品'
    }
  },
  computed: {
    dialogTitle() {
      return this.admin ? '🎁 奖品管理' : '🎁 可兑换奖品'
    }
  },
  watch: {
    prizeList: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.processedPrizes = [...newVal]
        } else {
          this.fetchPrizes()
        }
      }
    }
  },
  methods: {
    async fetchPrizes() {
      if (!this.prizeList) {
        const res = await api.getPrizeList()
        this.processedPrizes = res.data.data
      }
    },
    getEmptyPrize() {
      return {
        id: null,
        name: '',
        points: 10,
        imageUrl: '',
        stock: 1,
        status: 1
      }
    },
    toggleDeleteMode() {
      this.showDelete = !this.showDelete
      this.showEdit = false
    },
    toggleEditMode() {
      this.showEdit = !this.showEdit
      this.showDelete = false
    },
    handleAddPrize() {
      this.currentPrize = this.getEmptyPrize()
      this.formTitle = '新增奖品'
      this.showFormDialog = true
    },
    handleEdit(prize) {
      this.currentPrize = { ...prize }
      this.formTitle = '编辑奖品'
      this.showFormDialog = true
    },
    async handleDelete(prize) {
      await this.$confirm('确定删除该奖品吗？', '提示', { type: 'warning' })
      await api.deletePrize(prize.id)
      this.processedPrizes = this.processedPrizes.filter(p => p.id !== prize.id)
    },
    async submitPrize() {
      const payload = { ...this.currentPrize }
      if (payload.id) {
        await api.updatePrize(payload)
      } else {
        await api.createPrize(payload)
      }
      this.showFormDialog = false
      this.fetchPrizes()
    },
    handleExchange(prize) {
      this.$emit('exchange', prize)
    }
  }
}
</script>
<style lang="css">
.kids-prize-dialog {
  border-radius: 15px;
  background: linear-gradient(145deg, #f0f9ff 0%, #e0f2fe 100%);
}
.kids-prize-dialog .el-dialog__header {
  background: linear-gradient(135deg, #89cff0 0%, #5aa9e6 100%);
  border-radius: 15px 15px 0 0;
  padding: 20px;
}
.kids-prize-dialog .el-dialog__header .el-dialog__title {
  color: white;
  font-size: 24px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}
.kids-prize-dialog .admin-controls {
  margin-bottom: 20px;
  text-align: center;
}
.kids-prize-dialog .admin-controls .el-button {
  margin: 0 10px;
  border-radius: 20px;
  padding: 12px 20px;
}
.kids-prize-dialog .prize-grid {
  padding: 10px;
}
.kids-prize-dialog .prize-col {
  padding: 10px;
  transition: transform 0.3s ease;
}
.kids-prize-dialog .prize-col:hover {
  transform: translateY(-5px);
}
.kids-prize-dialog .prize-card {
  background: white;
  border-radius: 15px;
  padding: 15px;
  box-shadow: 0 4px 12px rgba(90, 169, 230, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}
.kids-prize-dialog .prize-card:hover {
  box-shadow: 0 6px 16px rgba(90, 169, 230, 0.2);
}
.kids-prize-dialog .action-icon {
  position: absolute;
  top: 10px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  transition: all 0.3s ease;
}
.kids-prize-dialog .action-icon.delete {
  right: 10px;
  background: #ff7875;
  color: white;
}
.kids-prize-dialog .action-icon.edit {
  right: 50px;
  background: #69c0ff;
  color: white;
}
.kids-prize-dialog .action-icon:hover {
  transform: scale(1.1);
}
.kids-prize-dialog .prize-image-wrapper {
  position: relative;
  height: 180px;
  background: #f8fbff;
  border-radius: 10px;
  margin-bottom: 15px;
  overflow: hidden;
}
.kids-prize-dialog .prize-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 15px;
  transition: transform 0.3s ease;
}
.kids-prize-dialog .points-bubble {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(90, 169, 230, 0.9);
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 14px;
  backdrop-filter: blur(3px);
}
.kids-prize-dialog .prize-name {
  color: #2a6496;
  margin: 0 0 15px;
  font-size: 18px;
  text-align: center;
}
.kids-prize-dialog .exchange-btn {
  width: 100%;
  border-radius: 20px;
  background: linear-gradient(135deg, #5aa9e6 0%, #89cff0 100%);
  color: white;
  border: none;
  padding: 12px;
  font-size: 16px;
}
.kids-prize-dialog .exchange-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}
.kids-prize-dialog .exchange-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>
