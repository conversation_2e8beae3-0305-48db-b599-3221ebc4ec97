<!-- SignBoard.vue -->
<template>
  <div class="sign-board">
    <!-- 头部区域 -->
    <div class="header">
      <h2 class="title"><i class="el-icon-date"></i> 每日签到</h2>
      <div class="actions">
        <el-button type="text" @click="showPrizeList">
          <i class="el-icon-present"></i> 兑换奖品
        </el-button>
        <el-button type="text" @click="showRedemptionHistory">
          <i class="el-icon-document"></i> 兑换记录
        </el-button>
      </div>
    </div>
    <!-- 数据统计 -->
    <div class="stats">
      <transition-group class="stats-container" name="flip" tag="div">
        <div key="days" class="stat-item">
          <div class="label">
            <i class="el-icon-success"></i> 连续签到
          </div>
          <div class="value animate-number">{{ checkinData.signDay }}天</div>
        </div>
        <div key="points" class="stat-item">
          <div class="label">
            <i class="el-icon-coin"></i> 当前积分
          </div>
          <div class="value animate-number">{{ checkinData.totalPoints }}</div>
        </div>
      </transition-group>
    </div>
    <!-- 签到按钮 -->
    <div class="checkin-wrapper">
      <transition mode="out-in" name="pulse">
        <el-button
            v-if="!checkinData.todayChecked"
            key="checkin"
            class="checkin-btn"
            icon="el-icon-finished"
            type="primary"
            @click="handleCheckin">
          立即签到
        </el-button>
        <el-button
            v-else
            key="checked"
            class="checkin-btn"
            disabled
            icon="el-icon-check"
            type="success">
          今日已签
        </el-button>
      </transition>
    </div>

    <!-- 奖品弹窗 -->
    <el-dialog :visible.sync="showPrizeDialog" title="🎁 可兑换奖品" width="80%">
      <prize-info :admin="isAdmin" :columns="4" :current-points="checkinData.totalPoints" :show-exchange-button="true"
                  @exchange="showRedeemForm"/>
    </el-dialog>

    <!-- 添加兑换记录弹窗 -->
    <el-dialog :visible.sync="showRecordDialog" title="📜 兑换记录" width="60%">
      <redemption-history v-if="showRecordDialog">
        <template #actions="{ row }">
          <el-button icon="el-icon-delete" plain size="mini" type="danger" @click="handleDeleteRecord(row.id)"></el-button>
        </template>
      </redemption-history>
    </el-dialog>

    <!-- 兑换表单弹窗 -->
    <el-dialog :visible.sync="showRedeemFormDialog" title="填写收货信息" width="50%">
      <el-form :model="redemptionForm" :rules="redemptionRules" label-width="80px" ref="redemptionForm">
        <el-form-item label="收货人" prop="name">
          <el-input v-model="redemptionForm.name" placeholder="请输入收货人姓名"></el-input>
        </el-form-item>
        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="redemptionForm.contact" placeholder="请输入联系方式"></el-input>
        </el-form-item>
        <el-form-item v-if="false" label="所在地区" prop="selectedRegion">
          <el-cascader
            v-model="selectedRegion"
            :options="regionOptions"
            @change="handleRegionChange"
            placeholder="请选择省/市/区">
          </el-cascader>
        </el-form-item>
        <el-form-item label="详细地址" prop="detailAddress">
          <el-input type="textarea" v-model="redemptionForm.detailAddress" placeholder="请输入详细地址信息，如街道、门牌号等"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showRedeemFormDialog = false">取消</el-button>
        <el-button type="primary" @click="submitRedeemForm">确认兑换</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import myMessage from "@/common/message";
import api from "@/common/api";
import RedemptionHistory from "@/components/tuc/prize/RedemptionHistory.vue";
import PrizeInfo from "@/components/tuc/prize/PrizeInfo.vue";
import { provinceAndCityData, regionData, provinceAndCityDataPlus, regionDataPlus, CodeToText, TextToCode } from 'element-china-area-data';

export default {
  name: 'SignBoard',
  components: {PrizeInfo: PrizeInfo, RedemptionHistory},
  data() {
    return {
      // 签到相关数据
      checkinData: {
        signDay: 0,  // 连续签到天数
        totalPoints: 0,      // 总积分
        todayChecked: false, // 今日是否已签到
        points: 0 // 本次签到积分
      },

      // 奖品相关数据
      prizes: [],            // 奖品列表数据
      showPrizeDialog: false,// 是否显示奖品弹窗
      currentPoints: 100,
      isAdmin: false,

      // 兑换记录相关
      redemptionRecords: [], // 兑换记录列表
      showRecordDialog: false,// 是否显示记录弹窗

      // 兑换表单相关
      showRedeemFormDialog: false, // 是否显示兑换表单弹窗
      redemptionForm: {
        prizeId: null,
        name: '',
        contact: '',
        detailAddress: '',
        address: ''
      },
      redemptionRules: {
        name: [
          { required: true, message: '请输入收货人姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        contact: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          { pattern: /^(1\d{10}|0\d{2,3}-\d{7,8}|\d{7,8})$/, message: '请输入正确的联系方式', trigger: 'blur' }
        ],
        // selectedRegion: [
        //   { required: true, message: '请选择所在地区', trigger: 'change' },
        //   { type: 'array', min: 3, message: '请选择完整的省市区', trigger: 'change' }
        // ],
        detailAddress: [
          { required: true, message: '请输入详细地址', trigger: 'blur' },
          { min: 5, max: 100, message: '长度在 5 到 100 个字符', trigger: 'blur' }
        ]
      },
      selectedRegion: [],
      regionOptions: regionData,
      selectedPrize: null
    }
  },
  mounted() {
    this.loadCheckinData()
  },
  methods: {
    async handleDeleteRecord(id) {
      try {
        await this.$confirm('确认删除该兑换记录吗？', '提示', {
          type: 'warning'
        })
        await api.redemptionDelete(id)
        myMessage.success('删除成功')
        // 刷新数据
        if (!this.redemptionRecords) {
          const res = await api.getRedemptionHistory()
          this.redemptionRecords = res.data.data.records
        }
        this.showRecordDialog = false
      } catch (error) {
        if (error !== 'cancel') {
          myMessage.error(error.message)
        }
      }
    },
    // 加载签到数据
    async loadCheckinData() {
      const res = await api.getPointsInfo()
      this.checkinData = res.data.data
    },

    // 执行签到
    async handleCheckin() {
      try {
        const res = await api.userSignDay()
        this.checkinData = res.data.data
        this.checkinData.todayChecked = true;
        myMessage.success(`签到成功！+${this.checkinData.points}积分`)
      } catch (error) {
        myMessage.error(error.message)
      }
    },

    // 显示奖品列表
    async showPrizeList() {
      const res = await api.getPrizeList()
      console.log("showPrizeList", res.data)
      this.prizes = res.data.data
      this.showPrizeDialog = true
    },

    // 显示兑换记录
    async showRedemptionHistory() {
      const res = await api.getRedemptionHistory()
      console.log("showRedemptionHistory", res.data)

      this.redemptionRecords = res.data.data.records
      this.showRecordDialog = true
    },

    // 显示兑换表单
    showRedeemForm(prize) {
      this.selectedPrize = prize;
      this.redemptionForm.prizeId = prize.id;
      // 重置表单
      if (this.$refs.redemptionForm) {
        this.$refs.redemptionForm.resetFields();
      }
      this.selectedRegion = [];
      this.redemptionForm.address = '';
      this.showRedeemFormDialog = true;
    },

    // 处理地区选择变化
    handleRegionChange(value) {
      // 将选择的地区编码转换为文本
      console.log("handleRegionChange", this.selectedRegion)
        const province = CodeToText[value[0]];
        const city = CodeToText[value[1]];
        const area = CodeToText[value[2]];
        this.redemptionForm.address = `${province} ${city} ${area}`;
    },

    // 提交兑换表单
    submitRedeemForm() {
      this.$refs.redemptionForm.validate(async (valid) => {
        if (valid) {
          // 检查地区是否已选择
          // if (!this.selectedRegion || this.selectedRegion.length < 3) {
          //   myMessage.warning('请选择完整的省市区');
          //   return false;
          // }
          
          try {
            // 组合完整地址
            const completeAddress = this.redemptionForm.address + ' ' + this.redemptionForm.detailAddress;
            
            // 构建兑换参数
            const redemptionData = {
              prizeId: this.redemptionForm.prizeId,
              name: this.redemptionForm.name,
              contact: this.redemptionForm.contact,
              address: completeAddress
            };
            
            await api.redeemPrize(redemptionData);
            myMessage.success('兑换成功！');
            this.loadCheckinData(); // 刷新积分数据
            this.showRedeemFormDialog = false;
            this.showPrizeDialog = false;
          } catch (error) {
            myMessage.error(error.message || '兑换失败，请重试');
          }
        } else {
          myMessage.warning('请完善收货信息');
          return false;
        }
      });
    }
  }
}
</script>

<style scoped>
:root {
  --primary-color: #409EFF;
  --secondary-color: #337ecc;
  --bg-gradient: linear-gradient(150deg, #e3f2fd 0%, #bbdefb 100%);
}

.sign-board {
  background: var(--bg-gradient);
  padding: 25px;
  border-radius: 16px;
  box-shadow: 0 4px 15px rgba(48, 144, 255, 0.15);
  transition: all 0.3s ease;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title {
  color: var(--secondary-color);
  margin: 0;
  font-size: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats {
  margin: 25px 0;
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stats-container {
  display: flex;
  justify-content: center;
  gap: 40px;
}

.stat-item {
  text-align: center;
  padding: 10px 20px;
}

.label {
  color: var(--secondary-color);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
  justify-content: center;
}

.value {
  color: var(--primary-color);
  font-size: 28px;
  font-weight: bold;
  margin-top: 8px;
}

.checkin-wrapper {
  margin: 20px 0;
  text-align: center;
}

.checkin-btn {
  width: 140px;
  height: 48px;
  font-size: 16px;
  border-radius: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.checkin-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.prize-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease;
  border: 1px solid #e8f4ff;
}

.prize-card:hover {
  transform: translateY(-5px);
}

.prize-image-wrapper {
  position: relative;
  height: 160px;
  background: #f5faff;
}

.prize-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 15px;
}

.points-required {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(64, 158, 255, 0.9);
  color: white;
  padding: 6px;
  text-align: center;
  font-size: 14px;
}

.prize-title {
  color: var(--secondary-color);
  margin: 15px 0;
  padding: 0 10px;
}

/* 数字动画 */
.animate-number {
  transition: all 0.5s ease;
}

.flip-enter-active, .flip-leave-active {
  transition: all 0.4s;
}

.flip-enter, .flip-leave-to {
  opacity: 0;
  transform: rotateX(60deg);
}

.pulse-enter-active {
  animation: pulse-in 0.3s;
}

.pulse-leave-active {
  animation: pulse-out 0.3s;
}

@keyframes pulse-in {
  0% {
    transform: scale(0.9);
    opacity: 0
  }
  100% {
    transform: scale(1);
    opacity: 1
  }
}

@keyframes pulse-out {
  0% {
    transform: scale(1);
    opacity: 1
  }
  100% {
    transform: scale(0.9);
    opacity: 0
  }
}

/* 弹窗样式覆盖 */
.blue-dialog {
  border-radius: 12px !important;
}

.blue-dialog .el-dialog__header {
  background: var(--bg-gradient);
  border-radius: 12px 12px 0 0;
}

.blue-dialog .el-dialog__title {
  color: var(--secondary-color);
  font-weight: bold;
}

/* 地区选择器样式 */
.el-cascader {
  width: 100%;
}
</style>
