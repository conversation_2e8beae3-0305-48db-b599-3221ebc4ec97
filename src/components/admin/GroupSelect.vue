<template>
  <el-select
      v-model="selectedValue"
      :multiple="isMultiple"
      :placeholder="placeholderText"
      size="medium"
      :value="selectedValue"
      @focus="loadGroupList"
  >
    <el-option
        v-for="group in groupList"
        :key="group.id"
        :label="group.name"
        :value="group.id"
    />
  </el-select>
</template>

<script>
import api from "@/common/api";

export default {
  name: 'GroupSelect',
  props: {
    value: {
      type: [Array, Number, String],
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: true
    },
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      groupList: this.dataList,
      selectedValue: this.value
    };
  },
  computed: {
    isMultiple() {
      return this.multiple;
    },
    placeholderText() {
      return this.isMultiple ? '请选择班级' : '请选择一个班级';
    }
  },
  watch: {
    selectedValue(newVal) {
      this.$emit('input', newVal);
    },
    value(newVal) {
      this.selectedValue = newVal;
    }
  },
  methods: {
    loadGroupList() {
      if (this.groupList.length === 0) {
        api.admin_listAdminGroup()
            .then(res => {
              this.groupList = res.data.data;
            })
            .catch(err => {
              console.error('Error fetching group list:', err);
            });
      }
    }
  }
};
</script>

