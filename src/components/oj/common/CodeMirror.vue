<template>
  <div style="margin: 0px 0px 15px 0px;font-size: 14px;position: relative">
    <!-- 头部区域，包含语言选择和右侧操作按钮 -->
    <el-row id="js-right-header" class="header">
      <!-- 左侧操作区域 -->
      <el-col :lg="16" :md="16" :sm="16" :xs="24">
        <div class="select-row">
          <!-- 语言选择下拉框 -->
          <span>{{ $t('m.Lang') }}:</span>
          <span>
            <el-select :value="language" class="left-adjust" size="small" @change="onLangChange">
              <el-option v-for="item in languages" :key="item" :value="item">{{ item }}</el-option>
            </el-select>
          </span>

          <!-- 重置代码按钮 -->
          <span>
            <el-tooltip :content="$t('m.Reset_Code')" placement="top">
              <el-button icon="el-icon-refresh" size="small" @click="onResetClick"></el-button>
            </el-tooltip>
          </span>
          <!-- 获取最近通过代码按钮（登录状态显示） -->
          <span v-if="isAuthenticated && !submitDisabled">
            <el-tooltip :content="$t('m.Get_Recently_Passed_Code')" placement="top">
              <el-button icon="el-icon-download" size="small" @click="getUserLastAccepetedCode"></el-button>
            </el-tooltip>
          </span>
        </div>
      </el-col>
      <!-- 右侧操作区域 -->
      <el-col :lg="8" :md="8" :sm="8" :xs="24">
        <div class="select-row fl-right">
          <!-- 文件上传按钮 -->
          <span>
            <el-tooltip :content="$t('m.Upload_file')" placement="bottom">
              <el-button icon="el-icon-upload" size="small" @click="onUploadFile"></el-button>
            </el-tooltip>
            <input id="file-uploader" style="display: none" type="file" @change="onUploadFileDone">
          </span>
          <!-- 编辑器设置下拉面板 -->
          <span>
            <el-tooltip :content="$t('m.Code_Editor_Setting')" placement="top">
              <el-popover placement="bottom" trigger="click" width="300">
                <el-button slot="reference" icon="el-icon-s-tools" size="small"></el-button>
                <div class="setting-title">{{ $t('m.Setting') }}</div>

                <!-- 主题设置 -->
                <div class="setting-item">
                  <span class="setting-item-name"><i class="fa fa-tachometer"></i> {{ $t('m.Theme') }}</span>
                  <el-select :value="theme" class="setting-item-value" size="small" @change="onThemeChange">
                    <el-option v-for="item in themes" :key="item.label" :label="$t('m.' + item.label)"
                               :value="item.value">{{ $t('m.' + item.label) }}</el-option>
                  </el-select>
                </div>
                <!-- 字体大小设置 -->
                <div class="setting-item">
                  <span class="setting-item-name"><i class="fa fa-font"></i> {{ $t('m.FontSize') }}</span>
                  <el-select :value="fontSize" class="setting-item-value" size="small" @change="onFontSizeChange">
                    <el-option v-for="item in fontSizes" :key="item" :label="item" :value="item">{{ item }}</el-option>
                  </el-select>
                </div>
                <!-- 缩进设置 -->
                <div class="setting-item">
                  <span class="setting-item-name">
                    <svg aria-hidden="true" fill="currentColor" focusable="false" height="1.2em"
                         style="vertical-align: text-bottom;"
                         viewBox="0 0 1024 1024" width="1.2em">
                      <g transform="translate(101.57 355.48)"><rect height="152.35" rx="50.78" width="812.53" x="0"
                                                                    y="0"></rect><rect height="50.78"
                                                                                       rx="25.39" width="812.53"
                                                                                       x="0"
                                                                                       y="253.92"></rect><rect
                          height="203.13" rx="25.39" width="50.78" x="0" y="177.74"></rect><rect height="203.13"
                                                                                                 rx="25.39"
                                                                                                 width="50.78"
                                                                                                 x="761.75"
                                                                                                 y="177.74"></rect></g>
                    </svg> {{ $t('m.TabSize') }}
                  </span>
                  <el-select :value="tabSize" class="setting-item-value" size="small" @change="onTabSizeChange">
                    <el-option :label="$t('m.Two_Spaces')" :value="2">{{ $t('m.Two_Spaces') }}</el-option>
                    <el-option :label="$t('m.Four_Spaces')" :value="4">{{ $t('m.Four_Spaces') }}</el-option>
                    <el-option :label="$t('m.Eight_Spaces')" :value="8">{{ $t('m.Eight_Spaces') }}</el-option>
                  </el-select>
                </div>
              </el-popover>
            </el-tooltip>
          </span>
          <!-- 专注模式切换 -->
          <template v-if="supportFocusMode">
            <span v-if="!openFocusMode" class="hidden-sm-and-down">
              <el-tooltip :content="$t('m.Enter_Focus_Mode')" placement="bottom">
                <el-button icon="el-icon-full-screen" size="small" @click="switchFocusMode(true)"></el-button>
              </el-tooltip>
            </span>
            <span v-else class="hidden-sm-and-down">
              <el-tooltip :content="$t('m.Exit_Focus_Mode')" placement="bottom">
                <el-button size="small" @click="switchFocusMode(false)">
                  <svg aria-hidden="true" fill="currentColor" focusable="false" height="0.95em" viewBox="0 0 1024 1024"
                       width="0.95em"><path
                      d="M463.04 863.32h-88.51V641.14H152.35v-88.87H463.4l-.36 311.05zM863.32 463.4H552.27l.31-311.05h88.56v222.18h222.18v88.87z"></path></svg>
                </el-button>
              </el-tooltip>
            </span>
          </template>
        </div>
      </el-col>
    </el-row>
    <!-- 代码编辑器区域 -->
    <div :style="'line-height: 1.5;font-size:'+fontSize">
      <codemirror ref="myEditor" :options="options" :value="value" class="js-right"
                  @change="onEditorCodeChange"></codemirror>
    </div>
    <!-- 测试用例抽屉 -->
    <el-drawer :modal="false" :visible.sync="openTestCaseDrawer" :with-header="false" direction="btt"
               size="40%" style="position: absolute;" @close="closeDrawer">
      <el-tabs v-model="testJudgeActiveTab" style="height: 100%;" type="border-card" @tab-click="handleClick">
        <!-- 测试用例输入页 -->
        <el-tab-pane :label="$t('m.Test_Case')" name="input" style="margin-right: 15px;margin-top: 8px;">
          <div class="mt-10">
            <el-tag v-for="(example, index) of problemTestCase" :key="index" :effect="example.active?'dark':'plain'"
                    class="tj-test-tag"
                    size="samll" type="primary"
                    @click="addTestCaseToTestJudge(example.input, example.output, index)">{{ $t('m.Fill_Case') }}
              {{ index + 1 }}
            </el-tag>
          </div>
          <el-input v-model="userInput" :rows="7" class="mt-10" maxlength="1000" resize="none" show-word-limit
                    type="textarea"></el-input>
        </el-tab-pane>
        <!-- 测试结果页 -->
        <el-tab-pane :label="$t('m.Test_Result')" name="result">
          <div v-loading="testJudgeLoding">
            <!-- 测试结果状态显示 -->
            <div v-if="testJudgeRes.status !== -10" class="tj-res-tab">
              <!-- 判题状态显示 -->
              <div class="tj-res-item">
                <div class="name">{{ $t('m.Status') }}</div>
                <div class="value">
                  <el-tag 
                    :type="getResultStausType(testJudgeRes.problemJudgeMode, testJudgeRes.status)"
                    effect="dark"
                    size="medium">
                    {{ getResultStatusName(testJudgeRes.problemJudgeMode, testJudgeRes.status, expectedOutput) }}
                  </el-tag>
                  <!-- 如果匹配预期输出，显示提示 -->
                  <span v-if="equalsExpectedOuput" class="ml-10 color-success">
                    <i class="el-icon-check"></i>
                    {{ $t('m.Matches_Expected_Output') }} {{ equalsExpectedOuput }}
                  </span>
                </div>
              </div>

              <!-- 用户输入显示 -->
              <div v-if="testJudgeRes.userInput" class="tj-res-item">
                <div class="name">{{ $t('m.Input') }}</div>
                <div class="value">
                  <el-input
                    v-model="testJudgeRes.userInput"
                    :rows="4"
                    readonly
                    type="textarea"
                    resize="none">
                  </el-input>
                </div>
              </div>

              <!-- 预期输出显示 -->
              <div v-if="testJudgeRes.expectedOutput" class="tj-res-item">
                <div class="name">{{ $t('m.Expected_Output') }}</div>
                <div class="value">
                  <el-input
                    v-model="testJudgeRes.expectedOutput"
                    :rows="4"
                    readonly
                    type="textarea"
                    resize="none">
                  </el-input>
                </div>
              </div>

              <!-- 实际输出显示 -->
              <div v-if="testJudgeRes.output" class="tj-res-item">
                <div class="name">{{ $t('m.Your_Output') }}</div>
                <div class="value">
                  <el-input
                    v-model="testJudgeRes.output"
                    :rows="4"
                    readonly
                    type="textarea"
                    resize="none">
                  </el-input>
                </div>
              </div>

              <!-- 错误信息显示 -->
              <div v-if="testJudgeRes.stderr" class="tj-res-item">
                <div class="name">{{ $t('m.Error_Info') }}</div>
                <div class="value">
                  <el-input
                    v-model="testJudgeRes.stderr"
                    :rows="4"
                    readonly
                    type="textarea"
                    resize="none">
                  </el-input>
                </div>
              </div>

              <!-- 执行时间和内存使用 -->
              <div v-if="testJudgeRes.time !== null && testJudgeRes.time !== undefined" class="tj-res-item">
                <div class="name">{{ $t('m.Time') }}</div>
                <div class="value">
                  <el-tag type="info" size="small">{{ testJudgeRes.time }} ms</el-tag>
                </div>
              </div>

              <div v-if="testJudgeRes.memory !== null && testJudgeRes.memory !== undefined" class="tj-res-item">
                <div class="name">{{ $t('m.Memory') }}</div>
                <div class="value">
                  <el-tag type="info" size="small">{{ testJudgeRes.memory }} KB</el-tag>
                </div>
              </div>

              <!-- 特殊判题模式提示 -->
              <div v-if="testJudgeRes.problemJudgeMode === 'spj'" class="tj-res-item">
                <div class="name">{{ $t('m.Judge_Mode') }}</div>
                <div class="value">
                  <el-tag type="warning" size="small">
                    <i class="el-icon-warning"></i>
                    {{ $t('m.Special_Judge') }}
                  </el-tag>
                </div>
              </div>

              <!-- 交互式判题模式提示 -->
              <div v-if="testJudgeRes.problemJudgeMode === 'interactive'" class="tj-res-item">
                <div class="name">{{ $t('m.Judge_Mode') }}</div>
                <div class="value">
                  <el-tag type="warning" size="small">
                    <i class="el-icon-connection"></i>
                    {{ $t('m.Interactive_Judge') }}
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 未开始测试时的提示 -->
            <div v-else class="tj-res-tab">
              <el-alert
                :title="$t('m.No_Test_Result')"
                type="info"
                center
                show-icon
                :closable="false">
              </el-alert>
            </div>
          </div>
        </el-tab-pane>
        <!-- 测试运行按钮 -->
        <el-tab-pane>
          <span slot="label">
            <el-tag class="tj-btn" effect="plain" type="success" @click="submitTestJudge"><i class="el-icon-video-play"> {{
                $t('m.Running_Test')
              }}</i></el-tag>
          </span>
          <!-- 未登录提示 -->
          <template v-if="!isAuthenticated">
            <div class="tj-res-tab mt-10">
              <el-alert :closable="false" :title="$t('m.Please_login_first')" center show-icon
                        type="warning"></el-alert>
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>

    <!-- 老师代码拉取弹窗 -->
    <el-dialog
        :close-on-click-modal="false"
        :visible.sync="pullCodeVisible"
        class="success-dialog"
        title="老师推送了代码给你"
        width="450px"
        @close="pullCodeVisible = false"
    >
      <!-- TODO 暂时不开启-->
      <div v-if="false" class="dialog-content">
        <codemirror :options="codeMirrorOptions" :value="pullCodeData.pushCode"></codemirror>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="danger" @click="handlePullCode(PushStatus.RejectPush)">
          {{ PushStatus.RejectPush.description }}
        </el-button>
        <el-button type="success" @click="handlePullCode(PushStatus.AcceptPush)">
          {{ PushStatus.AcceptPush.description }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import utils from '@/common/utils'
import api from '@/common/api'
import myMessage from '@/common/message'
import {codemirror} from 'vue-codemirror-lite'
import {JUDGE_STATUS, JUDGE_STATUS_RESERVE} from '@/common/constants'

/* ---------- CodeMirror 主题样式 ---------- */
import 'codemirror/theme/monokai.css'
import 'codemirror/theme/solarized.css'
import 'codemirror/theme/material.css'
import 'codemirror/theme/idea.css'
import 'codemirror/theme/eclipse.css'
import 'codemirror/theme/base16-dark.css'
import 'codemirror/theme/cobalt.css'
import 'codemirror/theme/dracula.css'

/* ---------- CodeMirror 插件和扩展 ---------- */
// 搜索高亮相关
import 'codemirror/addon/scroll/annotatescrollbar.js'
import 'codemirror/addon/search/matchesonscrollbar.js'
import 'codemirror/addon/dialog/dialog.js'
import 'codemirror/addon/dialog/dialog.css'
import 'codemirror/addon/search/searchcursor.js'
import 'codemirror/addon/search/search.js'
import 'codemirror/addon/search/match-highlighter.js'

// 语法模式支持
import 'codemirror/mode/clike/clike.js'
import 'codemirror/mode/python/python.js'
import 'codemirror/mode/pascal/pascal.js' // Pascal
// ... 其他语言模式导入 ...
// 代码编辑增强
import 'codemirror/addon/selection/active-line.js' // 当前行高亮
import 'codemirror/addon/fold/foldgutter.css' // 折叠 gutter
import 'codemirror/addon/fold/foldgutter.js' // 折叠功能
import 'codemirror/addon/edit/matchbrackets.js' // 括号匹配
import 'codemirror/addon/edit/matchtags.js' // 标签匹配
import 'codemirror/addon/edit/closetag.js' // 自动闭合标签
import 'codemirror/addon/edit/closebrackets.js' // 自动闭合括号
import 'codemirror/addon/hint/show-hint.css' // 代码提示样式
import 'codemirror/addon/hint/show-hint.js' // 代码提示功能
import 'codemirror/addon/hint/anyword-hint.js' // 任意词提示
import 'codemirror/addon/hint/javascript-hint' // JS 提示
import 'codemirror/addon/selection/mark-selection.js'
import {CodeType, PushStatus} from "@/views/tuco/js/enums"; // 选择标记

export default {
  name: 'CodeMirror',
  components: {
    codemirror
  },
  props: {
    // 代码内容（v-model 绑定）
    value: {
      type: String,
      default: ''
    },
    // 支持的语言列表
    languages: {
      type: Array,
      default: () => ['C', 'C++', 'Java', 'Python3', 'Python2']
    },
    // 当前选择语言
    language: {
      type: String,
      default: 'C'
    },
    // 编辑器高度
    height: {
      type: Number,
      default: 550
    },
    // 当前主题
    theme: {
      type: String,
      default: 'solarized'
    },
    // 字体大小
    fontSize: {
      type: String,
      default: '14px'
    },
    // Tab 缩进大小
    tabSize: {
      type: Number,
      default: 4
    },
    // 是否显示测试用例抽屉
    openTestCaseDrawer: {
      type: Boolean,
      default: false
    },
    // 题目ID
    pid: {
      type: Number
    },
    // 提交类型（公开/比赛等）
    type: {
      type: String,
      default: 'public'
    },
    // 题目测试用例
    problemTestCase: {
      type: Array
    },
    // 是否已认证
    isAuthenticated: {
      type: Boolean,
      default: false
    },
    // 是否是远程评测
    isRemoteJudge: {
      type: Boolean,
      default: false
    },
    // 是否开启专注模式
    openFocusMode: {
      type: Boolean,
      default: false
    },
    // 提交按钮是否禁用
    submitDisabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 新增数据项
      saveTimer: null,
      pullCodeVisible: false,
      pullCodeData: null,
      codeType: CodeType.OJ.code,

      // CodeMirror 配置项
      options: {
        tabSize: this.tabSize,
        mode: 'text/x-csrc',
        theme: 'solarized',
        lineNumbers: true,       // 显示行号
        line: true,
        foldGutter: true,        // 代码折叠
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        lineWrapping: true,      // 自动换行
        styleSelectedText: true, // 选中文本高亮
        showCursorWhenSelecting: true,
        highlightSelectionMatches: {showToken: /\w/, annotateScrollbar: true},
        // extraKeys: { Ctrl: 'autocomplete' }, //自定义快捷键

        matchBrackets: true,     // 括号匹配
        indentUnit: this.tabSize, // 缩进单位
        styleActiveLine: true,    // 当前行高亮
        autoCloseBrackets: true,  // 自动闭合括号
        autoCloseTags: true,      // 自动闭合标签
        hintOptions: {            // 代码提示配置
          completeSingle: false
        }
      },
      // 语言模式映射表
      mode: {C: 'text/x-csrc'},
      // 支持的主题列表
      themes: [
        {label: 'monokai', value: 'monokai'},
        {label: 'solarized', value: 'solarized'},
        {label: 'material', value: 'material'},
        {label: 'idea', value: 'idea'},
        {label: 'eclipse', value: 'eclipse'},
        {label: 'base16_dark', value: 'base16-dark'},
        {label: 'cobalt', value: 'cobalt'},
        {label: 'dracula', value: 'dracula'}
      ],
      // 支持的字体大小选项
      fontSizes: ['12px', '14px', '16px', '18px', '20px'],
      // 测试用例相关状态
      testJudgeActiveTab: 'input',
      userInput: '',
      expectedOutput: null,
      testJudgeRes: {
        status: -10,
        problemJudgeMode: 'default'
      },
      testJudgeKey: null,
      testJudgeLoding: false,
      refreshStatus: null,
      equalsExpectedOuput: null
    }
  },
  computed: {
    PushStatus() {
      return PushStatus
    },
    // 获取当前编辑器实例
    editor() {
      return this.$refs.myEditor.editor
    },
    // 当前语言（计算属性）
    currentLanguage() {
      return this.language
    },
    // 判题状态信息
    status() {
      return {
        type: JUDGE_STATUS[this.testJudgeRes.status].type,
        statusName: JUDGE_STATUS[this.testJudgeRes.status].name,
        color: JUDGE_STATUS[this.testJudgeRes.status].rgb
      }
    },
    // 是否支持专注模式
    supportFocusMode() {
      return utils.supportFocusMode(this.$route.name)
    }
  },
  watch: {
    // 监听高度变化调整编辑器尺寸
    height(newVal) {
      this.editor.setSize('100%', newVal)
      this.$nextTick(() => {
        this.editor.refresh()
      })
    },
    // 监听主题变化
    theme(newVal) {
      this.editor.setOption('theme', newVal)
    },
    // 监听用户输入变化
    userInput(newVal) {
      this.expectedOutput = null
      for (const example of this.problemTestCase) {
        example.active = example.input === newVal
        if (example.active) this.expectedOutput = example.output
      }
    }
  },
  mounted() {
    // 初始化语言模式
    utils.getLanguages().then(languages => {
      const mode = {}
      languages.forEach(lang => {
        mode[lang.name] = lang.contentType
      })
      this.mode = mode
      this.editor.setOption('mode', this.mode[this.language])
    })

    // 初始化定时任务
    this.initAutoSave();
    // 首次加载检查代码推送
    this.getLatelyMy();

    // 初始化编辑器配置
    this.editor.setOption('theme', this.theme)
    this.editor.setSize('100%', this.height)

    // 输入事件监听（触发代码提示）
    this.editor.on('inputRead', (instance, changeObj) => {
      if (changeObj.text?.[0]?.slice(-1).match(/[a-z]/i)) {
        instance.showHint({completeSingle: false})
      }
    })

    // 初始化刷新编辑器
    this.$nextTick(() => {
      this.editor.refresh()
    })
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    clearInterval(this.refreshStatus)
  },
  methods: {

    // 初始化自动保存
    initAutoSave() {
      this.saveTimer = setInterval(() => {
        if (this.isAuthenticated) {
          this.saveCode();
          this.getLatelyMy();
        }
      }, 3000);
    },
    // 保存代码方法
    saveCode() {
      const code = this.editor.getValue().trim();
      if (!code) return;
      api.saveLately({
        code: code,
        type: this.codeType
      }).catch(err => {
        console.error("自动保存失败", err);
      });
    },
    // 获取最新代码推送
    getLatelyMy() {
      api.getLatelyMy({type: this.codeType})
          .then(res => {
            this.pullCodeData = res.data.data;
            if (this.pullCodeData.pushStatus === PushStatus.Push.code) {
              this.pullCodeVisible = true;
            }
          })
          .catch(err => {
            console.error("获取代码推送失败", err);
          });
    },
    // 处理代码推送
    handlePullCode(pushStatus) {
      switch (pushStatus) {
        case PushStatus.AcceptPush:
          // 替换编辑器代码
          this.editor.setValue(this.pullCodeData.pushCode);
          break;
        case PushStatus.RejectPush:
        case PushStatus.UnPush:
        default:
          break;
      }

      api.pullCode({
        pushStatus: pushStatus.code,
        type: this.codeType
      }).finally(() => {
        this.pullCodeVisible = false;
      });
    },
    /* ---------- 编辑器事件处理 ---------- */
    onEditorCodeChange(newCode) {
      this.$emit('update:value', newCode)
    },

    /* ---------- 语言切换处理 ---------- */
    onLangChange(newVal) {
      this.editor.setOption('mode', this.mode[newVal])
      this.$emit('changeLang', newVal)
    },

    /* ---------- 主题切换处理 ---------- */
    onThemeChange(newTheme) {
      this.editor.setOption('theme', newTheme)
      this.$emit('changeTheme', newTheme)
    },

    /* ---------- 字体大小变更处理 ---------- */
    onFontSizeChange(fontSize) {
      this.fontSize = fontSize
      this.$nextTick(() => {
        this.editor.refresh()
      })
      this.$emit('update:fontSize', fontSize)
    },

    /* ---------- Tab缩进大小变更处理 ---------- */
    onTabSizeChange(tabSize) {
      this.tabSize = tabSize
      this.$emit('update:tabSize', tabSize)
      this.editor.setOption('tabSize', tabSize)
      this.editor.setOption('indentUnit', tabSize)
    },

    /* ---------- 文件上传处理 ---------- */
    onUploadFile() {
      document.getElementById('file-uploader').click()
    },

    onUploadFileDone() {
      const file = document.getElementById('file-uploader').files[0]
      const reader = new FileReader()
      reader.onload = e => {
        this.editor.setValue(e.target.result)
        document.getElementById('file-uploader').value = ''
      }
      reader.readAsText(file, 'UTF-8')
    },

    /* ---------- 测试用例相关方法 ---------- */
    addTestCaseToTestJudge(input, output, index) {
      this.userInput = input
      this.expectedOutput = output
      this.problemTestCase[index].active = true
    },

    // 提交代码测试
    submitTestJudge() {
      if (!this.validateBeforeSubmit()) return

      const submission = {
        pid: this.pid,
        language: this.language,
        code: this.value,
        type: this.type,
        userInput: this.userInput,
        expectedOutput: this.expectedOutput,
        mode: this.mode[this.language],
        isRemoteJudge: this.isRemoteJudge
      }

      api.submitTestJudge(submission).then(
          res => {
            this.handleSubmitSuccess(res.data.data)
          },
          () => {
            this.testJudgeActiveTab = 'input'
          }
      )
    },

    // 提交前验证
    validateBeforeSubmit() {
      if (!this.isAuthenticated) {
        myMessage.warning(this.$i18n.t('m.Please_login_first'))
        this.$store.dispatch('changeModalStatus', {visible: true})
        return false
      }

      if (this.value.trim() === '') {
        myMessage.error(this.$i18n.t('m.Code_can_not_be_empty'))
        return false
      }

      if (this.value.length > 65535) {
        myMessage.error(this.$i18n.t('m.Code_Length_can_not_exceed_65535'))
        return false
      }

      return true
    },

    // 处理提交成功
    handleSubmitSuccess(judgeKey) {
      this.testJudgeKey = judgeKey
      this.testJudgeActiveTab = 'result'
      this.testJudgeLoding = true
      this.startStatusPolling()
    },

    // 启动状态轮询检查
    startStatusPolling() {
      if (this.refreshStatus) clearTimeout(this.refreshStatus)

      const check = () => {
        api.getTestJudgeResult(this.testJudgeKey).then(
            res => {
              const data = res.data.data
              if (data.status !== JUDGE_STATUS_RESERVE.Pending) {
                this.handleFinalStatus(data)
              } else {
                this.refreshStatus = setTimeout(check, 1000)
              }
            },
            () => {
              this.testJudgeLoding = false
              clearTimeout(this.refreshStatus)
            }
        )
      }

      this.refreshStatus = setTimeout(check, 1000)
    },

    // 处理最终判题状态
    handleFinalStatus(data) {
      this.testJudgeRes = data
      this.equalsExpectedOuput = null
      this.testJudgeLoding = false

      if (data.status === JUDGE_STATUS_RESERVE.ac) {
        this.findMatchingTestCase()
      }
    },

    // 查找匹配的测试用例
    findMatchingTestCase() {
      this.problemTestCase.some((example, index) => {
        if (example.input === this.testJudgeRes.userInput &&
            example.output === this.testJudgeRes.expectedOutput) {
          this.equalsExpectedOuput = index + 1
          return true
        }
        return false
      })
    },

    /* ---------- 状态显示相关方法 ---------- */
    getResultStausType(problemJudgeMode, status) {
      const isSPJSuccess = problemJudgeMode === 'spj' && [0, -1].includes(status)
      return isSPJSuccess ? 'success' : this.status.type
    },

    getResultStatusName(problemJudgeMode, status, hasExpectedOutput) {
      if (problemJudgeMode === 'spj' && [0, -1].includes(status)) return 'Success'
      if (status === 0 && !hasExpectedOutput) return 'Success'
      return this.status.statusName
    },

    /* ---------- 其他UI交互方法 ---------- */
    handleClick(tab, event) {
      // 处理Tab点击事件
      console.log('Tab clicked:', tab.name)
    },

    closeDrawer() {
      this.$emit('update:openTestCaseDrawer', false)
    },

    onResetClick() {
      this.$emit('resetCode')
    },

    getUserLastAccepetedCode() {
      this.$emit('getUserLastAccepetedCode')
    },

    switchFocusMode(isOpen) {
      this.$emit('switchFocusMode', isOpen)
    }
  }
}
</script>

<style scoped>
.header {
  margin-bottom: 10px;
  margin-right: 5px;
  margin-left: 5px;
}

.header .left-adjust {
  width: 170px;
  margin-left: 5px;
}

.setting-title {
  border-bottom: 1px solid #f3f3f6;
  color: #000;
  font-weight: 700;
  padding: 10px 0;
}

.setting-item {
  display: flex;
  padding: 15px 0 0;
}

.setting-item-name {
  flex: 2;
  color: #333;
  font-weight: 700;
  font-size: 13px;
  margin-top: 7px;
}

.setting-item-value {
  width: 140px;
  margin-left: 15px;
  flex: 5;
}

.select-row {
  margin-top: 4px;
}

::v-deep .el-drawer__body {
  border: 1px solid rgb(240, 240, 240);
}

.tj-btn {
  font-size: 13px;
  font-weight: 600;
  border: 1px solid #32ca99;
}

.tj-btn:hover {
  background-color: #d5f1eb;
}

.tj-test-tag {
  margin-right: 15px;
  cursor: pointer;
}

.tj-test-tag:hover {
  font-weight: 600;
}

.tj-res-tab {
  padding-right: 15px;
}

.tj-res-item {
  display: flex;
  margin-top: 10px;
}

.tj-res-item .name {
  flex: 2;
  text-align: center;
  line-height: 34px;
  font-size: 12px;
}

.tj-res-item .value {
  flex: 10;
}

::v-deep .el-textarea__inner[readonly] {
  background-color: #f7f8f9 !important;
}

.color-gray {
  color: #999;
}

.color-success {
  color: #67c23a;
}

.mr-5 {
  margin-right: 5px;
}

.ml-10 {
  margin-left: 10px;
}

.mt-10 {
  margin-top: 10px;
}

@media screen and (max-width: 768px) {
  .select-row span {
    margin-right: 2px;
  }

  .tj-res-item .name {
    flex: 2;
  }

  .tj-res-item .value {
    flex: 5;
  }
}

@media screen and (min-width: 768px) {
  .select-row span {
    margin-right: 3px;
  }

  .fl-right {
    float: right;
  }
}

::v-deep .el-tabs__content {
  position: absolute;
  top: 40px;
  bottom: 2px;
  left: 0;
  right: 0;
  overflow-y: auto;
}

::v-deep .el-card__header {
  padding: 10px 25px;
  background-color: antiquewhite;
}

.ce-title {
  color: rgb(255, 153, 0);
  font-size: 15px;
  font-weight: 600;
}

.status-title {
  font-size: 15px;
  font-weight: 700;
}
</style>

<style>
@media screen and (max-width: 992px) {
  .CodeMirror {
    height: 550px;
  }
}

.cm-s-monokai .cm-matchhighlight {
  background-color: rgba(73, 72, 62, 0.99);
}

.cm-s-solarized .cm-matchhighlight {
  background-color: #d7d4f0;
}

.cm-s-material .cm-matchhighlight {
  background-color: rgba(128, 203, 196, 0.2);
}
</style>
