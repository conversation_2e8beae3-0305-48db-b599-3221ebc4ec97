<template>
  <div>
    <el-form ref="registerForm" :model="registerForm" :rules="rules">
      <el-form-item prop="username">
        <el-input
            v-model="registerForm.username"
            :placeholder="$t('m.Register_Username')"
            class="custom-input"
            @keyup.enter.native="handleRegister"
            prefix-icon="el-icon-phone-outline"
            size="large"
        >
          <el-button
              slot="append"
              :loading="btnPhoneLoading"
              icon="el-icon-message"
              type="primary"
              @click.native="sendRegisterPhoneCode"
              class="custom-button"
              size="small"
          >
            <span v-show="btnPhoneLoading">{{ countdownNum }}</span>
            <span class="small-text">发送</span>
          </el-button>
        </el-input>
      </el-form-item>

      <el-form-item prop="realname">
        <el-input
            v-model="registerForm.realname"
            placeholder="姓名"
            class="custom-input"
            @keyup.enter.native="handleRegister"
            prefix-icon="el-icon-s-custom"
            size="large"
        />
      </el-form-item>

      <el-form-item prop="nickname">
        <el-input
            v-model="registerForm.nickname"
            placeholder="昵称"
            class="custom-input"
            @keyup.enter.native="handleRegister"
            prefix-icon="el-icon-edit"
            size="large"
        />
      </el-form-item>

      <el-form-item prop="password">
        <el-input
            v-model="registerForm.password"
            :placeholder="$t('m.Register_Password')"
            prefix-icon="el-icon-lock"
            type="password"
            @keyup.enter.native="handleRegister"
            class="custom-input"
            size="large"
        />
      </el-form-item>

      <el-form-item prop="passwordAgain">
        <el-input
            v-model="registerForm.passwordAgain"
            :placeholder="$t('m.Register_Password_Again')"
            prefix-icon="el-icon-lock"
            type="password"
            @keyup.enter.native="handleRegister"
            class="custom-input"
            size="large"
        />
      </el-form-item>

      <el-form-item prop="groupCode">
        <el-input
            v-model="registerForm.groupCode"
            :placeholder="$t('m.Group_Code')"
            class="custom-input"
            @keyup.enter.native="handleRegister"
            prefix-icon="el-icon-s-order"
            size="large"
        />
      </el-form-item>

      <el-form-item prop="code">
        <el-input
            v-model="registerForm.code"
            :placeholder="$t('m.Register_Email_Captcha')"
            prefix-icon="el-icon-s-check"
            @keyup.enter.native="handleRegister"
            class="custom-input"
            size="large"
        />
      </el-form-item>
    </el-form>

    <div class="footer">
      <el-button
          :loading="btnRegisterLoading"
          type="primary"
          @click="handleRegister()"
      >
        {{ $t('m.Register_Btn') }}
      </el-button>
      <el-link type="primary" @click="switchMode('Login')">{{
          $t('m.Register_Already_Registed')
        }}
      </el-link>
    </div>
  </div>
</template>
<script>
import {mapActions, mapGetters} from 'vuex';
import api from '@/common/api';
import mMessage from '@/common/message';

export default {
  data() {
    const CheckUsernameNotExist = (rule, value, callback) => {
      api.checkUsernameOrEmail(value, undefined).then(
          (res) => {
            if (res.data.data.username === true) {
              callback(new Error(this.$i18n.t('m.The_username_already_exists')));
            } else {
              callback();
            }
          },
          (_) => callback()
      );
    };
    const CheckEmailNotExist = (rule, value, callback) => {
      api.checkUsernameOrEmail(undefined, value).then(
          (res) => {
            if (res.data.data.email === true) {
              callback(new Error(this.$i18n.t('m.The_email_already_exists')));
            } else {
              callback();
            }
          },
          (_) => callback()
      );
    };
    const CheckPassword = (rule, value, callback) => {
      if (this.registerForm.password !== '') {
        // 对第二个密码框再次验证
        this.$refs.registerForm.validateField('passwordAgain');
      }
      callback();
    };

    const CheckAgainPassword = (rule, value, callback) => {
      if (value !== this.registerForm.password) {
        callback(new Error(this.$i18n.t('m.Password_does_not_match')));
      }
      callback();
    };
    return {
      btnRegisterLoading: false,
      btnEmailLoading: false,
      btnPhoneLoading: false,
      countdownNum: null,
      registerForm: {
        username: '',
        realname: '',   // 姓名字段
        nickname: '',   // 昵称字段
        password: '',
        passwordAgain: '',
        groupCode: '',
        email: '',
        code: '',
      },
      sendEmailError: false,
      rules: {
        username: [
          {
            required: true,
            message: this.$i18n.t('m.Username_Check_Required'),
            trigger: 'blur',
          },
          {
            validator: CheckUsernameNotExist,
            trigger: 'blur',
            message: this.$i18n.t('m.The_username_already_exists'),
          },
          {
            max: 20,
            message: this.$i18n.t('m.Username_Check_Max'),
            trigger: 'blur',
          },
        ],

        email: [
          {
            required: true,
            message: this.$i18n.t('m.Email_Check_Required'),
            trigger: 'blur',
          },
          {
            type: 'email',
            message: this.$i18n.t('m.Email_Check_Format'),
            trigger: 'blur',
          },
          {
            validator: CheckEmailNotExist,
            message: this.$i18n.t('m.The_email_already_exists'),
            trigger: 'blur',
          },
        ],
        password: [
          {
            required: true,
            message: this.$i18n.t('m.Password_Check_Required'),
            trigger: 'blur',
          },
          {
            min: 6,
            max: 20,
            message: this.$i18n.t('m.Password_Check_Between'),
            trigger: 'blur',
          },
          {validator: CheckPassword, trigger: 'blur'},
        ],
        passwordAgain: [
          {
            required: true,
            message: this.$i18n.t('m.Password_Again_Check_Required'),
            trigger: 'blur',
          },
          {validator: CheckAgainPassword, trigger: 'change'},
        ],
        code: [
          {
            required: true,
            message: this.$i18n.t('m.Code_Check_Required'),
            trigger: 'blur',
          },
          {
            min: 6,
            max: 6,
            message: this.$i18n.t('m.Code_Check_Length'),
            trigger: 'blur',
          },
        ],
      },
    };
  },
  methods: {
    ...mapActions([
      'changeModalStatus',
      'startTimeOut',
      'changeRegisterTimeOut',
    ]),
    switchMode(mode) {
      this.changeModalStatus({
        mode,
        visible: true,
      });
    },
    countDown() {
      let i = this.time;
      if (i == 0) {
        this.btnEmailLoading = false;
        return;
      }
      this.countdownNum = i;
      setTimeout(() => {
        this.countDown();
      }, 1000);
    },
    sendRegisterEmail() {
      var emailReg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      if (!emailReg.test(this.registerForm.email)) {
        mMessage.error(this.$i18n.t('m.Email_Check_Format'));
        return;
      }
      this.btnEmailLoading = true;
      this.countdownNum = 'Waiting...';
      if (this.registerForm.email) {
        mMessage.info(this.$i18n.t('m.The_system_is_processing'));
        api.getRegisterEmail(this.registerForm.email).then(
            (res) => {
              if (res.data.msg != null) {
                mMessage.message(
                    'success',
                    this.$i18n.t('m.Register_Send_Email_Msg'),
                    5000
                );
                this.$notify.success({
                  title: this.$i18n.t('m.Success'),
                  message: this.$i18n.t('m.Register_Send_Email_Msg'),
                  duration: 2000,
                  offset: 50
                });
                this.countDown();
                this.startTimeOut({name: 'registerTimeOut'});
              }
            },
            (res) => {
              this.btnEmailLoading = false;
              this.countdownNum = null;
            }
        );
      }
    },
    sendRegisterPhoneCode() {
      // 简单的手机号正则表达式，可根据实际需求进行调整
      var phoneReg = /^1[3-9]\d{9}$/; // 假设中国大陆手机号格式
      console.log(this.registerForm.username)
      if (!phoneReg.test(this.registerForm.username)) {
        mMessage.error(this.$i18n.t('m.Phone_Check_Format'));
        return;
      }
      this.btnPhoneLoading = true;
      this.countdownNum = 'Waiting...';
      if (this.registerForm.username) {
        mMessage.info(this.$i18n.t('m.The_system_is_processing'));
        api.getRegisterPhone(this.registerForm.username).then(
            (res) => {
              if (res.data.msg != null) {
                mMessage.message(
                    'success',
                    this.$i18n.t('m.Register_Send_Phone_Msg'),
                    5000
                );
                this.$notify.success({
                  title: this.$i18n.t('m.Success'),
                  message: this.$i18n.t('m.Register_Send_Phone_Msg'),
                  duration: 2000,
                  offset: 50
                });
                this.countDown();
                this.startTimeOut({name: 'registerTimeOut'});
              }
            },
            (res) => {
              this.btnPhoneLoading = false;
              this.countdownNum = null;
            }
        );
      }
    },

    handleRegister() {
      this.$refs['registerForm'].validate((valid) => {
        if (valid) {
          const _this = this;
          let formData = Object.assign({}, this.registerForm);
          delete formData['passwordAgain'];
          this.btnRegisterLoading = true;
          api.register(formData).then(
              (res) => {
                mMessage.success(this.$i18n.t('m.Thanks_for_registering'));
                this.switchMode('Login');
                this.btnRegisterLoading = false;
              },
              (res) => {
                this.registerForm.code = '';
                this.btnRegisterLoading = false;
              }
          );
        }
      });
    },
  },
  computed: {
    ...mapGetters(['registerTimeOut', 'modalStatus']),
    time: {
      get() {
        return this.registerTimeOut;
      },
      set(value) {
        this.changeRegisterTimeOut({time: value});
      },
    },
  },
  created() {
    if (this.time != 60 && this.time != 0) {
      this.btnEmailLoading = true;
      this.countDown();
    }
  },
};
</script>


<style scoped>
.custom-input {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-button {
  border-radius: 8px;
  padding: 0 10px;
}

.small-text {
  font-size: 10px;
  color: #fff;
}

.footer {
  overflow: auto;
  margin-top: 20px;
  margin-bottom: -15px;
  text-align: center;
}

/deep/ .el-input-group__append {
  background: #25bb9b;
}

.footer .el-button--primary {
  margin: 0 0 15px 0;
  width: 100%;
}

/deep/ .el-form-item__content {
  margin-left: 0px !important;
}
</style>
