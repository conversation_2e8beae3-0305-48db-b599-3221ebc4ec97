<!-- CourseModal.vue -->
<template>
  <el-dialog
      :title="courseData.name"
      :visible.sync="visible"
      custom-class="kids-modal"
      width="500px"
      @close="handleCancel"
  >
    <div class="modal-header-tips">
      <i class="el-icon-alarm-clock"></i>
      <span>上课时间到啦！小可爱准备好学习了吗？</span>
    </div>
    <div class="course-content">
      <div class="info-item clock">
        <i class="iconfont icon-clock"></i>
        <div>
          <label>上课时间：</label>
          <p class="fun-time">
            {{ courseData.startTime }}<br>
            <span class="to-text">到</span><br>
            {{ courseData.endTime }}
          </p>
        </div>
      </div>
      <div v-if="courseData.content" class="info-item content-box">
        <i class="iconfont icon-book"></i>
        <div>
          <label>今天要学：</label>
          <p class="fun-content">{{ courseData.content }}</p>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button
          class="cancel-btn"
          @click="handleCancel">
        <i class="iconfont icon-cancel"></i> 再玩一会儿
      </el-button>
      <el-button
          class="confirm-btn"
          type="primary"
          @click="handleConfirm">
        <i class="iconfont icon-confirm"></i> 准备上课
      </el-button>
    </template>
  </el-dialog>
</template>
<script>
export default {
  name: 'CourseModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    courseData: {
      type: Object,
      default: () => ({
        name: '',
        groupName: '',
        startTime: '',
        endTime: '',
        content: '',
        url: ''
      })
    }
  },
  methods: {
    handleConfirm() {
      window.open(this.courseData.url, '_blank')
      this.handleClose()
    },
    handleCancel() {
      this.handleClose()
    },
    handleClose() {
      console.log('close handleClose')
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
/* 可爱风格样式 */
.kids-modal {
  border-radius: 15px !important;
  background: linear-gradient(135deg, #fff9e6 0%, #ffe8f4 100%);
  border: 3px solid #ff99cc;
}

.kids-modal /deep/ .el-dialog__header {
  background: #ff6699;
  border-radius: 12px 12px 0 0;
}

.kids-modal /deep/ .el-dialog__title {
  color: white !important;
  font-size: 24px;
  font-family: 'Comic Sans MS', cursive;
}

.modal-header-tips {
  background: #fff0f5;
  padding: 12px;
  margin: -20px -20px 20px;
  border-bottom: 2px dashed #ff99cc;
  display: flex;
  align-items: center;
  font-size: 18px;
  color: #ff3366;
}

.modal-header-tips i {
  font-size: 24px;
  margin-right: 10px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin: 15px 0;
  padding: 10px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(255, 102, 153, 0.1);
}

.info-item i {
  font-size: 28px;
  margin-right: 15px;
  flex-shrink: 0;
}

.icon-school {
  color: #66ccff;
}

.icon-clock {
  color: #ff9966;
}

.icon-book {
  color: #99cc66;
}

label {
  color: #ff6699;
  font-weight: bold;
  font-size: 16px;
}

.fun-text {
  color: #666;
  font-size: 18px;
  margin: 5px 0;
  font-family: 'Ma Shan Zheng', cursive;
}

.fun-time {
  color: #ff6666;
  font-size: 16px;
  line-height: 1.4;
}

.to-text {
  color: #999;
  font-size: 14px;
  margin: 3px 0;
  display: inline-block;
}

.fun-content {
  color: #666;
  font-size: 16px;
  line-height: 1.6;
  padding: 8px;
  background: #fff9f9;
  border-radius: 8px;
}

/* 按钮样式 */
.cancel-btn {
  background: #ff6666 !important;
  border-color: #ff6666 !important;
  color: white !important;
  border-radius: 20px !important;
  padding: 12px 25px !important;
  font-size: 16px;
}

.confirm-btn {
  background: #66cc99 !important;
  border-color: #66cc99 !important;
  color: white !important;
  border-radius: 20px !important;
  padding: 12px 25px !important;
  font-size: 16px;
}

.iconfont {
  margin-right: 8px;
}

@font-face {
  font-family: 'iconfont';
  src: url('//at.alicdn.com/t/c/font_1234567_abcdefghijk.woff2') format('woff2');
}

.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
}

.icon-school:before {
  content: "\e601";
}

.icon-clock:before {
  content: "\e602";
}

.icon-book:before {
  content: "\e603";
}

.icon-cancel:before {
  content: "\e604";
}

.icon-confirm:before {
  content: "\e605";
}
</style>
<!-- 在index.html中引入图标字体 -->
<!--<link href="//at.alicdn.com/t/c/font_1234567_abcdefghijk.css" rel="stylesheet">-->
