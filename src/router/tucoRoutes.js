import NotFound from "@/views/404.vue"
import Tu<PERSON> from "@/views/tuco/TuC.vue";
import Course from "@/views/tuco/Course.vue";

const tucoRoutes = [
    {
        path: '/tuco',
        name: 'TuC',
        component: TuC,
        meta: {title: 'TuC'}
    },
    {
        path: '/course',
        name: 'Course',
        component: Course,
        meta: {title: 'Course'}
    },
    {
        path: '*',
        meta: {title: '404'},
        component: NotFound,
    }
]
export default tucoRoutes
