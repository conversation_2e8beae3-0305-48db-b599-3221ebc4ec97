<template>
  <div :class="bodyClass">
    <div id="problem-main">
      <!--problem main-->
      <el-row :id="'problem-box' + '-' + $route.name" class="problem-box">

        <!--左边-->
        <el-col :id="'problem-left'+'-'+ $route.name" :lg="12" :md="12" :sm="24" class="problem-left">
          <el-tabs v-model="activeName" type="border-card" @tab-click="handleClickTab">
            <!-- 画板内容 -->
            <el-tab-pane v-loading="loading" name="problemDetail">
              <!-- 标签页标题 -->
              <span slot="label"><i class="fa fa-list-alt">{{ $t('m.Drawing_Board') }}</i></span>
              <!-- 问题详情内容区域 -->
              <div :id="'js-left'+'-'+ $route.name" :padding="10" class="js-left" shadow>
                <div class="code-run-container">
                  <!--  这里写画板 -->
                  <CodeRunner ref="codeRunner" :speed="2"/>
                </div>
              </div>

            </el-tab-pane>

            <!-- 我的分享标签页 -->
            <el-tab-pane name="myShare">
              <span slot="label"><i class="el-icon-share"></i> {{ $t('m.My_Share') }}</span>
              <template v-if="!isAuthenticated">
                <div id="js-share" style="margin:20px 0px;margin-left:-20px;">
                  <el-alert :closable="false" :description="$t('m.Login_to_view_your_code')"
                            :title="$t('m.Please_login_first')" center show-icon type="warning"></el-alert>
                </div>
              </template>
              <!-- 分享记录-->
              <template v-else>
                <div id="js-share" style="margin-right:10px;">
                  <code-table :button-text="'复制链接'" :codeList="codeShareList" @other-operation="copyShareUrl"/>
                </div>
              </template>
            </el-tab-pane>
            <!--  我的代码 -->
            <el-tab-pane name="myCode">
              <span slot="label"><i class="el-icon-code"></i> {{ $t('m.My_Code') }}</span>
              <template v-if="!isAuthenticated">
                <div id="js-share" style="margin:20px 0px;margin-left:-20px;">
                  <el-alert :closable="false" :description="$t('m.Login_to_view_your_code')"
                            :title="$t('m.Please_login_first')" center show-icon type="warning"></el-alert>
                </div>
              </template>
              <template v-else>
                <div id="js-share" style="margin-right:10px;">
                  <code-table :codeList="codeRecordList"/>
                </div>
              </template>
            </el-tab-pane>

          </el-tabs>
        </el-col>

        <!--中间-->
        <div :id="'js-center'+'-'+ $route.name" :title="$t('m.Shrink_Sidebar')"
             class="problem-resize hidden-sm-and-down">
          <span>⋮</span>
          <span>
            <el-tooltip v-if="!toResetWatch"
                        :content="toWatchProblem? $t('m.View_Problem_Content'): $t('m.Only_View_Problem')"
                        placement="right">
              <el-button circle class="right-fold fold" icon="el-icon-caret-right" size="mini"
                         @click.stop="onlyWatchProblem"></el-button>
            </el-tooltip>
            <el-tooltip v-else :content="$t('m.Put_away_the_full_screen_and_write_the_code')" placement="left">
              <el-button circle class="left-fold fold" icon="el-icon-caret-left" size="mini"
                         @click.stop="resetWatch(false)"></el-button>
            </el-tooltip>

          </span>
        </div>

        <!--右边-->
        <el-col v-show="shareType !=='OnlyDraw'" :id="'problem-right' + '-' + $route.name" :lg="12" :md="12" :sm="24"
                class="problem-right">
          <el-card id="submit-code" :padding="10" class="submit-detail" shadow="always">
            <div id="js-right-bottom" class="control-panel">
              <el-row>
                <el-col :lg="10" :md="10" :sm="24" class="auth-prompt">
                  <!-- 当用户未登录时，显示登录提示 -->
                  <div v-if="!isAuthenticated">
                    <el-alert :closable="false" class="alert-message" effect="dark" show-icon type="info">
                      {{ $t('m.Please_login_first') }}
                    </el-alert>
                  </div>
                  <!-- 显示提交状态 -->
                  <div v-if="statusVisible" class="status-message"></div>
                </el-col>
                <el-col :lg="14" :md="14" :sm="24" class="action-buttons">
                  <!-- 点击按钮 执行获取到的代码 -->
                  <el-button class="play-button" icon="el-icon-video-play" size="small" type="primary"
                             @click.native="submitCode()">
                    <span>{{ $t('m.runCode') }}</span>
                  </el-button>
                  <!-- 点击按钮 保存代码 -->
                  <el-button icon="el-icon-document-add" size="small" type="primary" @click.native="saveCode">
                    <span>{{ $t('m.saveCode') }}</span>
                  </el-button>
                  <!-- 点击按钮 分享代码 -->
                  <el-button icon="el-icon-share" size="small" type="info" @click.native="openShare()">
                    <span>{{ $t('m.shareCode') }}</span>
                  </el-button>
                  <el-button class="highlight-button" size="small" @click="highlightLine++">
                    Highlight Line
                  </el-button>
                </el-col>
              </el-row>
            </div>

            <div>
              <!-- 选择分享方式的弹窗 -->
              <el-dialog
                  v-if="shareDialogVisible"
                  :close-on-click-modal="false"
                  :visible.sync="shareDialogVisible"
                  class="share-dialog"
                  title="选择分享方式"
                  width="450px"
                  @close="resetSelection"
              >
                <div class="dialog-content">
                  <el-radio-group v-model="selectedOption">
                    <el-radio
                        v-for="(option, index) in radioOptions"
                        :key="index"
                        :label="option.value"
                        class="radio-option"
                    >
                      {{ option.label }} ({{ option.value }})
                    </el-radio>
                  </el-radio-group>
                </div>

                <div slot="footer" class="dialog-footer">
                  <el-button class="cancel-btn" @click="shareDialogVisible = false">取消</el-button>
                  <el-button class="confirm-btn" type="primary" @click="confirmShare">确认分享</el-button>
                </div>
              </el-dialog>

              <!-- 分享成功后的弹窗 -->
              <el-dialog
                  :close-on-click-modal="false"
                  :visible.sync="shareSuccessVisible"
                  class="success-dialog"
                  title="分享成功"
                  width="450px"
                  @close="shareSuccessVisible = false"
              >
                <div class="dialog-content">
                  <p>分享成功！</p>
                  <p>分享链接: <a :href="shareUrl" target="_blank">{{ shareUrl }}</a></p>
                </div>
                <div slot="footer" class="dialog-footer">
                  <el-button class="close-btn" @click="shareSuccessVisible = false">关闭</el-button>
                  <el-button class="copy-btn" type="primary" @click="copyText(shareUrl)">复制链接</el-button>
                </div>
              </el-dialog>

              <!-- 老师代码拉取弹窗 -->
              <el-dialog
                  :close-on-click-modal="false"
                  :visible.sync="pullCodeVisible"
                  class="success-dialog"
                  title="老师推送了代码给你"
                  width="450px"
                  @close="pullCodeVisible = false"
              >
                <!-- TODO 暂时不开启-->
                <div v-if="false" class="dialog-content">
                  <codemirror :options="codeMirrorOptions" :value="pullCodeData.pushCode"></codemirror>
                </div>
                <div slot="footer" class="dialog-footer">
                  <el-button type="danger" @click="handlePullCode(PushStatus.RejectPush)">
                    {{ PushStatus.RejectPush.description }}
                  </el-button>
                  <el-button type="success" @click="handlePullCode(PushStatus.AcceptPush)">
                    {{ PushStatus.AcceptPush.description }}
                  </el-button>
                </div>
              </el-dialog>
            </div>

            <!-- CodeMirror组件用于代码编辑，接收多个属性和事件处理函数 -->
            <tu-code-mirror ref="codeEditor" :highlight-line="highlightLine"></tu-code-mirror>

          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {mapGetters} from "vuex";

import CodeMirror from "@/components/oj/common/CodeMirror.vue";
import Pagination from "@/components/oj/common/Pagination";
import ProblemHorizontalMenu from "@/components/oj/common/ProblemHorizontalMenu";
import Markdown from "@/components/oj/common/Markdown";
import TuCodeMirror from "@/components/tuc/TuCodeMirror.vue";
import api from "@/common/api";
import myMessage from "@/common/message";
import CodeTable from "@/components/tuc/CodeTable.vue";
import "codemirror/lib/codemirror.css";
import "codemirror/mode/clike/clike.js";
import "codemirror/mode/javascript/javascript.js";
import "codemirror/addon/edit/closebrackets.js";
import "codemirror/addon/selection/active-line.js";

import {codemirror} from "vue-codemirror-lite";
import {CodeType, PushStatus} from "@/views/tuco/js/enums";
import CodeRunner from "@/components/tuc/CodeRunner.vue";
import CPlusTemplate from "@/components/tuc/js/codeTemplate";


export default {
  name: "Tuc",
  components: {
    CodeRunner,
    CodeTable, CodeMirror, TuCodeMirror, Pagination, ProblemHorizontalMenu, Markdown, codemirror
  },
  data() {
    return {
      // codeMirrorCode: '', // 代码编辑器的代码
      codeCheckConfig: {
        cmd: [
          {
            args: [
              "/usr/bin/g++", // 默认的编译器路径
              "a.cc",          // 源文件
              "-o",            // 输出选项
              "a"              // 输出文件名
            ],
            env: [
              "PATH=/usr/bin:/bin" // 环境变量路径
            ],
            files: [
              {
                content: ""        // 初始内容为空
              },
              {
                name: "stdout",    // 标准输出文件配置
                max: 10240         // 最大文件大小（字节）
              },
              {
                name: "stderr",    // 错误输出文件配置
                max: 10240         // 最大文件大小（字节）
              }
            ],
            cpuLimit: 10000000000,      // CPU时间限制
            memoryLimit: 104857600,     // 内存限制（字节）
            procLimit: 50,              // 进程限制
            copyIn: {
              "a.cc": {
                content: "code"         // 源代码内容占位符
              }
            },
            copyOut: [
              "stdout",    // 设置需要导出的标准输出
              "stderr"     // 设置需要导出的错误输出
            ],
            copyOutCached: [
              "a"          // 缓存生成的输出文件
            ]
          }
        ]
      },
      codeCheckRes: {},
      highlightLine: -1,
      // 控制状态显示的可见性
      statusVisible: false,
      // 是否正在提交
      submitting: false,
      // 代码提交内容
      code: "",
      speed: 1, // 默认速度
      // 编程语言
      language: "C++",
      // 当前激活的标签页名称
      activeName: "problemDetail",
      // 是否正在加载
      loading: false,
      // 页面body的样式类
      bodyClass: "",
      // 是否打开专注模式
      openFocusMode: false,
      // 我的代码记录列表
      codeRecordList: [],
      // 我的分享记录列表
      codeShareList: [],
      codeShare: {},
      codeRecord: {
        id: undefined,
        uid: undefined,
        username: undefined,
        nickname: undefined,
        realname: undefined,
        shareKey: undefined,
        code: undefined,
        createTime: undefined,
        updateTime: undefined
      },
      // 你的数据属性
      intervalId: null,
      // 拉取的代码数据
      pullCodeData: {
        pushStatus: undefined,
        pushCode: undefined
      },
      codeMirrorOptions: {
        mode: "text/x-c++src",
        theme: "default",
        lineNumbers: true,
        tabSize: 4,
        autoCloseBrackets: true,
        styleActiveLine: true,

        readOnly: true,
      },
      // 分享相关
      shareId: null, // 用于存储 shareId
      shareType: null,// 用于存储 shareType,
      shareDialogVisible: false, // 控制选择分享方式的弹窗显示
      shareSuccessVisible: false, // 控制分享成功弹窗显示
      pullCodeVisible: false, // 控制拉取代码弹窗显示

      selectedOption: 'Original',  // 存储当前选中的分享方式
      radioOptions: [
        {label: '原页面', value: 'Original'},
        {label: '只显示动画', value: 'OnlyDraw'},
        // {label: '全屏页面', value: 'FullOriginal'},
        // {label: '全屏幕动画', value: 'FullDraw'},
      ],
      shareUrl: '', // 保存分享成功后的 URL
    }
  },
  created() {

  },

  mounted() {
    this.dragControllerDiv();
    this.getList();

    this.intervalId = setInterval(() => {
      this.getLatelyMy();
      this.saveLatelyCode();
    }, 3000); // 每隔3秒调用一次


    // 初始化获取参数
    this.shareId = this.$route.query.shareId;
    this.shareType = this.$route.query.shareType;
    // 处理初始化时的 shareType
    this.handleShare();
  },
  beforeDestroy() {
    // 在组件销毁之前清除定时器
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  },
  methods: {
    highlightSpecificLine() {
      // Function to highlight a specific line, line number 1 used as an example
      this.highlightLine = 1;
    },

    // 点击确认分享时，传递选中的值
    confirmShare() {
      if (this.selectedOption) {
        this.$emit('confirm-share', this.selectedOption);
        this.shareDialogVisible = false; // 关闭选择分享方式的弹窗

        // 获取当前的代码
        let code = this.$refs.codeEditor.getCode();

        // 调用 API 保存分享数据
        api.saveCodeShare({code: code, shareType: this.selectedOption}).then((res) => {
          this.shareUrl = res.data.data.url; // 获取分享的 URL
          myMessage.success("分享成功，链接为：" + this.shareUrl);
          this.getList();
          // 显示分享成功的弹窗
          this.shareSuccessVisible = true;
        }).catch((err) => {
          myMessage.error("分享失败");
        });
      } else {
        myMessage.warning("请选择一个分享选项");
      }
    },

    copyText(text) {
      if (text) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          // 使用 Clipboard API 复制文本
          navigator.clipboard.writeText(text)
              .then(() => {
                myMessage.success("链接已复制到剪贴板");
              })
              .catch((err) => {
                myMessage.error("复制失败，请手动复制链接");
              });
        } else {
          // Fallback: 使用 document.execCommand('copy')
          const textArea = document.createElement("textarea");
          textArea.value = text;
          // 确保 textarea 不可见
          textArea.style.position = "fixed";
          textArea.style.opacity = 0;
          document.body.appendChild(textArea);
          textArea.select();
          try {
            const successful = document.execCommand('copy');
            if (successful) {
              myMessage.success("链接已复制到剪贴板");
            } else {
              myMessage.error("复制失败，请手动复制链接");
            }
          } catch (err) {
            myMessage.error("复制失败，请手动复制链接");
          }
          document.body.removeChild(textArea);
        }
      } else {
        myMessage.error("链接为空，无法复制");
      }
    },

    // 重置选中的值（当弹窗关闭时）
    resetSelection() {
      this.selectedOption = ''; // 清除选中的值
    },
    // 处理代码分享
    handleShare() {
      console.log("handleShare-当前的shareId：", this.shareId)
      const shareId = parseInt(this.shareId);
      if (!shareId || shareId <= 0) return; // 简化验证条件

      // 调用接口获取代码分享数据
      api.getCodeShare({id: shareId})
          .then((res) => {
            this.codeShare = res.data.data;
            const code = this.codeShare.code;
            this.$refs.codeEditor.setCode(code);
            this.submitCode(code);
          })
          .catch(() => {
            myMessage.error("分享链接不存在");
          });

    },

    // 处理表格操作
    copyShareUrl(rowData) {
      this.copyText(rowData.url);
    },

    // 初始化要获取的数据
    getList() {
      this.listCodeRecord();
      this.listCodeShare();
    },

    // 获取保存的代码记录
    listCodeRecord(data) {
      api.listCodeRecord().then((res) => {
        this.codeRecordList = res.data.data;
        console.log("获取我的代码记录", this.codeRecordList)
      }).catch((err) => {
        console.log("获取我的代码记录失败", err);
      });
    },

    // 获取分享的代码记录
    listCodeShare(data) {
      api.listCodeShare().then((res) => {
        console.log("获取分享记录", res)
        this.codeShareList = res.data.data;
      }).catch((err) => {
        console.log("获取分享记录失败", err);
      });
    },

    // 保存最近代码
    saveLatelyCode() {
      // 获取编辑器代码并验证是否为空
      const code = this.$refs.codeEditor.getCode()?.trim();
      if (!code) return;

      // 准备数据并调用保存接口
      api.saveLately({code: code, type: CodeType.Tuc.code})
          .then((res) => {
            // 保存最近代码成功
            // console.log("保存最近代码成功", res);
          })
          .catch((err) => {
            // 保存最近代码失败
            console.error("保存最近代码失败", err);
          });

    },

    // 处理拉取代码函数
    handlePullCode(pushStatus) {
      switch (pushStatus) {
        case PushStatus.AcceptPush:
          // 使用 codeEditor 替换代码内容
          this.$refs.codeEditor.setCode(this.pullCodeData.pushCode);
          break;
        case PushStatus.RejectPush:
        case PushStatus.UnPush:
        default:
          break;
      }
      api.pullCode({pushStatus: pushStatus.code, type: CodeType.Tuc.code})
          .then((res) => {
            console.log("更新代码", res);
          })
          .catch((err) => {
            console.log("更新代码", err);
          });

      this.pullCodeVisible = false;
    },

    // 查看最近代码，以及老师有没有推送代码
    getLatelyMy() {
      if (!this.isAuthenticated) {
        return
      }
      api.getLatelyMy({type: CodeType.Tuc.code})
          .then((res) => {
            this.pullCodeData = res.data.data;
            // console.log("拉取最近代码", this.pullCodeData)
            if (this.pullCodeData.pushStatus === PushStatus.Push.code) {
              console.log("判断成功", this.pullCodeData.pushStatus, PushStatus.Push.code)
              this.pullCodeVisible = true;
            }
          })
          .catch((err) => {
            // 保存最近代码失败
            console.log("保存最近代码失败", err);
          });
    },

    // 打开分享页面
    openShare() {
      this.$refs.codeEditor.getCode();
      if (!this.$refs.codeEditor.getCode()) {
        myMessage.error("没有写代码");
        return;
      }
      this.shareDialogVisible = true
    },

    // 保存代码
    saveCode() {
      console.log("保存代码");
      let code = this.$refs.codeEditor.getCode();
      api.saveCodeRecord({code: code}).then((res) => {
        myMessage.success("保存成功")
        this.getList();
      }).catch((err) => {
        myMessage.error("保存失败", err);
      });
    },

    // 运行代码
    submitCode(inCode) {
      this.highlightLine = -1;
      // 调用子组件的方法运行代码
      // Fetch and process the submitted code
      const code = inCode?.trim()
          ? inCode
          : this.$refs.codeEditor.getCode();
      this.$refs.codeRunner.runCode(code);

      const convertedCode = CPlusTemplate(code);

      this.codeCheckConfig.cmd[0].copyIn["a.cc"].content = convertedCode;
      api.checkCPlus(this.codeCheckConfig)
          .then(res => {
            this.codeCheckRes = res.data[0];
            console.log("run", res.data)
            console.log(this.codeCheckRes)
            if (this.codeCheckRes.status === "Accepted") {
              myMessage.success("Tuc 运行成功");
            } else {
              const number = this.parseErrorLine(this.codeCheckRes.files.stderr);
              this.highlightLine = number - 74;
              console.log("highlightLine", number, this.highlightLine)
              myMessage.error("Tuc 运行错误" + "\n" + this.codeCheckRes.files.stderr);
            }
          })
    },

    parseErrorLine(message) {
      // 使用正则表达式匹配文件名和行号
      const regex = /a\.cc:(\d+):\d+/;
      const match = message.match(regex);
      if (match) {
        const lineNumber = parseInt(match[1], 10); // 转换匹配到的行号为整数
        return lineNumber;
      } else {
        throw new Error("行号无法解析");
      }
    },
    // 代码转换
    translateCppToJs(cppCode) {
      // 去掉头文件声明
      cppCode = cppCode.replace(/#include\s*<.*>/g, '');

      // 替换整数、双精度、字符串和字符声明
      cppCode = cppCode.replace(/\bint\b/g, 'let');
      cppCode = cppCode.replace(/\bdouble\b/g, 'let');
      cppCode = cppCode.replace(/\bstring\b/g, 'let');
      cppCode = cppCode.replace(/\bchar\b/g, 'let');

      // 替换输入语句为浏览器输入提示
      cppCode = cppCode.replace(/\bcin\s*>>\s*(\w+);/g, 'let $1 = prompt("");');

      // 将C++输出替换为JS的console.log，并在同一表达式中添加闭括号
      // 假设每个输出表达式使用'<<'类似于'cout << "text" << variable;'
      // cppCode = cppCode.replace(/\bcout\s*<<\s*(.+?);/g, (match, p1) => {
      //   return `console.log(${p1.trim().replace(/\s*<<\s*/g, ', ')});`;
      // });
      //
      // // 基本循环替换，将for循环中的int替换为let
      // cppCode = cppCode.replace(/\bfor\s*\(\s*int/g, 'for (let');
      //
      // // 替换C++的main函数结构，不再需要main方法
      // cppCode = cppCode.replace(/\bint\s+main\s*\(\s*\)\s*{/, '');
      // cppCode = cppCode.replace(/\breturn\s+0\s*;/, '');
      // cppCode = cppCode.replace(/}\s*$/, '');
      //
      // // 处理字符常量（需要将单引号改成双引号）
      // cppCode = cppCode.replace(/'(.*?)'/g, '"$1"');

      return cppCode.trim();
    },

    dragControllerDiv() {
      // Elements with dynamic IDs based on route name
      const resize = document.getElementById(`js-center-${this.$route.name}`);
      const left = document.getElementById(`problem-left-${this.$route.name}`);
      const right = document.getElementById(`problem-right-${this.$route.name}`);
      const box = document.getElementById(`problem-box-${this.$route.name}`);

      const mouseMoveHandler = (e) => {
        // Calculate width based on mouse movement
        const moveLen = Math.min(
            Math.max(0, e.clientX - startX + resizeLeft),
            box.offsetWidth - 580
        );

        const leftWidthPercent = (moveLen / box.offsetWidth) * 100;
        resize.style.left = `${leftWidthPercent}%`;
        left.style.width = `${leftWidthPercent}%`;
        right.style.width = `${100 - leftWidthPercent}%`;

        this.toWatchProblem = moveLen < 420;
      };

      // Event handlers to manage mouse drag operations
      let startX, resizeLeft;
      resize.onmousedown = (e) => {
        startX = e.clientX;
        resizeLeft = resize.offsetLeft;
        resize.style.background = "#818181"; // Change color to indicate drag started

        // Bind event handlers
        document.onmousemove = mouseMoveHandler;
        document.onmouseup = () => {
          resize.style.background = "#d6d6d6"; // Revert background color
          document.onmousemove = null;
          document.onmouseup = null;
        };
      };
    },
    onlyWatchProblem() {
      // Toggle only-watch problem mode
      if (this.toWatchProblem) {
        this.resetWatch(true);
        this.toWatchProblem = false;
        return;
      }

      const resize = document.getElementById(`js-center-${this.$route.name}`);
      const left = document.getElementById(`problem-left-${this.$route.name}`);
      const right = document.getElementById(`problem-right-${this.$route.name}`);
      const box = document.getElementById(`problem-box-${this.$route.name}`);

      resize.style.left = `${box.clientWidth - 10}px`;
      left.style.width = `${box.clientWidth - 10}px`;
      right.style.width = "0px";
      right.style.display = "none";
      this.toResetWatch = true;
    },
    resetWatch(minLeft = false) {
      const resize = document.getElementById(`js-center-${this.$route.name}`);
      const left = document.getElementById(`problem-left-${this.$route.name}`);
      const right = document.getElementById(`problem-right-${this.$route.name}`);
      const box = document.getElementById(`problem-box-${this.$route.name}`);

      const leftWidth = minLeft ? 431 : box.clientWidth - 580;
      const leftWidthPercent = (leftWidth / box.offsetWidth) * 100;

      resize.style.left = `${leftWidthPercent}%`;
      left.style.width = `${leftWidthPercent}%`;
      right.style.width = `${100 - leftWidthPercent}%`;
      right.style.display = "";
      this.toResetWatch = false;
    }
  }
  ,
  computed: {
    PushStatus() {
      return PushStatus
    },
    ...mapGetters([
      "problemSubmitDisabled",
      "contestRuleType",
      "ContestRealTimePermission",
      "contestStatus",
      "isAuthenticated",
      "userInfo",
      "isSuperAdmin",
      "isAdminRole",
      "canSubmit",
      "websiteConfig"
    ])

  }
  ,

  watch: {
    /**
     * 当路由发生变化时执行的函数
     * 重置组件状态，准备处理新的路由请求
     */
    $route() {
      this.submitted = false; // 重置提交状态
      this.submitDisabled = false; // 启用提交功能
      this.submitting = false; // 重置提交中状态
      this.statusVisible = false; // 隐藏状态显示
    }
    ,

    /**
     * 监听用户认证状态变化的函数
     * 当用户认证状态变为已认证时，重置组件状态
     * @param {boolean} newVal - 用户认证状态的新值
     */
    isAuthenticated(newVal) {
      if (newVal === true) {
        this.submitted = false; // 重置提交状态
        this.submitDisabled = false; // 启用提交功能
        this.submitting = false; // 重置提交中状态
        this.statusVisible = false; // 隐藏状态显示
        this.init(); // 重新初始化组件
      }
    }
    ,

    /**
     * 当活动标签发生变化时执行的函数
     * 调整视口高度以适应新的标签页
     */
    activeName() {
    },

    // // 动态监听路由参数 shareId 的变化
    // '$route.query.shareId': {
    //   handler(newVal) {
    //     this.shareId = newVal;
    //     this.handleShareType(); // 处理 shareType 的逻辑
    //
    //   },
    //   immediate: true // 确保初始化时也触发
    // },
    // // 动态监听路由参数 shareType 的变化
    // '$route.query.shareType': {
    //   handler(newVal) {
    //     this.shareType = newVal;
    //     this.handleShareType(); // 处理 shareType 的逻辑
    //   },
    //   immediate: true
    // }
  }
  ,
}
;
</script>

<style scoped>
.control-panel {
  background-color: #f7f8fa;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auth-prompt {
  margin-top: 4px;
}

.alert-message {
  font-size: 14px;
  font-weight: bold;
  line-height: 1.5;
}

.status-message {
  background-color: #e0f7fa;
  padding: 5px;
  border-radius: 4px;
  margin-top: 4px;
  text-align: center;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 4px;
  gap: 10px;
}

.play-button {
  background-color: #42b983;
  border: none;
  transition: background-color 0.3s ease;
}

.play-button:hover {
  background-color: #369f75;
}

.highlight-button {
  border: 1px solid #dcdfe6;
  color: #606266;
  transition: all 0.3s ease;
}

.highlight-button:hover {
  border-color: #42b983;
  color: #42b983;
}


a {
  color: #3091f2 !important;
}

.problem-menu span {
  margin-left: 5px;
}


.submit-detail {
  height: 100%;
}


.js-left {
  padding-right: 15px;
}

.code-run-container {
  margin: 20px auto;
}

@media screen and (min-width: 992px) {


  .js-left {
    height: 730px !important;
    overflow-y: auto;
  }


  #js-submission {
    overflow-y: auto;
  }

  .submit-detail {
    overflow-y: auto;
  }


  .problem-menu span {
    margin-left: 10px;
  }


}

@media screen and (min-width: 992px) {
  .problem-box {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .problem-left {
    width: 50%; /*左侧初始化宽度*/
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    float: left;
  }

  .problem-resize {
    cursor: col-resize;
    position: absolute;
    top: 330px;
    left: 50%;
    background-color: #d6d6d6;
    border-radius: 5px;
    width: 10px;
    height: 50px;
    background-size: cover;
    background-position: center;
    font-size: 32px;
    color: white;
  }

  .problem-resize:hover .right-fold {
    display: block;
  }

  .problem-resize:hover .fold:before {
    content: "";
    position: absolute;
    display: block;
    width: 6px;
    height: 24px;
    left: -6px;
  }

  .right-fold {
    position: absolute;
    display: none;
    font-weight: bolder;
    margin-left: 15px;
    margin-top: -35px;
    cursor: pointer;
    z-index: 1000;
    text-align: center;
  }

  .left-fold {
    position: absolute;
    font-weight: bolder;
    margin-left: -40px;
    margin-top: 10px;
    cursor: pointer;
    z-index: 1000;
    text-align: center;
  }

  .fold:hover {
    color: #409eff;
    background: #fff;
  }

  /*拖拽区鼠标悬停样式*/
  .problem-resize:hover {
    color: #444444;
  }

  .problem-right {
    height: 100%;
    float: left;
    width: 50%;
  }
}

@media screen and (max-width: 992px) {
  .submit-detail {
    padding-top: 20px;
  }

  .submit-detail {
    height: 100%;
  }
}


.example pre {
  flex: 1 1 auto;
  align-self: stretch;
  border-style: solid;
  background: transparent;
  padding: 5px 10px;
  white-space: pre;
  margin-top: 10px;
  margin-bottom: 10px;
  background: #f1f1f1;
  border: 1px dashed #e9eaec;
  overflow: auto;
  font-size: 1.1em;
  margin-right: 7%;
}

#submit-code {
  height: auto;
}

#submit-code {
  float: left;
}


#submit-code .status span {
  margin-left: 10px;
}


/* 弹窗样式 */
.dialog-content {
  padding: 10px;
  font-family: Arial, sans-serif;
  line-height: 1.5;
}

.dialog-title {
  color: #333;
  font-weight: bold;
  margin-bottom: 10px;
}

/* 让 CodeMirror 加载它自己的样式 */
.CodeMirror {
  border-radius: 5px;
  border: 1px solid #ddd;
  height: auto;
}

.radio-option {
  margin-bottom: 15px;
}

.dialog-footer {
  text-align: center;
  margin: 20px auto;
  padding: 10px 20px;
}

.el-button {
  font-size: 14px;
  padding: 6px 20px;
}

/* 选择分享方式的弹窗 */
.share-dialog .el-dialog__header {
  background-color: #f5f7fa;
  font-weight: 500;
  font-size: 16px;
}

.share-dialog .el-dialog__body {
  padding: 0;
}

/* 分享成功的弹窗 */
.success-dialog .el-dialog__header {
  background-color: #f5f7fa;
  font-weight: 500;
  font-size: 16px;
}

.success-dialog .el-dialog__body {
  padding: 0;
}

.cancel-btn {
  background-color: #f1f1f1;
  color: #333;
  margin-right: 10px;
}

.confirm-btn,
.copy-btn {
  background-color: #409eff;
  color: #fff;
}

.close-btn {
  background-color: #f1f1f1;
  color: #333;
}

a {
  color: #409eff;
  text-decoration: none;
}
</style>
