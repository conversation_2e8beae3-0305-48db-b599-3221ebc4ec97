<template>
  <div class="admin-container">
    <div v-if="!mobileNar">
      <!-- 左侧垂直菜单 -->
      <el-menu :default-active="currentPath" :router="true" class="vertical_menu">
        <!-- Logo和语言切换 -->
        <el-tooltip :content="$t('m.Click_To_Change_Web_Language')" effect="dark" placement="bottom">
          <div class="logo" @click="changeWebLanguage(webLanguage == 'zh-CN' ? 'en-US' : 'zh-CN')">
            <img :src="imgUrl" alt="Online Judge Admin"/>
          </div>
        </el-tooltip>

        <!-- 动态生成的菜单项 -->
        <template v-for="(menu, index) in menusPc">
          <el-menu-item v-if="!menu.submenu" :index="menu.path">
            <i :class="menu.icon" aria-hidden="true"></i>{{ $t(menu.label) }}
          </el-menu-item>

          <el-submenu v-if="menu.submenu" :index="menu.index">
            <template slot="title">
              <i :class="menu.icon" aria-hidden="true"></i>{{ $t(menu.label) }}
            </template>
            <el-menu-item v-for="(item, subIndex) in menu.submenu" :key="subIndex" :index="item.path">
              {{ $t(item.label) }}
            </el-menu-item>
          </el-submenu>
        </template>

      </el-menu>

      <!-- 顶部工具栏 -->
      <div id="header">
        <el-row>
          <el-col :span="18">
            <!-- 面包屑导航 -->
            <div class="breadcrumb-container">
              <el-breadcrumb separator-class="el-icon-arrow-right">
                <el-breadcrumb-item :to="{ path: '/admin/' }">
                  {{ $t('m.Home_Page') }}
                </el-breadcrumb-item>
                <el-breadcrumb-item v-for="item in routeList" :key="item.path">
                  {{ $t('m.' + item.meta.title.replaceAll(' ', '_')) }}
                </el-breadcrumb-item>
              </el-breadcrumb>
            </div>
          </el-col>
          <el-col v-show="isAuthenticated" :span="6">
            <!-- KaTeX 编辑器 -->
            <i class="fa fa-font katex-editor fa-size" @click="openKatexEditor"></i>
            <!-- 用户头像和下拉菜单 -->
            <avatar
                :inline="true"
                :size="30"
                :src="userInfo.avatar"
                :username="userInfo.username"
                class="drop-avatar"
                color="#FFF"
            ></avatar>
            <el-dropdown style="vertical-align: middle;" @command="handleCommand">
          <span>
            {{ userInfo.username }}<i class="el-icon-caret-bottom el-icon--right"></i>
          </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="logout">{{ $t('m.Logout') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-col>
        </el-row>
      </div>
    </div>
    <div v-else>
      <!-- 手机端导航栏 -->
      <mu-appbar class="mobile-nav" color="primary">
        <!-- 左侧抽屉开关 -->
        <mu-button slot="left" icon @click="opendrawer = !opendrawer">
          <i class="el-icon-s-unfold"></i>
        </mu-button>
        <!-- 网站短名称 -->
        {{ websiteConfig.shortName ? `${websiteConfig.shortName} ADMIN` : 'ADMIN' }}

        <!-- KaTeX 显示按钮 -->
        <mu-menu v-show="isAuthenticated" slot="right">
          <mu-button flat @click="katexVisible = true">
            <i class="fa fa-font katex-editor"></i>
          </mu-button>
        </mu-menu>

        <!-- 用户下拉菜单 -->
        <mu-menu v-show="isAuthenticated" slot="right" :open.sync="openusermenu">
          <mu-button flat>
            {{ userInfo.username }}<i class="el-icon-caret-bottom"></i>
          </mu-button>
          <mu-list slot="content" @change="handleCommand">
            <mu-list-item button value="logout">
              <mu-list-item-content>
                <mu-list-item-title>{{ $t('m.Logout') }}</mu-list-item-title>
              </mu-list-item-content>
            </mu-list-item>
          </mu-list>
        </mu-menu>
      </mu-appbar>

      <!-- 抽屉菜单 -->
      <mu-drawer :docked="false" :open.sync="opendrawer" :right="false">
        <mu-list toggle-nested>
          <!-- 创建通用菜单项方法 -->
          <template v-for="menu in menus">
            <mu-list-item
                :key="menu.key"
                :open="openSideMenu === menu.key"
                :ripple="false"
                button
                nested
                @toggle-nested="openSideMenu = $event ? menu.key : ''"
            >
              <mu-list-item-action>
                <mu-icon :value="menu.icon" size="24"></mu-icon>
              </mu-list-item-action>
              <mu-list-item-title>{{ $t(menu.title) }}</mu-list-item-title>
              <mu-list-item-action>
                <mu-icon class="toggle-icon" size="24" value=":el-icon-arrow-down"></mu-icon>
              </mu-list-item-action>
              <!-- 创建子菜单 -->
              <template v-for="item in menu.items" v-if="menu.showSubMenu(item)">
                <mu-list-item
                    slot="nested"
                    :ripple="false"
                    :to="item.route"
                    active-class="mobile-menu-active"
                    button
                    @click="closeDrawer"
                >
                  <mu-list-item-title>{{ $t(item.title) }}</mu-list-item-title>
                </mu-list-item>
              </template>
            </mu-list-item>
          </template>
        </mu-list>
      </mu-drawer>
    </div>

    <div class="content-app">
      <transition mode="out-in" name="fadeInUp">
        <router-view></router-view>
      </transition>
      <div class="footer">
        Powered by
        <a
            :href="websiteConfig.projectUrl"
            style="color:#1E9FFF"
            target="_blank"
        >{{ websiteConfig.projectName }}</a
        >
        <span style="margin-left:10px">
          <el-dropdown placement="top" @command="changeWebLanguage">
            <span class="el-dropdown-link" style="font-size:14px">
              <i aria-hidden="true" class="fa fa-globe">
                {{ this.webLanguage == 'zh-CN' ? '简体中文' : 'English' }}</i
              ><i class="el-icon-arrow-up el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="zh-CN">简体中文</el-dropdown-item>
              <el-dropdown-item command="en-US">English</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
      </div>
    </div>
    <el-dialog :visible.sync="katexVisible" title="Latex Editor" width="350px">
      <KatexEditor></KatexEditor>
    </el-dialog>
  </div>
</template>

<script>
import {mapGetters} from 'vuex';
import api from '@/common/api';
import mMessage from '@/common/message';
import Avatar from 'vue-avatar';

const KatexEditor = () => import('@/components/admin/KatexEditor.vue');
export default {
  name: 'app',
  mounted() {
    this.currentPath = this.$route.path;
    this.getBreadcrumb();
    window.onresize = () => {
      this.page_width();
    };
    this.page_width();
  },
  data() {
    return {
      openusermenu: false,
      openSideMenu: '',
      katexVisible: false,
      opendrawer: false,
      mobileNar: false,
      currentPath: '',
      routeList: [],
      imgUrl: require('@/assets/backstage.png'),
      menus: [
        {
          key: 'dashboard',
          icon: ':fa fa-tachometer',
          title: 'm.Dashboard',
          items: [{route: '/admin/dashboard', title: 'm.Dashboard'}],
          showSubMenu: () => true
        },
        {
          key: 'general',
          icon: ':el-icon-menu',
          title: 'm.General',
          items: [
            {route: '/admin/announcement', title: 'm.Announcement_Admin'},
            {route: '/admin/notice', title: 'm.SysNotice'},
            {route: '/admin/conf', title: 'm.System_Config'},
            {route: '/admin/switch', title: 'm.System_Switch'}
          ],
          showSubMenu: () => this.isSuperAdmin
        },
        {
          key: 'organization',
          icon: ':el-icon-user',
          title: 'm.Organization_Admin',
          items: [
            {route: '/admin/organization', title: 'm.User_Admin'},
            {route: '/admin/group', title: 'm.Group_Admin'},
            {route: '/admin/role', title: 'm.Group_Role'},
            {route: '/admin/batch-import', title: 'm.Batch_Import'}

          ],
          showSubMenu: () => this.isSuperAdmin
        },
        {
          title: 'm.Course_Admin',
          key: 'course',
          icon: 'el-icon-notebook-1',
          submenu: [
            {title: 'm.Course_Plan', route: '/admin/course'},
          ]
        },
        {
          key: 'problem',
          icon: ':fa fa-bars',
          title: 'm.Problem_Admin',
          items: [
            {route: '/admin/problems', title: 'm.Problem_List'},
            {route: '/admin/problem/create', title: 'm.Create_Problem'},
            {route: '/admin/problem/tag', title: 'm.Admin_Tag', condition: this.isSuperAdmin || this.isProblemAdmin},
            {
              route: '/admin/group-problem/apply',
              title: 'm.Admin_Group_Apply_Problem',
              condition: this.isSuperAdmin || this.isProblemAdmin
            },
            {route: '/admin/problem/batch-operation', title: 'm.Export_Import_Problem', condition: this.isSuperAdmin}
          ],
          showSubMenu: item => !item.condition || item.condition
        },
        {
          key: 'training',
          icon: ':el-icon-s-claim',
          title: 'm.Training_Admin',
          items: [
            {route: '/admin/training', title: 'm.Training_List'},
            {route: '/admin/training/create', title: 'm.Create_Training'},
            {route: '/admin/training/category', title: 'm.Admin_Category'}
          ],
          showSubMenu: () => true
        },
        {
          key: 'contest',
          icon: ':fa fa-trophy',
          title: 'm.Contest_Admin',
          items: [
            {route: '/admin/contest', title: 'm.Contest_List'},
            {route: '/admin/contest/create', title: 'm.Create_Contest'}
          ],
          showSubMenu: () => true
        },
        {
          key: 'discussion',
          icon: ':fa fa-comments',
          title: 'm.Discussion',
          items: [{route: '/admin/discussion', title: 'm.Discussion_Admin'}],
          showSubMenu: () => true
        }
      ],
      menusPc: [
        {
          label: 'm.Dashboard',
          path: '/admin/',
          icon: 'fa fa-tachometer fa-size'
        },
        {
          label: 'm.General',
          index: 'general',
          icon: 'el-icon-menu',
          submenu: [
            {label: 'm.Announcement_Admin', path: '/admin/announcement'},
            {label: 'm.SysNotice', path: '/admin/notice'},
            {label: 'm.System_Config', path: '/admin/conf'},
            {label: 'm.System_Switch', path: '/admin/switch'}
          ]
        },
        {
          label: 'm.Organization_Admin',
          index: 'organization',
          icon: 'el-icon-user',
          submenu: [
            {label: 'm.User_Admin', path: '/admin/user'},
            {label: 'm.Group_Admin', path: '/admin/group'},
            {label: 'm.Group_Role', path: '/admin/role'},
            {label: 'm.Batch_Import', path: '/admin/batch-import'}
          ]
        },
        {
          label: 'm.Course_Admin',
          index: 'course',
          icon: 'el-icon-notebook-1',
          submenu: [
            {label: 'm.Course_Plan', path: '/admin/course'},
          ]
        },
        {
          label: 'm.Prize_Admin',
          index: 'prize',
          icon: 'el-icon-present',
          submenu: [
            {label: 'm.Prize_Info', path: '/admin/prize/info'},
            {label: 'm.Prize_Redemption', path: '/admin/prize/redemption'},

          ]
        },
        {
          label: 'm.Problem_Admin',
          index: 'problem',
          icon: 'fa fa-bars fa-size',
          submenu: [
            {label: 'm.Problem_List', path: '/admin/problems'},
            {label: 'm.Create_Problem', path: '/admin/problem/create'},
            {label: 'm.Admin_Tag', path: '/admin/problem/tag'},
            {label: 'm.Admin_Group_Apply_Problem', path: '/admin/group-problem/apply'},
            {label: 'm.Export_Import_Problem', path: '/admin/problem/batch-operation'}
          ]
        },
        {
          label: 'm.Training_Admin',
          index: 'training',
          icon: 'el-icon-s-claim',
          submenu: [
            {label: 'm.Training_List', path: '/admin/training'},
            {label: 'm.Create_Training', path: '/admin/training/create'},
            {label: 'm.Admin_Category', path: '/admin/training/category'}
          ]
        },
        {
          label: 'm.Contest_Admin',
          index: 'contest',
          icon: 'fa fa-trophy fa-size',
          submenu: [
            {label: 'm.Contest_List', path: '/admin/contest'},
            {label: 'm.Create_Contest', path: '/admin/contest/create'}
          ]
        },
        // {
        //   label: 'm.Discussion',
        //   index: 'discussion',
        //   icon: 'fa fa-comments fa-size',
        //   submenu: [
        //     {label: 'm.Discussion_Admin', path: '/admin/discussion'}
        //   ]
        // }
      ]

    };
  },
  components: {
    KatexEditor,
    Avatar,
  },
  methods: {

    openKatexEditor() {
      this.katexVisible = true;
    },
    closeDrawer() {
      this.opendrawer = false;
    },
    handleCommand(command) {
      if (command === 'logout') {
        api.admin_logout().then((res) => {
          this.$router.push({path: '/admin/login'});
          mMessage.success(this.$i18n.t('m.Log_Out_Successfully'));
          this.$store.commit('clearUserInfoAndToken');
        });
      }
    },
    page_width() {
      let screenWidth = window.screen.width;
      if (screenWidth < 992) {
        this.mobileNar = true;
      } else {
        this.mobileNar = false;
      }
    },
    getBreadcrumb() {
      let matched = this.$route.matched.filter((item) => item.meta.title); //获取路由信息，并过滤保留路由标题信息存入数组
      this.routeList = matched;
    },
    changeWebLanguage(language) {
      this.$store.commit('changeWebLanguage', {language: language});
    },
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'isSuperAdmin',
      'isProblemAdmin',
      'isAuthenticated',
      'websiteConfig',
      'webLanguage',
    ]),
    'window.screen.width'(newVal, oldVal) {
      if (newVal < 992) {
        this.mobileNar = true;
      } else {
        this.mobileNar = false;
      }
    },
  },
  watch: {
    $route() {
      this.getBreadcrumb(); //监听路由变化
    },
  },
};
</script>

<style scoped>
.vertical_menu {
  overflow: auto;
  width: 15%;
  height: 100%;
  position: fixed !important;
  z-index: 100;
  top: 0;
  bottom: 0;
  left: 0;
}

.vertical_menu .logo {
  margin: 20px 0;
  text-align: center;
  cursor: pointer;
}

.vertical_menu .logo img {
  background-color: #fff;
  border: 3px solid #fff;
  width: 110px;
  height: 110px;
}

.fa-size {
  text-align: center;
  font-size: 18px;
  vertical-align: middle;
  margin-right: 5px;
  width: 24px;
}

a {
  background-color: transparent;
}

a:active,
a:hover {
  outline-width: 0;
}

img {
  border-style: none;
}

.admin-container {
  overflow: auto;
  font-weight: 400;
  height: 100%;
  -webkit-font-smoothing: antialiased;
  background-color: #eff3f5;
  overflow-y: auto;
}

.breadcrumb-container {
  padding: 17px;
  background-color: #fff;
}

* {
  box-sizing: border-box;
}

#header {
  text-align: right;
  margin-left: 15%;
  padding-right: 30px;
  line-height: 50px;
  height: 50px;
  background: #f9fafc;
}

.footer {
  margin: 15px;
  text-align: center;
  font-size: small;
}

@media screen and (max-width: 992px) {
  .content-app {
    padding: 0 5px;
    margin-top: 20px;
  }
}

@media screen and (min-width: 992px) {
  .content-app {
    margin-top: 10px;
    margin-right: 10px;
    margin-left: calc(20% + 10px);;
  }

  .vertical_menu {
    width: 20%;
  }

  #header {
    margin-left: 20%;
  }
}

@media screen and (min-width: 1150px) {
  .content-app {
    margin-top: 10px;
    margin-right: 10px;
    margin-left: 220px;
  }

  .vertical_menu {
    width: 210px;
  }

  #header {
    margin-left: 210px;
  }
}


@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate(0, 30px);
  }

  to {
    opacity: 1;
    transform: none;
  }
}

.fadeInUp-enter-active {
  animation: fadeInUp 0.8s;
}

.katex-editor {
  margin-right: 5px;
  cursor: pointer;
  vertical-align: middle;
  margin-right: 10px;
}

.drop-avatar {
  vertical-align: middle;
  margin-right: 10px;
}
</style>
