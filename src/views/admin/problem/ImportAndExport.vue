<template>
  <div>
    <el-card>
      <div slot="header">
        <span class="panel-title home-title">{{ $t('m.Export_Problem') }}</span>
        <div class="filter-row">
          <span>
            <el-button
                icon="el-icon-arrow-down"
                size="small"
                type="primary"
                @click="exportProblems"
            >{{ $t('m.Export') }}
            </el-button>
          </span>
          <span>
            <vxe-input
                v-model="keyword"
                :placeholder="$t('m.Enter_keyword')"
                size="medium"
                type="search"
                @keyup.enter.native="filterByKeyword"
                @search-click="filterByKeyword"
            ></vxe-input>
          </span>
        </div>
      </div>
      <vxe-table
          ref="xTable"
          :checkbox-config="{ labelField: '', highlight: true, range: true }"
          :data="problems"
          :loading="loadingProblems"
          auto-resize
          stripe
          @checkbox-change="handleSelectionChange"
          @checkbox-all="handlechangeAll"
      >
        <vxe-table-column type="checkbox" width="60"></vxe-table-column>
        <vxe-table-column field="id" min-width="100" title="ID">
        </vxe-table-column>
        <vxe-table-column :title="$t('m.Title')" field="title" min-width="150">
        </vxe-table-column>
        <vxe-table-column
            :title="$t('m.Author')"
            field="author"
            min-width="150"
        >
        </vxe-table-column>

        <vxe-table-column :title="$t('m.Created_Time')" field="gmtCreate">
          <template v-slot="{ row }">
            {{ row.gmtCreate | localtime }}
          </template>
        </vxe-table-column>
      </vxe-table>

      <div class="panel-options">
        <el-pagination
            :page-size="limit"
            :page-sizes="[10, 50, 100, 500]"
            :total="total"
            class="page"
            layout="prev, pager, next, sizes"
            @current-change="getProblems"
            @size-change="handleSizeChange"
        >
        </el-pagination>
      </div>
    </el-card>

    <el-card style="margin-top:15px">
      <div slot="header">
        <span class="panel-title home-title">{{ $t('m.Import_Problem') }}</span>
      </div>
      <!-- 原生上传 -->
      <div>
        <h1> 原生上传从OJ导入题目</h1>
        <el-upload
            ref="hoj"
            :auto-upload="false"
            :file-list="fileList1"
            :limit="3"
            :on-change="onFile1Change"
            :on-error="uploadFailed"
            :on-success="uploadSucceeded"
            :show-file-list="true"
            :with-credentials="true"
            action="/api/file/import-problem"
            name="file"
        >
          <el-button slot="trigger" :loading="loading.hoj" icon="el-icon-folder-opened" size="small" type="primary">
            {{ $t('m.Choose_File') }}
          </el-button>
          <el-button
              :disabled="!fileList1.length"
              :loading="loading.hoj"
              icon="el-icon-upload"
              size="small"
              style="margin-left: 10px;"
              type="success"
              @click="submitUpload('hoj')"
          >{{ $t('m.Upload') }}
          </el-button
          >
        </el-upload>
      </div>
      <!-- OSS上传-->
      <div>
        <h1> OSS上传从OJ导入题目</h1>
        <oss-upload v-model="fileUrl" :allowed-extensions="['zip']" :max-count="1" accept=".zip"/>
        <el-button :disabled="!fileUrl" :loading="loading.hoj" icon="el-icon-upload" size="small"
                   style="margin-left: 10px;" type="success"
                   @click="submitUpload2">{{ $t('m.Upload') }}
        </el-button>
      </div>

    </el-card>

    <el-card style="margin-top:15px">
      <div slot="header">
        <span class="panel-title home-title">导入客观题（选择、判断、填空）</span>
      </div>
      <div>
        <p>1、仅支持选择题、判断题、填空题，必须通过这里的模版上传题目</p>
        <p>2、一共有两个模版 分别为【题目内容】、【选项内容】</p>
        <p>3、题目数据不允许有空行、图片</p>
        <p>
          4、注意：
          <el-tag type="danger">两个表格的题题目id要<strong> 一 一对应</strong></el-tag>
        </p>
      </div>
      <div class="upload-container">
        <div class="upload-row">
          <!-- 题目表上传 -->
          <div class="upload-section">
            <div class="upload-title">
              <el-link
                  :href="'https://tuco.oss-cn-guangzhou.aliyuncs.com/2025/03/04/xlsx/b9cd9bca124f499c86a1bd4d5c426d2e.xlsx'"
                  download="题目模板.xlsx"
                  type="primary"
              >
                题目数据表(点击下载模版)
              </el-link>
            </div>
            <oss-upload
                v-model="problemFixedBody.problemUrl"
                :allowed-extensions="['xlsx']"
                :max-count="1"
                accept=".xlsx"
                class="upload-component"
                list-type="text"
            />
            <div class="upload-tip">{{ $t('m.Problem_Table_Tip') }}</div>
          </div>
          <!-- 分隔线 -->
          <div class="divider"></div>
          <!-- 题目选项数据表上传 -->
          <div class="upload-section">
            <div class="upload-title">
              <el-link
                  :href="'https://tuco.oss-cn-guangzhou.aliyuncs.com/2025/03/04/xlsx/d37deba3e1fb46ffb0bb1b525f128c99.xlsx'"
                  download="题目选项表模板.xlsx"
                  type="primary">题目选项表(点击下载模版)
              </el-link>
            </div>
            <oss-upload
                v-model="problemFixedBody.problemOptionUrl"
                :allowed-extensions="['xlsx']"
                :max-count="1"
                accept=".xlsx"
                class="upload-component"
                list-type="text"
            />
            <div class="upload-tip">{{ $t('m.Option_Table_Tip') }}</div>
          </div>
        </div>
        <el-button
            :disabled="!problemFixedBody.problemUrl || !problemFixedBody.problemOptionUrl"
            class="submit-btn"
            icon="el-icon-upload"
            size="small"
            type="success"
            @click="importProblemFixed"
        >
          {{ $t('m.Upload') }}
        </el-button>
      </div>

    </el-card>

    <el-card v-if="false" style="margin-top:15px">
      <div slot="header">
        <span class="panel-title home-title">{{
            $t('m.Import_QDUOJ_Problem')
          }}</span>
      </div>
      <el-upload
          ref="qduoj"
          :auto-upload="false"
          :file-list="fileList2"
          :limit="3"
          :on-change="onFile2Change"
          :on-error="uploadFailed"
          :on-success="uploadSucceeded"
          :show-file-list="true"
          :with-credentials="true"
          action="/api/file/import-qdoj-problem"
          name="file"
      >
        <el-button
            slot="trigger"
            :loading="loading.qduoj"
            icon="el-icon-folder-opened"
            size="small"
            type="primary"
        >{{ $t('m.Choose_File') }}
        </el-button
        >
        <el-button
            :disabled="!fileList2.length"
            :loading="loading.qduoj"
            icon="el-icon-upload"
            size="small"
            style="margin-left: 10px;"
            type="success"
            @click="submitUpload('qduoj')"
        >{{ $t('m.Upload') }}
        </el-button
        >
      </el-upload>
    </el-card>

    <el-card v-if="false" style="margin-top:15px">
      <div slot="header">
        <span class="panel-title home-title">{{
            $t('m.Import_FPS_Problem')
          }}</span>
      </div>
      <el-upload
          ref="fps"
          :auto-upload="false"
          :file-list="fileList3"
          :limit="3"
          :on-change="onFile3Change"
          :on-error="uploadFailed"
          :on-success="uploadSucceeded"
          :show-file-list="true"
          :with-credentials="true"
          action="/api/file/import-fps-problem"
          name="file"
      >
        <el-button
            slot="trigger"
            :loading="loading.fps"
            icon="el-icon-folder-opened"
            size="small"
            type="primary"
        >{{ $t('m.Choose_File') }}
        </el-button
        >
        <el-button
            :disabled="!fileList3.length"
            :loading="loading.fps"
            icon="el-icon-upload"
            size="small"
            style="margin-left: 10px;"
            type="success"
            @click="submitUpload('fps')"
        >{{ $t('m.Upload') }}
        </el-button
        >
      </el-upload>
    </el-card>

    <el-card style="margin-top:15px">
      <div slot="header">
        <span class="panel-title home-title">{{
            $t('m.Import_Hydro_Problem')
          }}</span>
      </div>
      <el-upload
          ref="hydro"
          :auto-upload="false"
          :file-list="fileList4"
          :limit="3"
          :on-change="onFile4Change"
          :on-error="uploadFailed"
          :on-success="uploadSucceeded"
          :show-file-list="true"
          :with-credentials="true"
          action="/api/file/import-hydro-problem"
          name="file"
      >
        <el-button
            slot="trigger"
            :loading="loading.hydro"
            icon="el-icon-folder-opened"
            size="small"
            type="primary"
        >{{ $t('m.Choose_File') }}
        </el-button
        >
        <el-button
            :disabled="!fileList4.length"
            :loading="loading.hydro"
            icon="el-icon-upload"
            size="small"
            style="margin-left: 10px;"
            type="success"
            @click="submitUpload('hydro')"
        >{{ $t('m.Upload') }}
        </el-button
        >
      </el-upload>
    </el-card>
  </div>
</template>
<script>
import api from '@/common/api';
import utils from '@/common/utils';
import myMessage from '@/common/message';
import OssUpload from '@/components/tuc/OssUpload.vue';

export default {
  name: 'import_and_export',
  components: {OssUpload},
  data() {
    return {
      problemFixedBody: {
        problemOptionUrl: '',
        problemUrl: '',
      },
      fileUrl: null,
      fileList1: [],
      fileList2: [],
      fileList3: [],
      fileList4: [],
      page: 1,
      limit: 10,
      total: 0,
      loadingProblems: false,
      loadingImporting: false,
      keyword: '',
      problems: [],
      selected_problems: [],
      loading: {
        hoj: false,
        qduoj: false,
        fps: false,
        hydro: false,
      },
    };
  },
  mounted() {
    this.getProblems();
  },
  methods: {
    // 题目表部分勾选 改变选中的内容
    handleSelectionChange({records}) {
      this.selected_problems = records;
    },

    // 一键全部选中，改变选中的内容列表
    handlechangeAll() {
      this.selected_problems = this.$refs.xTable.getCheckboxRecords();
    },

    async importProblemFixed() {
      const res = await api.importProblemFixed(this.problemFixedBody);
      if (res.data.status === 200) {
        myMessage.success(this.$i18n.t('m.Success'));
      } else {
        myMessage.error('题目导入失败');
      }
      this.problemFixedBody = null;
    },

    handleSizeChange(pageSize) {
      this.limit = pageSize;
      this.getProblems();
    },

    getProblems(page = 1) {
      let params = {
        keyword: this.keyword,
        currentPage: page,
        limit: this.limit,
        oj: 'Mine',
      };
      this.loadingProblems = true;
      api.admin_getProblemList(params).then((res) => {
        this.problems = res.data.data.records;
        this.total = res.data.data.total;
        this.loadingProblems = false;
      });
    },
    exportProblems() {
      let params = [];
      if (this.selected_problems.length <= 0) {
        myMessage.error(this.$i18n.t('m.Export_Problem_NULL_Tips'));
        return;
      }
      for (let p of this.selected_problems) {
        params.push('pid=' + p.id);
      }
      let url = '/api/file/export-problem?' + params.join('&');
      utils.downloadFile(url);
    },
    submitUpload(ref) {
      this.loading[ref] = true;
      this.$refs[ref].submit();
    },
    submitUpload2() {
      this.loading['hoj'] = true;
      // 加载10s

      myMessage.loading("上传中")
      console.log("fileUrl", this.fileUrl)
      // 延迟10s 上传
      api.importProblemOss({url: this.fileUrl})
          .then((res) => {
          })
          .catch((err) => {
            myMessage.error(this.$i18n.t('m.Upload_Problem_Failed'));
          })
          .finally(() => {
            this.fileUrl = null;
            this.loading['hoj'] = false;
          })
      myMessage.success(this.$i18n.t('m.Upload_Problem_Succeeded'));
      this.getProblems();
    },
    onFile1Change(file, fileList) {
      this.fileList1 = fileList.slice(-1);
    },
    onFile2Change(file, fileList) {
      this.fileList2 = fileList.slice(-1);
    },
    onFile3Change(file, fileList) {
      this.fileList3 = fileList.slice(-1);
    },
    onFile4Change(file, fileList) {
      this.fileList4 = fileList.slice(-1);
    },
    uploadSucceeded(response, file, fileList) {
      this.loading.hoj = false;
      this.loading.qduoj = false;
      this.loading.fps = false;
      this.loading.hydro = false;
      if (response.status != 200) {
        myMessage.error(response.msg);
        this.$notify.error({
          title: this.$i18n.t('m.Error'),
          message: response.msg,
          dangerouslyUseHTMLString: true,
          duration: 8000,
        });
      } else {
        myMessage.success(this.$i18n.t('m.Upload_Problem_Succeeded'));
        this.getProblems();
      }
    },
    uploadFailed() {
      this.loading.hoj = false;
      this.loading.qduoj = false;
      this.loading.fps = false;
      this.loading.hydro = false;
      myMessage.error(this.$i18n.t('m.Upload_Problem_Failed'));
    },
    filterByKeyword() {
      this.getProblems();
    },
  },
};
</script>

<style scoped>
.upload-container {
  max-width: 800px;
  margin: 20px auto;
}

.upload-row {
  display: flex;
  gap: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.upload-section {
  flex: 1;
  min-width: 300px;
}

.upload-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
  font-size: 24px;
}

.upload-tip {
  font-size: 12px;
  color: #95a5a6;
  margin-top: 8px;
  line-height: 1.4;
}

.divider {
  width: 1px;
  background: #e0e0e0;
  margin: 0 10px;
}

.submit-btn {
  display: block;
  margin: 0 auto;
  padding: 12px 32px;
  transition: all 0.3s ease;
}

.submit-btn:not([disabled]) {
  background: #67c23a;
  border-color: #67c23a;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

@media (max-width: 768px) {
  .upload-row {
    flex-direction: column;
  }

  .divider {
    height: 1px;
    width: 100%;
    margin: 10px 0;
  }
}

.filter-row {
  margin-top: 10px;
}

@media screen and (max-width: 768px) {
  .filter-row span {
    margin-right: 5px;
  }
}

@media screen and (min-width: 768px) {
  .filter-row span {
    margin-right: 20px;
  }
}
</style>
