<template>
  <div class="problem">
    <el-tabs v-model="activateProblemType" @tab-click="handleClickTabs">
      <el-tab-pane v-for="type in Object.values(ProblemType)" :key="type.code" :label="type.name"
                   :name="type"></el-tab-pane>
    </el-tabs>

    <choice-problem
        v-if="isChoiceType"
        :all-tags-tmp="allTagsTmp"

        :contest-id="contestID"
        :contest-problem="contestProblem"
        :input-visible="inputVisible"
        :options.sync="options"
        :problem="problem"
        :problem-tags="problemTags"
        :problem-type="activateProblemType"
        :rules="rules"
        :tag-input="tagInput"
        :title="title"
        @submit="submit"
        @close-tag="closeTag"
        @add-tag="addTag"/>

    <fill-in-blank-problem
        v-if="activateProblemType === ProblemType.FillInBlank"
        :all-tags-tmp="allTagsTmp"

        :contest-id="contestID"
        :contest-problem="contestProblem"
        :input-visible="inputVisible"
        :options.sync="options"
        :problem="problem"
        :problem-tags="problemTags"
        :problem-type="activateProblemType"
        :rules="rules"
        :tag-input="tagInput"
        :title="title"
        @submit="submit"
        @close-tag="closeTag"
        @add-tag="addTag"/>

    <el-card v-if="activateProblemType === ProblemType.Programmer">
      <problem-base
          :all-tags-tmp="allTagsTmp"
          :contest-id="contestID"
          :contest-problem="contestProblem"
          :input-visible.sync="inputVisible"
          :problem="problem"
          :problem-tags.sync="problemTags"
          :rules="rules"
          :tag-input.sync="tagInput"
          :title="title"
      />
      <el-form ref="form" :model="problem" :rules="rules" label-position="top" label-width="70px">
        <!--比赛题目信息-->
        <el-row v-if="contestID" :gutter="20">
          <el-col :md="12" :xs="24">
            <el-form-item :label="$t('m.Contest_Display_Title')" required>
              <el-input v-model="contestProblem.displayTitle" :placeholder="$t('m.Contest_Display_Title')"></el-input>
            </el-form-item>
          </el-col>

          <el-col :md="12" :xs="24">
            <el-form-item :label="$t('m.Contest_Display_ID')" required>
              <el-input v-model="contestProblem.displayId" :placeholder="$t('m.Contest_Display_ID')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!--基本信息填写框-->
        <el-row :gutter="20">
          <el-col :md="6" :xs="24">
            <el-form-item :label="'流程图'" required>
              <el-switch v-model="problem.requireImage" :active-value="true" :inactive-value="false"
                         active-text="上传图片" inactive-text="不上传"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :md="6" :xs="24">
            <el-form-item :label="$t('m.Time_Limit') + '(ms)'" required>
              <el-input v-model="problem.timeLimit" :disabled="problem.isRemote" :placeholder="$t('m.Time_Limit')"
                        type="Number"></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="6" :xs="24">
            <el-form-item :label="$t('m.Memory_Limit') + '(mb)'" required>
              <el-input v-model="problem.memoryLimit" :disabled="problem.isRemote" :placeholder="$t('m.Memory_Limit')"
                        type="Number"></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="6" :xs="24">
            <el-form-item :label="$t('m.Stack_Limit') + '(mb)'" required>
              <el-input v-model="problem.stackLimit" :disabled="problem.isRemote" :placeholder="$t('m.Stack_Limit')"
                        type="Number"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!--输入输出 and 提示-->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('m.Input')" prop="input_description">
              <Editor :value.sync="problem.input"></Editor>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('m.Output')" prop="output_description">
              <Editor :value.sync="problem.output"></Editor>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('m.Hint')" style="margin-top: 20px">
              <Editor :value.sync="problem.hint"></Editor>
            </el-form-item>
          </el-col>
        </el-row>

        <!--题目类型-->
        <el-row :gutter="20">
          <el-col :md="4" :xs="24">
            <el-form-item :label="$t('m.Type')">
              <el-radio-group v-model="problem.type" :disabled="disableRuleType || problem.isRemote"
                              @change="problemTypeChange">
                <el-radio :label="0">ACM</el-radio>
                <el-radio :label="1">OI</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :md="4" :xs="24">
            <el-form-item :label="$t('m.Code_Shareable')">
              <el-switch v-model="problem.codeShare" active-text="" inactive-text="">
              </el-switch>
            </el-form-item>
          </el-col>

        </el-row>

        <!--代码模版-->
        <el-row>
          <el-col :md="24" :xs="24">
            <el-form-item :error="error.languages" :label="$t('m.Languages')" required>
              <el-checkbox-group v-model="problemLanguages">
                <el-tooltip v-for="lang in allLanguage" :key="lang.name" :content="lang.description" class="spj-radio"
                            effect="dark" placement="top-start">
                  <el-checkbox :label="lang.name"></el-checkbox>
                </el-tooltip>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>

        <div>
          <div class="panel-title home-title">
            {{ $t('m.Problem_Examples') }}
            <el-popover placement="right" trigger="hover">
              <p>
                {{ $t('m.Problem_Examples_Desc') }}
              </p>
              <i slot="reference" class="el-icon-question"></i>
            </el-popover>
          </div>
          <el-form-item v-for="(example, index) in problem.examples" :key="'example' + index">
            <Accordion :index="index" :isOpen="example.isOpen ? true : false"
                       :title="$t('m.Problem_Example') + (index + 1)" @changeVisible="changeExampleVisible">
              <el-button slot="header" icon="el-icon-delete" size="small" type="danger" @click="deleteExample(index)">
                {{ $t('m.Delete') }}
              </el-button>
              <el-row :gutter="20">
                <el-col :md="12" :xs="24">
                  <el-form-item :label="$t('m.Example_Input')">
                    <el-input v-model="example.input" :placeholder="$t('m.Example_Input')" :rows="5"
                              style="white-space: pre-line" type="textarea">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :md="12" :xs="24">
                  <el-form-item :label="$t('m.Example_Output')">
                    <el-input v-model="example.output" :placeholder="$t('m.Example_Output')" :rows="5" type="textarea">
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </Accordion>
          </el-form-item>
        </div>

        <div class="add-example-btn">
          <el-button class="add-examples" icon="el-icon-plus" type="small" @click="addExample()">{{
              $t('m.Add_Example')
            }}
          </el-button>
        </div>

        <template v-if="!problem.isRemote">
          <div class="panel-title home-title">
            {{ $t('m.Judge_Extra_File') }}
            <el-popover placement="right" trigger="hover">
              <p>{{ $t('m.Judge_Extra_File_Tips1') }}</p>
              <p>{{ $t('m.Judge_Extra_File_Tips2') }}</p>
              <i slot="reference" class="el-icon-question"></i>
            </el-popover>
          </div>

          <el-row :gutter="20">
            <el-col :md="12" :xs="24">
              <el-form-item>
                <el-checkbox v-model="addUserExtraFile">{{
                    $t('m.User_Program')
                  }}
                </el-checkbox>
              </el-form-item>
              <el-form-item v-if="addUserExtraFile">
                <AddExtraFile :files.sync="userExtraFile" type="user" @deleteFile="deleteFile"
                              @upsertFile="upsertFile"></AddExtraFile>
              </el-form-item>
            </el-col>
            <el-col :md="12" :xs="24">
              <el-form-item>
                <el-checkbox v-model="addJudgeExtraFile">{{
                    $t('m.SPJ_Or_Interactive_Program')
                  }}
                </el-checkbox>
              </el-form-item>
              <el-form-item v-if="addJudgeExtraFile">
                <AddExtraFile :files.sync="judgeExtraFile" type="judge" @deleteFile="deleteFile"
                              @upsertFile="upsertFile"></AddExtraFile>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <template v-if="!problem.isRemote">
          <div class="panel-title home-title">
            {{ $t('m.Read_Write_Mode') }}
          </div>
          <el-row :gutter="20">
            <el-col :md="8" :xs="24">
              <el-form-item required>
                <el-radio-group v-model="problem.isFileIO">
                  <el-radio :label="false">
                    {{ $t('m.Standard_IO') }}
                  </el-radio>
                  <el-radio :label="true">
                    {{ $t('m.File_IO') }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :md="8" :xs="24">
              <el-form-item v-if="problem.isFileIO">
                <el-input v-model="problem.ioReadFileName" size="small">
                  <template slot="prepend">{{ $t('m.Input_File_Name') }}</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :md="8" :xs="24">
              <el-form-item v-if="problem.isFileIO">
                <el-input v-model="problem.ioWriteFileName" size="small">
                  <template slot="prepend">{{ $t('m.Output_File_Name') }}</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <template v-if="!problem.isRemote">
          <div class="panel-title home-title">
            {{ $t('m.Judge_Mode') }}
            <el-popover placement="right" trigger="hover">
              <p>1. {{ $t('m.General_Judge_Mode_Tips') }}</p>
              <p>2. {{ $t('m.Special_Judge_Mode_Tips') }}</p>
              <p>3. {{ $t('m.Interactive_Judge_Mode_Tips') }}</p>
              <i slot="reference" class="el-icon-question"></i>
            </el-popover>
          </div>
          <el-form-item :error="error.spj" label="">
            <el-col :span="24">
              <el-radio-group v-model="problem.judgeMode" @change="switchMode">
                <el-radio label="default">{{ $t('m.General_Judge') }}</el-radio>
                <el-radio label="spj">{{ $t('m.Special_Judge') }}</el-radio>
                <el-radio label="interactive">{{
                    $t('m.Interactive_Judge')
                  }}
                </el-radio>
              </el-radio-group>
            </el-col>
          </el-form-item>
          <el-form-item v-if="problem.judgeMode != 'default'">
            <Accordion :title="
                problem.judgeMode == 'spj'
                  ? $t('m.Special_Judge_Code')
                  : $t('m.Interactive_Judge_Code')
              ">
              <template slot="header">
                <span style="margin-right:5px;">{{
                    problem.judgeMode == 'spj'
                        ? $t('m.SPJ_Language')
                        : $t('m.Interactive_Language')
                  }}：</span>
                <el-radio-group v-model="problem.spjLanguage">
                  <el-tooltip v-for="lang in allSpjLanguage" :key="lang.name" :content="lang.description"
                              class="spj-radio" effect="dark" placement="top-start">
                    <el-radio :label="lang.name">{{ lang.name }}</el-radio>
                  </el-tooltip>
                </el-radio-group>
                <el-button :loading="loadingCompile" icon="el-icon-folder-checked" size="small" style="margin-left:10px"
                           type="primary" @click="compileSPJ">{{ $t('m.Compile') }}
                </el-button>
              </template>
              <code-mirror v-model="problem.spjCode" :mode="spjMode"></code-mirror>
            </Accordion>
          </el-form-item>
        </template>

        <div class="panel-title home-title">{{ $t('m.Code_Template') }}</div>
        <el-form-item>
          <el-row>
            <el-col v-for="(v, k) in codeTemplate" :key="'template' + k" :span="24">
              <el-form-item>
                <el-checkbox v-model="v.status">{{ k }}</el-checkbox>
                <div v-if="v.status">
                  <code-mirror v-model="v.code" :mode="v.mode"></code-mirror>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>

        <el-row v-if="!problem.isRemote" :gutter="20">
          <div class="panel-title home-title">
            {{ $t('m.Judge_Samples') }}
            <el-popover placement="right" trigger="hover">
              <p>{{ $t('m.Sample_Tips') }}</p>
              <i slot="reference" class="el-icon-question"></i>
            </el-popover>
          </div>

          <el-form-item required>
            <el-radio-group v-model="problem.judgeCaseMode" @change="switchJudgeCaseMode">
              <el-radio :label="JUDGE_CASE_MODE.DEFAULT">
                {{ problem.type == 1 ? $t('m.OI_Judge_Case_Default_Mode') : $t('m.ACM_Judge_Case_Default_Mode') }}
              </el-radio>
              <template v-if="problem.type == 1">
                <el-radio :label="JUDGE_CASE_MODE.SUBTASK_LOWEST">{{
                    $t('m.Judge_Case_Subtask_Lowest_Mode')
                  }}
                </el-radio>
                <el-radio :label="JUDGE_CASE_MODE.SUBTASK_AVERAGE">{{
                    $t('m.Judge_Case_Subtask_Average_Mode')
                  }}
                </el-radio>
              </template>
              <template v-else>
                <el-radio :label="JUDGE_CASE_MODE.ERGODIC_WITHOUT_ERROR">
                  {{ $t('m.Judge_Case_Ergodic_Without_Error_Mode') }}
                </el-radio>
              </template>
            </el-radio-group>
          </el-form-item>

          <el-form-item required>
            <el-switch v-model="problem.isUploadCase" :active-text="$t('m.Use_Upload_File')"
                       :inactive-text="$t('m.Use_Manual_Input')" style="margin: 10px 0">
            </el-switch>
          </el-form-item>

          <div v-show="problem.isUploadCase">
            <el-col :span="24">
              <el-form-item :error="error.testcase">
                <el-upload :action="uploadFileUrl+'?mode='+problem.judgeCaseMode" :on-error="uploadFailed"
                           :on-success="uploadSucceeded" :show-file-list="true" name="file">
                  <el-button icon="el-icon-upload" size="small" type="primary">{{ $t('m.Choose_File') }}
                  </el-button>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <vxe-table ref="xTable" :data="problem.testCaseScore" :sort-config="{trigger: 'cell',
                defaultSort: {field: 'groupNum', order: 'asc'},
                orders: ['desc', 'asc', null],
                sortMethod: customSortMethod}" align="center" auto-resize stripe>
                <vxe-table-column field="index" title="#" width="60"></vxe-table-column>
                <vxe-table-column :title="$t('m.Sample_Input_File')" field="input" min-width="100">
                </vxe-table-column>
                <vxe-table-column :title="$t('m.Sample_Output_File')" field="output" min-width="100">
                </vxe-table-column>
                <vxe-table-column v-if="problem.judgeCaseMode == JUDGE_CASE_MODE.SUBTASK_LOWEST
                    || problem.judgeCaseMode == JUDGE_CASE_MODE.SUBTASK_AVERAGE" :title="$t('m.Sample_Group_Num')"
                                  field="groupNum" min-width="100" sortable>
                  <template v-slot="{ row }">
                    <el-input v-model="row.groupNum" size="small" type="number" @change="sortTestCaseList"></el-input>
                  </template>
                </vxe-table-column>
                <vxe-table-column v-if="problem.type == 1" :title="$t('m.Score')" field="score" min-width="100">
                  <template v-slot="{ row }">
                    <el-input v-model="row.score" :disabled="problem.type != 1" :placeholder="$t('m.Score')"
                              size="small" type="number">
                    </el-input>
                  </template>
                </vxe-table-column>
              </vxe-table>
            </el-col>
          </div>

          <div v-show="!problem.isUploadCase">
            <el-form-item v-for="(sample, index) in problemSamples" :key="'sample' + index">
              <Accordion :index="index" :isOpen="sample.isOpen ? true : false"
                         :title="$t('m.Problem_Sample') + (sample.index)" @changeVisible="changeSampleVisible">
                <el-button slot="header" icon="el-icon-delete" size="small" type="danger" @click="deleteSample(index)">
                  {{ $t('m.Delete') }}
                </el-button>
                <el-row :gutter="20">
                  <el-col :md="12" :xs="24">
                    <el-form-item :label="$t('m.Sample_Input')" required>
                      <el-input v-model="sample.input" :placeholder="$t('m.Sample_Input')" :rows="5" type="textarea">
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :md="12" :xs="24">
                    <el-form-item :label="$t('m.Sample_Output')" required>
                      <el-input v-model="sample.output" :placeholder="$t('m.Sample_Output')" :rows="5" type="textarea">
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col v-if="problem.type == 1" :span="24">
                    <el-form-item :label="$t('m.Score')">
                      <el-input v-model="sample.score" :placeholder="$t('m.Score')" size="small" type="number">
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col v-show="problem.judgeCaseMode == JUDGE_CASE_MODE.SUBTASK_LOWEST
                       || problem.judgeCaseMode == JUDGE_CASE_MODE.SUBTASK_AVERAGE" :span="24">
                    <el-form-item :label="$t('m.Sample_Group_Num')">
                      <el-input v-model="sample.groupNum" :placeholder="$t('m.Sample_Group_Num')" size="small"
                                type="number" @change="sortManualProblemSampleList">
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </Accordion>
            </el-form-item>

            <div class="add-sample-btn">
              <el-button class="add-samples" icon="el-icon-plus" type="small" @click="addSample()">{{
                  $t('m.Add_Sample')
                }}
              </el-button>
            </div>
          </div>
        </el-row>

        <el-form-item :label="$t('m.Source')">
          <el-input v-model="problem.source" :placeholder="$t('m.Source')"></el-input>
        </el-form-item>

        <el-form-item v-if="!problem.isRemote" :label="$t('m.Auto_Remove_the_Blank_at_the_End_of_Code')">
          <el-switch v-model="problem.isRemoveEndBlank" active-text="" inactive-text="">
          </el-switch>
        </el-form-item>

        <el-form-item :label="$t('m.Publish_the_Judging_Result_of_Test_Data')">
          <el-switch v-model="problem.openCaseResult" active-text="" inactive-text="">
          </el-switch>
        </el-form-item>

        <el-button size="medium" type="primary" @click.native="submit()">{{
            $t('m.Save')
          }}
        </el-button>
      </el-form>
    </el-card>


  </div>
</template>

<script>
import api from '@/common/api';
import {JUDGE_CASE_MODE, PROBLEM_LEVEL} from '@/common/constants';
import myMessage from '@/common/message';
import utils from '@/common/utils';
import ChoiceProblem from '@/components/tuc/problem/ChoiceProblem.vue';
import FillInBlankProblem from '@/components/tuc/problem/FillInBlankProblem.vue';
import ProblemBase from '@/components/tuc/problem/ProblemBase.vue';
import {ProblemType} from '@/components/tuc/problem/problemEnum';
import ProgrammerProblem from '@/components/tuc/problem/ProgrammerProblem.vue';
import {mapGetters} from 'vuex';

const Editor = () => import('@/components/admin/Editor.vue');
const Accordion = () => import('@/components/admin/Accordion.vue');
const AddExtraFile = () => import('@/components/admin/AddExtraFile.vue');
const CodeMirror = () => import('@/components/admin/CodeMirror.vue');
export default {
  name: 'Problem',
  components: {
    FillInBlankProblem,
    ProblemBase,
    ProgrammerProblem,
    ChoiceProblem,
    Accordion,
    AddExtraFile,
    CodeMirror,
    Editor,
  },
  data() {
    return {
      rules: {
        title: {
          required: true,
          message: 'Title is required',
          trigger: 'blur',
        },
        input_description: {
          required: true,
          message: 'Input Description is required',
          trigger: 'blur',
        },
        output_description: {
          required: true,
          message: 'Output Description is required',
          trigger: 'blur',
        },
      },
      backPath: '',
      loadingCompile: false,
      mode: '', // 该题目是编辑或者创建
      contest: {},
      codeTemplate: {},
      pid: null, // 题目id，如果为创建模式则为null
      contestID: null, // 比赛id
      contestProblem: {
        displayId: null,
        displayTitle: null,
        cid: null,
        pid: null,
      }, // 比赛题目的相关属性
      activateProblemType: ProblemType.Programmer,
      problem: {
        id: null,
        title: '',
        problemId: '',
        description: '',
        input: '',
        output: '',
        timeLimit: 1000,
        memoryLimit: 256,
        stackLimit: 128,
        difficulty: 0,
        problemType: null,
        auth: 1,
        codeShare: true,
        requireImage: '',
        examples: [], // 题面上的样例输入输出
        spjLanguage: '',
        spjCode: '',
        spjCompileOk: false,
        uploadTestcaseDir: '',
        testCaseScore: [],
        isRemote: false,
        isUploadCase: true,
        type: 0,
        hint: '',
        source: '',
        cid: null,
        isRemoveEndBlank: true,
        openCaseResult: true,
        judgeMode: 'default',
        judgeCaseMode: 'default',
        userExtraFile: '',
        judgeExtraFile: '',
        isFileIO: false,
        ioReadFileName: null,
        ioWriteFileName: null,
        options: [], // 题目选项（非编程题）

      },
      options: [], // 题目选项（非编程题）
      problemTags: [], //指定问题的标签列表
      problemLanguages: [], //指定问题的编程语言列表
      problemSamples: [], // 判题机使用的样例
      problemCodeTemplate: [],
      reProblem: {},
      testCaseUploaded: false,
      allLanguage: [], //所有编程语言
      allSpjLanguage: [], // 所有可以用特殊判题的语言
      allTags: [],
      allTagsTmp: [],
      inputVisible: false,
      tagInput: '',
      title: '',
      spjMode: '',
      disableRuleType: false,
      routeName: '',
      uploadTestcaseDir: '',
      uploadFileUrl: '',
      error: {
        tags: '',
        spj: '',
        languages: '',
        testCase: '',
      },
      PROBLEM_LEVEL: {},
      JUDGE_CASE_MODE: {},
      spjRecord: {
        spjCode: '',
        spjLanguage: '',
      },
      addUserExtraFile: false,
      addJudgeExtraFile: false,
      userExtraFile: null,
      judgeExtraFile: null,
      judgeCaseModeRecord: 'default',
      sampleIndex: 1,
    };
  },
  mounted() {
    this.PROBLEM_LEVEL = Object.assign({}, PROBLEM_LEVEL);
    this.JUDGE_CASE_MODE = Object.assign({}, JUDGE_CASE_MODE);
    this.routeName = this.$route.name;
    let contestID = this.$route.params.contestId;
    this.uploadFileUrl = '/api/file/upload-testcase-zip';
    if (
        this.routeName === 'admin-edit-problem' ||
        this.routeName === 'admin-edit-contest-problem'
    ) {
      this.mode = 'edit';
    } else {
      this.mode = 'add';
    }
    api
        .admin_getAllProblemTagList('ALL')
        .then((res) => {
          this.allTags = res.data.data;
          for (let tag of res.data.data) {
            this.allTagsTmp.push({value: tag.name, oj: tag.oj});
          }
        })
        .catch(() => {
        });
    api.getLanguages(this.$route.params.problemId, false).then((res) => {
      let allLanguage = res.data.data;
      this.allLanguage = allLanguage;
      for (let i = 0; i < allLanguage.length; i++) {
        if (allLanguage[i].isSpj == true) {
          this.allSpjLanguage.push(allLanguage[i]);
        }
      }
      this.problem = this.reProblem = {
        id: null,
        problemId: '',
        title: '',
        description: '',
        input: '',
        output: '',
        timeLimit: 1000,
        memoryLimit: 256,
        stackLimit: 128,
        difficulty: 0,
        auth: 1,
        codeShare: true,
        examples: [],
        spjLanguage: '',
        spjCode: '',
        spjCompileOk: false,
        isUploadCase: true,
        uploadTestcaseDir: '',
        testCaseScore: [],
        contestProblem: {},
        type: 0,
        hint: '',
        source: '',
        cid: null,
        isRemoveEndBlank: true,
        openCaseResult: true,
        judgeMode: 'default',
        judgeCaseMode: 'default',
        userExtraFile: null,
        judgeExtraFile: null,
        isFileIO: false,
        ioReadFileName: null,
        ioWriteFileName: null,
      };

      this.contestID = contestID;
      if (contestID) {
        this.problem.cid = this.reProblem.cid = contestID;
        this.problem.auth = this.reProblem.auth = 3;
        this.disableRuleType = true;
        api.admin_getContest(contestID).then((res) => {
          this.problem.type = this.reProblem.type = res.data.data.type;
          this.contest = res.data.data;
        });
      }
      this.problem.spjLanguage = 'C';
      this.init();
    });
  },
  watch: {
    $route() {
      this.routeName = this.$route.name;
      if (
          this.routeName === 'admin-edit-problem' ||
          this.routeName === 'admin-edit-contest-problem'
      ) {
        this.mode = 'edit';
      } else {
        this.mode = 'add';
      }
      this.$refs.form.resetFields();
      this.problem = this.reProblem;
      this.problemTags = []; //指定问题的标签列表
      this.problemLanguages = []; //指定问题的编程语言列表
      this.problemSamples = [];
      this.problemCodeTemplate = [];
      this.codeTemplate = [];
      this.init();
    },

    problemLanguages(newVal) {
      let data = {};
      // use deep copy to avoid infinite loop
      let languages = JSON.parse(JSON.stringify(newVal)).sort();
      for (let item of languages) {
        if (this.codeTemplate[item] === undefined) {
          let langConfig = this.allLanguage.find((lang) => {
            return lang.name === item;
          });
          let codeTemp;
          let problemCodeTemplate = this.problemCodeTemplate;
          if (problemCodeTemplate) {
            codeTemp = problemCodeTemplate.find((temp) => {
              return temp.lid == langConfig.id;
            });
          }
          if (codeTemp == undefined) {
            data[item] = {
              id: null,
              status: false,
              code: langConfig.codeTemplate,
              mode: langConfig.contentType,
            };
          } else {
            data[item] = {
              id: codeTemp.id,
              status: true,
              code: codeTemp.code,
              mode: langConfig.contentType,
            };
          }
        } else {
          data[item] = this.codeTemplate[item];
        }
      }
      this.codeTemplate = data;
    },

    'problem.spjLanguage'(newVal) {
      if (this.allSpjLanguage.length && this.problem.judgeMode != 'default') {
        this.spjMode = this.allSpjLanguage.find((item) => {
          return item.name == this.problem.spjLanguage && item.isSpj == true;
        })['contentType'];
      }
    },
  },
  methods: {
    init() {
      this.sampleIndex = 1;
      if (this.mode === 'edit') {
        this.pid = this.$route.params.problemId;
        this.backPath = this.$route.query.back;
        this.title = this.$i18n.t('m.Edit_Problem');
        let funcName = {
          'admin-edit-problem': 'admin_getProblem',
          'admin-edit-contest-problem': 'admin_getContestProblem',
        }[this.routeName];
        api[funcName](this.pid).then((problemRes) => {
          let data = problemRes.data.data;
          data.spjCompileOk = false;
          data.uploadTestcaseDir = '';
          data.testCaseScore = [];
          if (!data.spjCode) {
            data.spjCode = '';
          }
          data.spjLanguage = data.spjLanguage || 'C';
          this.spjRecord.spjLanguage = data.spjLanguage;
          this.spjRecord.spjCode = data.spjCode;
          this.judgeCaseModeRecord = data.judgeCaseModeRecord;
          this.problem = data;
          this.activateProblemType = ProblemType[this.problem.problemType];
          this.options = this.problem.options;

          this.problem['examples'] = utils.stringToExamples(data.examples);
          if (this.problem['examples'].length > 0) {
            this.problem['examples'][0]['isOpen'] = true;
          }
          this.testCaseUploaded = true;
          if (this.problem.userExtraFile) {
            this.addUserExtraFile = true;
            this.userExtraFile = JSON.parse(this.problem.userExtraFile);
          }
          if (this.problem.judgeExtraFile) {
            this.addJudgeExtraFile = true;
            this.judgeExtraFile = JSON.parse(this.problem.judgeExtraFile);
          }
          api
              .admin_getProblemCases(this.pid, this.problem.isUploadCase)
              .then((res) => {
                if (this.problem.isUploadCase) {
                  this.problem.testCaseScore = res.data.data;
                  this.problem.testCaseScore.forEach((item, index) => {
                    item.index = index + 1;
                  });
                  if (this.$refs.xTable != undefined) {
                    this.$refs.xTable.sort('groupNum', 'asc');
                  }
                } else {
                  this.problemSamples = res.data.data;
                  if (
                      this.problemSamples != null &&
                      this.problemSamples.length > 0
                  ) {
                    this.problemSamples[0]['isOpen'] = true;
                    this.problemSamples.forEach((item, index) => {
                      item.index = index + 1;
                    });
                    this.sampleIndex = this.problemSamples.length + 1;
                  }
                }
              });
        });
        if (funcName === 'admin_getContestProblem') {
          api
              .admin_getContestProblemInfo(this.pid, this.contestID)
              .then((res) => {
                this.contestProblem = res.data.data;
              });
        }
        this.getProblemCodeTemplateAndLanguage();

        api.admin_getProblemTags(this.pid).then((res) => {
          this.problemTags = res.data.data;
        });
      } else {
        this.addExample();
        this.testCaseUploaded = false;
        this.title = this.$i18n.t('m.Create_Problem');
        for (let item of this.allLanguage) {
          this.problemLanguages.push(item.name);
        }
      }
    },

    async getProblemCodeTemplateAndLanguage() {
      const that = this;
      await api.getProblemCodeTemplate(that.pid).then((res) => {
        that.problemCodeTemplate = res.data.data;
      });
      api.getProblemLanguages(that.pid).then((res) => {
        let Languages = res.data.data;
        for (let i = 0; i < Languages.length; i++) {
          that.problemLanguages.push(Languages[i].name);
        }
      });
    },

    switchMode(mode) {
      let modeName = 'General_Judge';
      let modeTips = 'General_Judge_Mode_Tips';
      if (mode == 'spj') {
        modeName = 'Special_Judge';
        modeTips = 'Special_Judge_Mode_Tips';
      } else if (mode == 'interactive') {
        modeName = 'Interactive_Judge';
        modeTips = 'Interactive_Judge_Mode_Tips';
      }
      const h = this.$createElement;
      this.$msgbox({
        title: this.$i18n.t('m.' + modeName),
        message: h('div', null, [
          h(
              'p',
              {style: 'text-align: center;font-weight:bolder;color:red'},
              this.$i18n.t('m.Change_Judge_Mode'),
          ),
          h('br', null, null),
          h(
              'p',
              {style: 'font-weight:bolder'},
              this.$i18n.t('m.' + modeTips),
          ),
        ]),
      });
    },
    querySearch(queryString, cb) {
      var ojName = 'ME';
      if (this.problem.isRemote) {
        ojName = this.problem.problemId.split('-')[0];
      }
      var restaurants = this.allTagsTmp.filter((item) => item.oj == ojName);
      var results = queryString
          ? restaurants.filter(
              (item) =>
                  item.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0,
          )
          : restaurants;
      cb(results);
    },
    changeContent(newVal) {
      this.announcement.content = newVal;
    },
    getLevelName(difficulty) {
      return utils.getLevelName(difficulty);
    },

    selectTag(item) {
      for (var i = 0; i < this.problemTags.length; i++) {
        if (this.problemTags[i].name == item.value) {
          myMessage.warning(this.$i18n.t('m.Add_Tag_Error'));
          this.tagInput = '';
          return;
        }
      }
      this.tagInput = item.value;
    },
    addTag(item) {
      let newTag = {
        name: this.tagInput,
      };
      if (this.tagInput) {
        for (var i = 0; i < this.problemTags.length; i++) {
          if (this.problemTags[i].name == this.tagInput) {
            myMessage.warning(this.$i18n.t('m.Add_Tag_Error'));
            this.tagInput = '';
            return;
          }
        }
        this.problemTags.push(newTag);
        this.inputVisible = false;
        this.tagInput = '';
      }
    },

    handleClickTabs(tab, event) {
      this.options = []
    },
    // 根据tag name从题目的tags列表中移除
    closeTag(tag) {
      this.problemTags.splice(
          this.problemTags.map((item) => item.name).indexOf(tag),
          1,
      );
    },

    deleteFile(type, name) {
      if (type == 'user') {
        this.$delete(this.userExtraFile, name);
      } else {
        this.$delete(this.judgeExtraFile, name);
      }
    },

    upsertFile(type, name, oldname, content) {
      if (type == 'user') {
        if (oldname && oldname != name) {
          this.$delete(this.userExtraFile, oldname);
        }
        if (!this.userExtraFile) {
          this.userExtraFile = {};
        }
        this.userExtraFile[name] = content;
      } else {
        if (oldname && oldname != name) {
          this.$delete(this.judgeExtraFile, name);
        }
        if (!this.judgeExtraFile) {
          this.judgeExtraFile = {};
        }
        this.judgeExtraFile[name] = content;
      }
    },

    problemTypeChange(type) {
      if (type == 1) {
        let length = this.problemSamples.length;
        let aver = parseInt(100 / length);
        let add_1_num = 100 - aver * length;
        for (let i = 0; i < length; i++) {
          if (i >= length - add_1_num) {
            this.problemSamples[i].score = aver + 1;
          } else {
            this.problemSamples[i].score = aver;
          }
        }
      }
    },

    // 添加题目样例
    addExample() {
      this.problem.examples.push({input: '', output: '', isOpen: true});
    },
    changeExampleVisible(index, isOpen) {
      this.problem.examples[index]['isOpen'] = isOpen;
    },
    // 添加判题机的测试样例
    addSample() {
      let len = this.sampleIndex;
      if (this.mode === 'edit') {
        this.problemSamples.push({
          input: '',
          output: '',
          score: this.problem.type == 0 ? null : 0,
          groupNum: this.problem.type == 0 ? null : len,
          pid: this.pid,
          isOpen: true,
          index: len,
        });
      } else {
        this.problemSamples.push({
          input: '',
          output: '',
          score: this.problem.type == 0 ? null : 0,
          groupNum: this.problem.type == 0 ? null : len,
          pid: this.pid,
          isOpen: true,
          index: len,
        });
      }
      this.sampleIndex = len + 1;
      this.sortManualProblemSampleList();
    },
    //根据下标删除特定的题目样例
    deleteExample(index) {
      this.problem.examples.splice(index, 1);
    },
    //根据下标删除特定的判题机测试样例
    deleteSample(index) {
      this.problemSamples.splice(index, 1);
    },
    changeSampleVisible(index, isOpen) {
      this.problemSamples[index]['isOpen'] = isOpen;
    },
    uploadSucceeded(response) {
      if (response.status != 200) {
        myMessage.error(response.msg);
        this.testCaseUploaded = false;
        return;
      }
      myMessage.success(this.$i18n.t('m.Upload_Testcase_Successfully'));
      let fileList = response.data.fileList;
      let averSorce = parseInt(100 / fileList.length);
      let add_1_num = 100 - averSorce * fileList.length;
      for (let i = 0; i < fileList.length; i++) {
        if (averSorce) {
          if (i >= fileList.length - add_1_num) {
            fileList[i].score = averSorce + 1;
          } else {
            fileList[i].score = averSorce;
          }
        }
        if (!fileList[i].output) {
          fileList[i].output = '-';
        }
        fileList[i].pid = this.problem.id;
      }
      this.problem.testCaseScore = fileList;
      this.problem.testCaseScore.forEach((item, index) => {
        item.index = index + 1;
      });
      this.testCaseUploaded = true;
      this.problem.uploadTestcaseDir = response.data.fileListDir;
    },
    uploadFailed() {
      myMessage.error(this.$i18n.t('m.Upload_Testcase_Failed'));
    },

    compileSPJ() {
      let data = {
        pid: this.problem.id,
        code: this.problem.spjCode,
        language: this.problem.spjLanguage,
        extraFiles: this.judgeExtraFile,
      };
      this.loadingCompile = true;
      let apiMethodName = 'compileSPJ';
      if (this.problem.judgeMode == 'interactive') {
        apiMethodName = 'compileInteractive';
      }
      api[apiMethodName](data).then(
          (res) => {
            this.loadingCompile = false;
            this.problem.spjCompileOk = true;
            this.error.spj = '';
            myMessage.success(this.$i18n.t('m.Compiled_Successfully'));
          },
          (err) => {
            this.loadingCompile = false;
            this.problem.spjCompileOk = false;
            const h = this.$createElement;
            this.$msgbox({
              title: 'Compile Error',
              type: 'error',
              message: h('pre', err.data.msg),
              showCancelButton: false,
              closeOnClickModal: false,
              customClass: 'dialog-compile-error',
            });
          },
      );
    },
    sortTestCaseList() {
      this.$refs.xTable.clearSort();
      this.$refs.xTable.sort('groupNum', 'asc');
    },
    customSortMethod({data, sortList}) {
      const sortItem = sortList[0];
      const {property, order} = sortItem;
      let list = [];
      list = data.sort(function (a, b) {
        var value1 = a.groupNum,
            value2 = b.groupNum;
        if (value1 === value2) {
          return a.index - b.index;
        }
        if (order == 'desc') {
          return value2 - value1;
        } else {
          return value1 - value2;
        }
      });
      return list;
    },
    sortManualProblemSampleList() {
      this.problemSamples = this.problemSamples.sort(function (a, b) {
        var value1 = a.groupNum,
            value2 = b.groupNum;
        if (value1 === value2) {
          return a.index - b.index;
        }
        return value1 - value2;
      });
    },
    submit() {
      console.log('submit', this.options, this.problem);
      this.problem.problemType = this.activateProblemType.code;

      const isValidate = this.validateSubmission();
      if (!isValidate) {
        return;
      }

      let funcName = {
        'admin-create-problem': 'admin_createProblem',
        'admin-edit-problem': 'admin_editProblem',
        'admin-create-contest-problem': 'admin_createContestProblem',
        'admin-edit-contest-problem': 'admin_editContestProblem',
      }[this.routeName];
      // edit contest problem 时, contest_id会被后来的请求覆盖掉
      if (funcName === 'editContestProblem') {
        this.problem.cid = this.contest.id;
      }
      if (
          funcName === 'admin_createProblem' ||
          funcName === 'admin_createContestProblem'
      ) {
        this.problem.author = this.userInfo.username;
      }

      var ojName = 'ME';
      if (this.problem.isRemote) {
        ojName = this.problem.problemId.split('-')[0];
      }

      let problemTagList = [];
      if (this.problemTags.length > 0) {
        problemTagList = Object.assign([], this.problemTags);
        for (let i = 0; i < problemTagList.length; i++) {
          //避免后台插入违反唯一性
          let found = false;
          for (let tag2 of this.allTags) {
            if (problemTagList[i].name == tag2.name && tag2.oj == ojName) {
              problemTagList[i] = tag2;
              found = true;
              break;
            }
          }
          // 如果在allTags中没有找到匹配项，则添加oj属性
          if (!found) {
            problemTagList[i].oj = ojName;
          }
        }
      }
      this.problemCodeTemplate = [];
      let problemLanguageList = Object.assign([], this.problemLanguages); // 深克隆 防止影响
      for (let i = 0; i < problemLanguageList.length; i++) {
        problemLanguageList[i] = {name: problemLanguageList[i]};
        for (let lang of this.allLanguage) {
          if (problemLanguageList[i].name == lang.name) {
            problemLanguageList[i] = lang;
            if (this.codeTemplate[lang.name].status) {
              if (this.codeTemplate[lang.name].code == null
                  || this.codeTemplate[lang.name].code.length == 0) {
                myMessage.error(
                    lang.name +
                    '：' +
                    this.$i18n.t('m.Code_template_of_the_language_cannot_be_empty'),
                );
                return;
              }
              this.problemCodeTemplate.push({
                id: this.codeTemplate[lang.name].id,
                pid: this.pid,
                code: this.codeTemplate[lang.name].code,
                lid: lang.id,
                status: this.codeTemplate[lang.name].status,
              });
            }
            break;
          }
        }
      }
      let problemDto = {}; // 上传给后台的数据
      if (!this.problem.isRemote) {
        if (this.problem.judgeMode != 'default') {
          let isChangeModeCode = this.spjRecord.spjLanguage !== this.problem.spjLanguage ||
                                 this.spjRecord.spjCode !== this.problem.spjCode;
          if (isChangeModeCode) {
            problemDto['changeModeCode'] = true;
          }
        } else {
          // 原本是spj或交互，但现在关闭了
          if (!this.spjRecord.spjCode) {
            problemDto['changeModeCode'] = true;
            this.problem.spjCode = null;
            this.problem.spjLanguage = null;
          }
        }

        if (this.userExtraFile && Object.keys(this.userExtraFile).length != 0) {
          this.problem.userExtraFile = JSON.stringify(this.userExtraFile);
        } else {
          this.problem.userExtraFile = null;
        }

        if (
            this.judgeExtraFile &&
            Object.keys(this.judgeExtraFile).length != 0
        ) {
          this.problem.judgeExtraFile = JSON.stringify(this.judgeExtraFile);
        } else {
          this.problem.judgeExtraFile = null;
        }
      }

      problemDto['problem'] = Object.assign({}, this.problem); // 深克隆
      problemDto.problem.examples = utils.examplesToString(
          this.problem.examples,
      ); // 需要转换格式

      problemDto['codeTemplates'] = this.problemCodeTemplate;
      problemDto['tags'] = problemTagList;
      problemDto['languages'] = problemLanguageList;
      problemDto['isUploadTestCase'] = this.problem.isUploadCase;
      problemDto['uploadTestcaseDir'] = this.problem.uploadTestcaseDir;
      problemDto['judgeMode'] = this.problem.judgeMode;
      problemDto['options'] = this.options;

      // 如果选择上传文件，则使用上传后的结果
      if (this.problem.isUploadCase) {
        problemDto['samples'] = this.problem.testCaseScore;
      } else {
        problemDto['samples'] = this.problemSamples;
      }

      if (this.judgeCaseModeRecord != this.problem.judgeCaseModeRecord) {
        problemDto['changeJudgeCaseMode'] = true;
      } else {
        problemDto['changeJudgeCaseMode'] = false;
      }

      api[funcName](problemDto)
          .then((res) => {
            if (
                this.routeName === 'admin-create-contest-problem' ||
                this.routeName === 'admin-edit-contest-problem'
            ) {
              if (res.data.data) {
                // 新增题目操作 需要使用返回来的pid
                this.contestProblem['pid'] = res.data.data.pid;
                this.contestProblem['cid'] = this.$route.params.contestId;
              }
              api.admin_setContestProblemInfo(this.contestProblem).then((res) => {
                myMessage.success('success');
                this.$router.push({
                  name: 'admin-contest-problem-list',
                  params: {contestId: this.$route.params.contestId},
                });
              });
            } else {
              myMessage.success('success');
              if (this.backPath) {
                this.$router.push({path: this.backPath});
              } else {
                this.$router.push({name: 'admin-problem-list'});
              }
            }
          })
          .catch(() => {
          });
    },

    /**
     * 执行题目提交前的完整校验
     * @returns {boolean} 是否通过所有校验，true表示校验通过
     * @description 包含以下校验维度：
     * 1. 基础信息校验（题目ID、比赛题目信息）
     * 2. 文件IO模式校验
     * 3. 测试数据校验（手动输入/文件上传两种模式）
     * 4. 特殊判题模式校验
     * 5. 编程语言校验
     */
    validateSubmission() {
      if (this.activateProblemType !== ProblemType.Programmer) {
        return true;
      }
      // 基础信息校验
      const basicChecks = [
        // 问题ID必填校验
        {
          condition: !this.problem.problemId,
          message: `${this.$i18n.t('m.Problem_Display_ID')} ${this.$i18n.t('m.is_required')}`,
        },
        // 文件IO模式校验
        {
          condition: this.problem.isFileIO && (!this.problem.ioReadFileName || !this.problem.ioWriteFileName),
          message: this.$i18n.t('m.When_the_read_write_mode_is_File_IO_the_input_file_name_or_output_file_name_cannot_be_empty'),
        },
      ];

      for (const check of basicChecks) {
        if (check.condition) {
          myMessage.error(check.message);
          return false;
        }
      }


      // 比赛题目信息校验
      if (this.contestID) {
        const contestChecks = [
          {field: 'displayId', message: 'm.Contest_Display_ID'},
          {field: 'displayTitle', message: 'm.Contest_Display_Title'},
        ];

        for (const {field, message} of contestChecks) {
          if (!this.contestProblem[field]) {
            myMessage.error(`${this.$i18n.t(message)} ${this.$i18n.t('m.is_required')}`);
            return false;
          }
        }
      }

      // 本地题目数据校验
      if (!this.problem.isRemote) {
        // 测试数据校验
        if (this.problem.isUploadCase) {
          // 文件上传模式校验
          if (!this.testCaseUploaded) {
            myMessage.error(`${this.$i18n.t('m.Judge_Samples')} ${this.$i18n.t('m.is_required')}`);
            return false;
          }
        } else {
          // 手动输入模式校验
          const sampleValidation = this.validateManualSamples();
          if (!sampleValidation.valid) {
            myMessage.error(sampleValidation.message);
            return false;
          }
        }

        // OJ题目特殊校验
        if (this.problem.type === 1) {
          const oiValidation = this.validateOISamples();
          if (!oiValidation.valid) {
            myMessage.error(oiValidation.message);
            return false;
          }
        }
      }

      // 特殊判题代码校验
      const spjValidation = this.validateSpecialJudgeCode();
      if (!spjValidation.valid) {
        myMessage.error(spjValidation.message);
        return false;
      }

      // 编程语言校验
      if (!this.problemLanguages.length) {
        myMessage.error(`${this.$i18n.t('m.Language')} ${this.$i18n.t('m.is_required')}`);
        return false;
      }

      return true;
    },

    /**
     * 校验手动输入的测试样例
     * @returns {{valid: boolean, message?: string}} 校验结果
     */
    validateManualSamples() {
      if (!this.problemSamples.length) {
        return {
          valid: false,
          message: `${this.$i18n.t('m.Judge_Samples')} ${this.$i18n.t('m.is_required')}`,
        };
      }

      for (const sample of this.problemSamples) {
        if (!sample.input && !sample.output) {
          return {
            valid: false,
            message: `${this.$i18n.t('m.Sample_Input')} or ${this.$i18n.t('m.Sample_Output')} ${this.$i18n.t('m.is_required')}`,
          };
        }
      }
      return {valid: true};
    },

    /**
     * 校验OI题目测试样例得分
     * @returns {{valid: boolean, message?: string}} 校验结果
     */
    validateOISamples() {
      const samples = this.problem.isUploadCase ? this.problem.testCaseScore : this.problemSamples;

      for (let i = 0; i < samples.length; i++) {
        const sample = samples[i];
        const index = this.problem.isUploadCase ? i + 1 : sample.index;

        // 空值校验
        if (sample.score === '') {
          return {
            valid: false,
            message: `${this.$i18n.t('m.Problem_Sample')}${index} ${this.$i18n.t('m.Score_must_be_an_integer')}`,
          };
        }

        // 数值有效性校验
        try {
          const score = parseInt(sample.score);
          if (score < 0) {
            return {
              valid: false,
              message: `${this.$i18n.t('m.Problem_Sample')}${index} ${this.$i18n.t('m.Score_must_be_greater_than_or_equal_to_0')}`,
            };
          }
        } catch (e) {
          return {valid: false, message: this.$i18n.t('m.Score_must_be_an_integer')};
        }

        // 分组编号校验
        const needGroupNum = [this.JUDGE_CASE_MODE.SUBTASK_LOWEST, this.JUDGE_CASE_MODE.SUBTASK_AVERAGE]
            .includes(this.problem.judgeCaseMode);

        if (needGroupNum && sample.groupNum === '') {
          return {
            valid: false,
            message: `${this.$i18n.t('m.Problem_Sample')}${index}：${this.$i18n.t('m.Non_Default_Judge_Case_Mode_And_Group_Num_IS_NULL')}`,
          };
        }
      }
      return {valid: true};
    },

    /**
     * 校验特殊判题代码
     * @returns {{valid: boolean, message?: string}} 校验结果
     */
    validateSpecialJudgeCode() {
      if (this.problem.isRemote || this.problem.judgeMode === 'default') {
        return {valid: true};
      }

      const isChanged = this.spjRecord.spjLanguage !== this.problem.spjLanguage ||
          this.spjRecord.spjCode !== this.problem.spjCode;

      if (!this.problem.spjCode) {
        this.error.spj = `${this.$i18n.t('m.Spj_Or_Interactive_Code')} ${this.$i18n.t('m.is_required')}`;
        return {valid: false, message: this.error.spj};
      }

      if (isChanged && !this.problem.spjCompileOk) {
        this.error.spj = this.$i18n.t('m.Spj_Or_Interactive_Code_not_Compile_Success');
        return {valid: false, message: this.error.spj};
      }

      return {valid: true};
    },
  },
  computed: {
    ProblemType() {
      return ProblemType;
    },
    // 添加计算属性优化代码可读性
    isChoiceType() {
      return [
        ProblemType.SingleChoice.code,
        ProblemType.MultipleChoice.code,
        ProblemType.TrueFalse.code,
      ].includes(this.activateProblemType.code);
    },
    ...mapGetters(['userInfo']),
  },
};
</script>

<style scoped>
/deep/ .el-form-item__label {
  padding: 0 !important;
}

.el-form-item {
  margin-bottom: 10px !important;
}

.difficulty-select {
  width: 120px;
}

.input-new-tag {
  width: 120px;
}

.button-new-tag {
  height: 24px;
  line-height: 22px;
  padding-top: 0;
  padding-bottom: 0;
}

.accordion {
  margin-bottom: 10px;
}

.add-examples {
  width: 100%;
  background-color: #fff;
  border: 1px dashed #2d8cf0;
  outline: none;
  cursor: pointer;
  color: #2d8cf0;
  height: 35px;
  font-size: 14px;
}

.add-examples i {
  margin-right: 10px;
}

.add-examples:hover {
  border: 0px;
  background-color: #2d8cf0 !important;
  color: #fff;
}

.add-example-btn {
  margin-bottom: 10px;
}

.add-samples {
  width: 100%;
  background-color: #fff;
  border: 1px dashed #19be6b;
  outline: none;
  cursor: pointer;
  color: #19be6b;
  height: 35px;
  font-size: 14px;
}

.add-samples i {
  margin-right: 10px;
}

.add-samples:hover {
  border: 0px;
  background-color: #19be6b !important;
  color: #fff;
}

.add-sample-btn {
  margin-bottom: 10px;
}

.dialog-compile-error {
  width: auto;
  max-width: 80%;
  overflow-x: scroll;
}
</style>
