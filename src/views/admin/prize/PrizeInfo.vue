<template>
  <prize-info :admin="true" :columns="6" :current-points="0" :show-exchange-button="false"/>
</template>
<script>
import api from '@/common/api';
import myMessage from "@/common/message";
import PrizeInfo from "@/components/tuc/prize/PrizeInfo.vue";

export default {
  components: {PrizeInfo: PrizeInfo},
  data() {
    return {}
  },

  mounted() {

  },

  methods: {}
}
</script>
<style scoped>

</style>
