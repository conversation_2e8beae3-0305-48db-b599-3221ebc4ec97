<template>
  <div>
    <el-form :inline="true" :model="queryParams" class="search-form">
      <el-form-item label="用户名">
        <el-input v-model="queryParams.username" clearable placeholder="用户名"/>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="queryParams.status" clearable placeholder="状态">
          <el-option label="全部" value=""/>
          <el-option :value="1" label="已发放"/>
          <el-option :value="0" label="待发放"/>
        </el-select>
      </el-form-item>
      <el-form-item label="兑换时间">
        <el-date-picker
            v-model="queryParams.redeemTimeRange"
            end-placeholder="结束日期"
            range-separator="至"
            start-placeholder="开始日期"
            type="daterange"
            value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
      </el-form-item>
    </el-form>
    <redemption-history
        :data="tableData"
        :auto-load="true"
        :pagination="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    >
      <template #actions="{ row }">
        <el-button icon="el-icon-delete" plain size="mini" type="danger"
                   @click="handleDeleteRecord(row.id)"></el-button>
        <el-button icon="el-icon-edit" plain size="mini" type="primary" @click="handleEditStatus(row)"></el-button>
      </template>
    </redemption-history>
  </div>
</template>

<script>
import RedemptionHistory from "@/components/tuc/prize/RedemptionHistory.vue";
import api from '@/common/api';

export default {
  components: {RedemptionHistory},
  data() {
    return {
      queryParams: {
        username: '',
        status: null,
        redeemTimeRange: []
      },
      tableData: [],
      pagination: {
        current: 1,
        size: 10,
        total: 0
      }
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      const  params={
        currentPage: this.pagination.current,
        limit: this.pagination.size
      };
      const data = {
        username: this.queryParams.username,
        status: this.queryParams.status,
        startTime: this.queryParams.redeemTimeRange ? this.queryParams.redeemTimeRange[0] : null,
        endTime: this.queryParams.redeemTimeRange ? this.queryParams.redeemTimeRange[1] : null
      };

      try {
        const res = await api.getRedemptionHistory(params, data);
        this.tableData = res.data.data;
        console.log("tableData",this.tableData)
        this.pagination.total = res.data.data.total;
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    },
    handleSearch() {
      this.pagination.current = 1;
      this.fetchData();
    },
    handleSizeChange(size) {
      this.pagination.size = size;
      this.fetchData();
    },
    handleCurrentChange(current) {
      this.pagination.current = current;
      this.fetchData();
    },
    async handleDeleteRecord(id) {
      try {
        await this.$confirm('确认删除该记录？', '提示', { type: 'warning' });
        await api.redemptionDelete(id);
        this.$message.success('删除成功');
        this.fetchData(); // 刷新数据
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败');
        }
      }
    },
    async handleEditStatus(row) {
      try {
        const newStatus = row.status ? 0 : 1;
        await this.$confirm(`确定标记为${newStatus ? '已发放' : '待发放'}?`, '提示', { type: 'warning' });
        await api.redemptionUpdateStatus({ id: row.id, status: newStatus });
        this.$message.success('状态更新成功');
        this.fetchData();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('更新失败');
        }
      }
    }
  }
};
</script>
<style scoped>

</style>
