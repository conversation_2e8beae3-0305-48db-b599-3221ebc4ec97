<template>
  <div class="view">
    <h1>角色管理</h1>

    <div class="role-container">
      <el-row align="middle" justify="space-between" type="flex">
        <!-- 搜索框 -->
        <el-col :span="6">
          <el-input v-model="searchQuery.keyword" class="search-bar" placeholder="输入角色名进行搜索" size="mini"
                    @change="searchRole">
            <template #prepend>
              <el-icon name="search" @click="searchRole"></el-icon>
            </template>
          </el-input>
        </el-col>

        <!-- 添加角色按钮 -->
        <el-col v-if="false" :span="6">
          <el-button size="mini" type="primary" @click="openCreateDialog">
            <el-icon name="plus"></el-icon>
            添加角色
          </el-button>
        </el-col>
      </el-row>
    </div>

    <el-table v-loading="loading" :data="roleList" size="mini" style="width: 100%;">
      <el-table-column label="ID" prop="id" width="80"></el-table-column>
      <el-table-column label="角色" prop="role"></el-table-column>
      <el-table-column label="描述" prop="description"></el-table-column>
      <el-table-column label="状态" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
            {{ scope.row.status === 0 ? '可用' : '不可用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column v-if="false" align="center" label="操作">
        <template slot-scope="scope">
          <div style="display: flex; justify-content: center; align-items: center; gap: 10px;">
            <el-button icon="el-icon-edit" plain size="small" type="primary" @click="openEditDialog(scope.row)">编辑
            </el-button>
            <el-button icon="el-icon-delete" plain size="small" type="danger" @click="openDeleteDialog(scope.row)">删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :page-sizes="[10, 20, 30, 40]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="currentChange"
    ></el-pagination>

    <!-- 表单弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
      <el-form ref="form" :model="currentRole" :rules="rules" class="optimized-form" label-width="100px">
        <el-form-item label="角色" prop="role">
          <el-input
              v-model="currentRole.role"
              maxlength="25"
              placeholder="请输入角色名称"
              show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
              v-model="currentRole.description"
              maxlength="100"
              placeholder="请输入角色描述"
              show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="currentRole.status" placeholder="请选择状态">
            <el-option :value="0" label="可用"></el-option>
            <el-option :value="1" label="不可用"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-row align="middle" gutter="16" justify="end">
          <el-button style="width: 100px;" @click="dialogVisible = false">取消</el-button>
          <el-button style="width: 100px;" type="primary" @click="submitForm">确定</el-button>
        </el-row>
      </div>
    </el-dialog>

    <!-- 删除确认弹窗 -->
    <el-dialog :visible.sync="deleteDialogVisible" title="确认删除" width="400px">
      <span>确定删除此项吗？</span>
      <div slot="footer" class="dialog-footer">
        <el-row align="middle" gutter="16" justify="space-between">
          <el-button style="width: 100px;" @click="deleteDialogVisible = false">取消</el-button>
          <el-button style="width: 100px;" type="danger" @click="deleteItem">删除</el-button>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from "@/common/api";
import myMessage from "@/common/message";

export default {
  name: "role",
  data() {
    return {
      pageSize: 10,
      total: 0,
      currentPage: 1,
      roleList: [],
      loading: false,
      searchQuery: {
        keyword: "",
      },
      dialogVisible: false,
      dialogTitle: "创建角色",
      deleteDialogVisible: false,
      itemToDelete: null,
      rules: {
        role: [
          {required: true, message: "请输入角色名称", trigger: "blur"},
          {min: 2, max: 25, message: "角色名称长度应在2到25之间", trigger: "blur"},
        ],
        description: [
          {required: true, message: "请输入角色描述", trigger: "blur"},
          {min: 5, max: 100, message: "描述长度应在5到100之间", trigger: "blur"},
        ],
      },
      currentRole: {
        id: null,
        role: "",
        description: "",
        status: 0,
        gmtCreate: null,
        gmtModified: null,
      },
    };
  },
  mounted() {
    this.getRoleList();
  },
  methods: {
    getRoleList() {
      this.loading = true;
      api.admin_getRoleList(this.currentPage, this.pageSize, this.searchQuery.keyword)
          .then((res) => {
            this.roleList = res.data.data.records;
            this.total = res.data.data.total;
            this.loading = false;
          })
          .catch(() => {
            this.loading = false;
          });
    },
    searchRole() {
      this.currentPage = 1;
      this.getRoleList();
    },
    openCreateDialog() {
      this.dialogTitle = "创建角色";
      this.currentRole = {id: null, role: "", description: "", status: 0};
      this.dialogVisible = true;
    },
    openEditDialog(role) {
      this.dialogTitle = "编辑角色";
      this.currentRole = {...role};
      this.dialogVisible = true;
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const action = this.currentRole.id
              ? api.updateRole(this.currentRole)
              : api.addRole(this.currentRole);

          action
              .then(() => {
                this.dialogVisible = false;
                this.getRoleList();
              })
              .catch((error) => {
                console.error("操作失败:", error);
              });
        }
      });
    },
    openDeleteDialog(role) {
      this.itemToDelete = role;
      this.deleteDialogVisible = true;
    },
    deleteItem() {
      api
          .deleteRole(this.itemToDelete.id)
          .then(() => {
            myMessage.success("删除成功");
            this.getRoleList();
          })
          .catch(() => {
            myMessage.error("删除失败");
          });
      this.deleteDialogVisible = false;
    },
    currentChange(page) {
      this.currentPage = page;
      this.getRoleList();
    },
    onPageSizeChange(size) {
      this.pageSize = size;
      this.getRoleList();
    },
  },
};
</script>
