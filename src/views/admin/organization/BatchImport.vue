<template>
  <div class="view">
    <!-- 导入csv用户数据 -->
    <el-card style="margin-top:20px">
      <div slot="header">
        <span class="panel-title home-title">{{ $t('m.Import_User') }}</span>
      </div>
      <p>1. {{ $t('m.Import_User_Tips1') }}</p>
      <p>2. {{ $t('m.Import_User_Tips2') }}</p>
      <p>3. {{ $t('m.Import_User_Tips3') }}</p>
      <p>4. {{ $t('m.Import_User_Tips4') }}</p>
      <p>5. {{ $t('m.Import_User_Tips5') }}</p>
      <el-upload
          v-if="!uploadUsers.length"
          :before-upload="handleUsersCSV"
          :show-file-list="false"
          accept=".csv"
          action=""
      >
        <el-button icon="el-icon-folder-opened" size="small" type="primary">
          {{ $t('m.Choose_File') }}
        </el-button>
      </el-upload>
      <template v-else>
        <!-- Updated data display table -->
        <vxe-table :data="uploadUsersPage" auto-resize stripe>
          <vxe-table-column :title="$t('m.Username')" field="username" min-width="96" show-overflow>
            <template v-slot="{ row }">
              {{ row.username }}
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.Password')" field="password" min-width="130" show-overflow>
            <template v-slot="{ row }">
              {{ row.password }}
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.RealName')" field="realname" min-width="120" show-overflow>
            <template v-slot="{ row }">
              {{ row.realname }}
            </template>
          </vxe-table-column>
          <!-- New role column -->
          <vxe-table-column :title="$t('m.Role')" field="role" min-width="100" show-overflow>
            <template v-slot="{ row }">
              {{ row.role }}
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.Group_Code')" field="groupCode" min-width="150" show-overflow>
            <template v-slot="{ row }">
              {{ row.groupCode }}
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.Nickname')" field="nickname" min-width="100" show-overflow>
            <template v-slot="{ row }">
              {{ row.nickname }}
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.School')" field="school" min-width="100" show-overflow>
            <template v-slot="{ row }">
              {{ row.school }}
            </template>
          </vxe-table-column>
          <vxe-table-column :title="$t('m.Gender')" field="gender" min-width="60" show-overflow>
            <template v-slot="{ row }">
              {{ row.gender }}
            </template>
          </vxe-table-column>
        </vxe-table>

        <div class="panel-options">
          <el-button icon="el-icon-upload" size="small" type="primary" @click="handleUsersUpload">
            {{ $t('m.Upload_All') }}
          </el-button>
          <el-button icon="el-icon-delete" size="small" type="danger" @click="handleResetData">
            {{ $t('m.Clear_All') }}
          </el-button>
          <el-pagination
              :current-page.sync="uploadUsersCurrentPage"
              :page-size="uploadUsersPageSize"
              :total="uploadUsers.length"
              class="page"
              layout="prev, pager, next"
          >
          </el-pagination>
        </div>
      </template>
    </el-card>


    <!-- 批量导入班级 -->
    <el-card style="margin-top:20px">
      <div slot="header">
        <span class="panel-title home-title">{{ $t('m.Import_Group') }}</span>
      </div>
      <p>1. {{ $t('m.Import_Group_Tips1') }}</p>
      <p>2. {{ $t('m.Import_Group_Tips2') }}</p>
      <el-upload v-if="!uploadGroups.length" :before-upload="handleGroupsCSV" :show-file-list="false" accept=".csv"
                 action="">
        <el-button icon="el-icon-folder-opened" size="small" type="primary">
          {{ $t('m.Choose_File') }}
        </el-button>
      </el-upload>
      <template v-else>
        <vxe-table :data="uploadGroupsPage" auto-resize stripe>
          <vxe-table-column :title="$t('m.Name')" field="name" min-width="120" show-overflow/>
          <vxe-table-column :title="$t('m.ShortName')" field="shortName" min-width="100" show-overflow/>
          <vxe-table-column :title="$t('m.Brief')" field="brief" min-width="150" show-overflow/>
          <vxe-table-column :title="$t('m.Description')" field="description" min-width="200" show-overflow/>
          <vxe-table-column :title="$t('m.Username')" field="username" min-width="120" show-overflow/>
          <vxe-table-column :title="$t('m.Group_Code')" field="code" min-width="120" show-overflow/>
        </vxe-table>
        <div class="panel-options">
          <el-button icon="el-icon-upload" size="small" type="primary" @click="handleGroupsUpload">
            {{ $t('m.Upload_All') }}
          </el-button>
          <el-button icon="el-icon-delete" size="small" type="danger" @click="handleResetGroupData">
            {{ $t('m.Clear_All') }}
          </el-button>
        </div>
      </template>
    </el-card>
  </div>
</template>

<script>
import papa from 'papaparse'; // csv插件
import api from '@/common/api';
import myMessage from '@/common/message';

export default {
  name: 'user',
  data() {
    return {

      // 用户导入
      uploadUsers: [],
      uploadUsersPage: [],
      uploadUsersCurrentPage: 1,
      uploadUsersPageSize: 15,
      // 班级导入
      uploadGroups: [],
      uploadGroupsPage: [],
      uploadGroupsCurrentPage: 1,
      uploadGroupsPageSize: 15,
    };
  },
  methods: {
    handleUsersCSV(file) {
      papa.parse(file, {
        complete: (results) => {
          // Map each user row to an object according to the structure of BatchImportUserDTO
          let data = results.data.map((user) => {
            return {
              username: user[0],
              password: user[1],
              realname: user[2],
              role: user[3] || 'default_user',    // Assuming role is the 4th column
              groupCode: user[4] || '',
              nickname: user[5] || '',
              school: user[6] || '',
              gender: user[7] || ''
            };
          }).filter(user => user.username && user.password && user.realname);

          let delta = results.data.length - data.length;
          if (delta > 0) {
            myMessage.warning(delta + this.$i18n.t('m.Generate_Skipped_Reason'));
          }

          this.uploadUsersCurrentPage = 1;
          this.uploadUsers = data;
          this.uploadUsersPage = data.slice(0, this.uploadUsersPageSize);
        },
        error: (error) => {
          myMessage.error(error);
        },
      });
    },
    handleUsersUpload() {
      api.admin_importUsers(this.uploadUsers)
          .then(() => {
            this.handleResetData();
            myMessage.success(this.$i18n.t('m.Upload_Successfully'));
          })
          .catch(() => {
          });
    },
    handleResetData() {
      this.uploadUsers = [];
    },
    handleGroupsCSV(file) {
      papa.parse(file, {
        complete: (results) => {
          const data = results.data
              .map((group) => ({
                name: group[0],
                shortName: group[1],
                brief: group[2],
                description: group[3],
                username: group[4],
                code: group[5],
              }))
              .filter(
                  (group) =>
                      group.name &&
                      group.shortName &&
                      group.brief &&
                      group.description &&
                      group.username &&
                      group.code
              );
          this.uploadGroups = data;
          this.uploadGroupsPage = data.slice(0, this.uploadGroupsPageSize);
        },
        error: (error) => {
          myMessage.error(error.message);
        },
      });
    },
    handleGroupsUpload() {
      api.admin_importGroups(this.uploadGroups)
          .then(() => {
            this.handleResetGroupData();
            myMessage.success(this.$t('m.Upload_Groups_Successfully'));
          })
          .catch(() => {
          });
    },
    handleResetGroupData() {
      this.uploadGroups = [];
    },
  },
  watch: {
    uploadUsersCurrentPage(page) {
      this.uploadUsersPage = this.uploadUsers.slice(
          (page - 1) * this.uploadUsersPageSize,
          page * this.uploadUsersPageSize
      );
    },
    uploadGroupsCurrentPage(page) {
      this.uploadGroupsPage = this.uploadGroups.slice(
          (page - 1) * this.uploadGroupsPageSize,
          page * this.uploadGroupsPageSize
      );
    },
  },
};
</script>

<style scoped>
.import-user-icon {
  color: #555555;
  margin-left: 4px;
}

.userPreview {
  padding-left: 10px;
}

/deep/ .el-tag--dark {
  border-color: #fff;
}

/deep/ .el-dialog__body {
  padding-bottom: 0;
}

/deep/ .el-form-item {
  margin-bottom: 10px !important;
}

.notification p {
  margin: 0;
  text-align: left;
}

.filter-row {
  margin-top: 10px;
}

@media screen and (max-width: 768px) {
  .filter-row span {
    margin-right: 5px;
  }
}

@media screen and (min-width: 768px) {
  .filter-row span {
    margin-right: 20px;
  }
}
</style>
