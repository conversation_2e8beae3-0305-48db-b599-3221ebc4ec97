<template>
  <div class="view">
    <!--用户展示-->
    <el-card>
      <div slot="header">
        <span class="panel-title home-title">{{ $t('m.General_User') }}</span>
        <div class="handler-row">
          <!-- 操作 容器 -->
          <div class="handler-container">
            <!-- 操作按钮 组合在一起 -->
            <div class="handler-btn-group">
              <el-button icon="el-icon-delete-solid" plain size="small" type="danger" @click="deleteUsers(null)">{{ $t('m.Delete') }}</el-button>
              <el-button icon="el-icon-refresh" plain size="small" type="warning" @click="resetFilters">{{ $t('m.Refresh') }}</el-button>
              <el-button icon="el-icon-search" plain size="small" type="primary" @click="searchData">{{ $t('m.Search') }}</el-button>
              <el-button icon="el-icon-plus" plain size="small" type="success" @click="openUserDialog(null,1)">创建用户</el-button>
            </div>
          </div>

          <!-- 输入 容器 -->
          <div class="select-container">
            <!-- 选择角色 -->
            <div class="select-item">
              <el-select v-model="queryParam.roleIds" multiple placeholder="请选择角色" size="medium">
                <el-option v-for="role in roleList" :key="role.id" :label="role.role + ' - ' + role.description" :value="role.id"/>
              </el-select>
            </div>

            <!-- 选择班级 -->
            <div class="select-item">
              <el-select v-model="queryParam.groupIds" multiple placeholder="请选择班级" size="medium">
                <el-option v-for="group in groupList" :key="group.id" :label="group.name" :value="group.id"/>
              </el-select>
            </div>

            <!-- 搜索输入框 -->
            <div class="select-item">
              <el-input
                  v-model="keyword"
                  :placeholder="$t('m.Enter_keyword')"
                  prefix-icon="el-icon-search"
                  size="medium"
                  type="search"
                  @search-click="filterByKeyword"
                  @keyup.enter.native="filterByKeyword">
              </el-input>
            </div>
          </div>
        </div>
      </div>
      <vxe-table
          ref="xTable"
          :checkbox-config="{ labelField: 'id', highlight: true, range: true, checkMethod: checCheckboxkMethod }"
          :data="userList"
          :loading="loadingTable"
          auto-resize
          stripe
          @checkbox-change="handleSelectionChange"
          @checkbox-all="handlechangeAll"
      >
        <vxe-table-column type="checkbox" width="60"></vxe-table-column>
        <vxe-table-column
            :title="$t('m.User')"
            field="username"
            min-width="150"
            show-overflow
        >
          <template v-slot="{ row }">
            <span>{{ row.username }}</span>
            <span style="margin-left:2px">
              <el-tag
                  v-if="row.titleName"
                  :color="row.titleColor"
                  effect="dark"
                  size="small"
              >
                {{ row.titleName }}
              </el-tag>
            </span>
          </template>
        </vxe-table-column>
        <vxe-table-column
            :title="$t('m.RealName')"
            field="realname"
            min-width="100"
            show-overflow
        ></vxe-table-column>
        <vxe-table-column
            :title="$t('m.Nickname')"
            field="nickname"
            min-width="100"
            show-overflow
        ></vxe-table-column>
        <vxe-table-column
            :title="$t('m.Created_Time')"
            field="gmtCreate"
            min-width="120"
        >
          <template v-slot="{ row }">
            {{ row.gmtCreate | localtime }}
          </template>
        </vxe-table-column>
        <vxe-table-column
            :title="$t('m.User_Type')"
            field="role"
            min-width="70"
        >
          <template v-slot="{ row }">
            {{ getRole(row.roles) | parseRole }}
          </template>
        </vxe-table-column>
        <vxe-table-column
            :title="$t('m.Status')"
            field="status"
            min-width="60"
        >
          <template v-slot="{ row }">
            <el-tag effect="dark" color="#19be6b" v-if="row.status == 0">{{
                $t('m.Normal')
              }}
            </el-tag>
            <el-tag effect="dark" color="#ed3f14" v-else>{{
                $t('m.Disable')
              }}
            </el-tag>
          </template>
        </vxe-table-column>
        <vxe-table-column
            title="积分"
            field="points"
            min-width="80"
        >
          <template v-slot="{ row }">
            <span>{{ row.points || 0 }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column :title="$t('m.Option')" min-width="150">
          <template v-slot="{ row }">
            <div class="button-group">
              <el-tooltip :content="$t('m.Edit_User')" effect="dark" placement="top">
                <el-button icon="el-icon-edit-outline" plain size="mini" type="primary" @click.native="openUserDialog(row,2)">
                </el-button>
              </el-tooltip>
              <el-tooltip :content="$t('m.Delete_User')" effect="dark" placement="top">
                <el-button icon="el-icon-delete-solid" plain size="mini" type="danger" @click.native="deleteUsers([row.uuid])">
                </el-button>
              </el-tooltip>
              <el-tooltip :content="$t('m.Reset_Password')" effect="dark" placement="top">
                <el-button icon="el-icon-unlock" plain size="mini" type="warning" @click.native="resetUserPassword(row.uuid)">
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </vxe-table-column>
      </vxe-table>
      <div class="panel-options">
        <el-pagination
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            class="page"
            layout="prev, pager, next, sizes"
            @current-change="currentChange"
            @size-change="onPageSizeChange"
        >
        </el-pagination>
      </div>
    </el-card>


    <!--创建/编辑用户的对话框-->
    <el-dialog :title="$t('m.User')" :visible.sync="showUserDialog" width="400px">
      <el-form ref="userForm" :model="selectUser" :rules="userFormRules" label-position="left" label-width="100px">
        <el-row :gutter="2">
          <el-col :span="24">
            <el-form-item :label="$t('m.Username')" prop="username" required>
              <el-input v-model="selectUser.username" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="handleType === 1" :span="24">
            <el-form-item :label="$t('m.Password')" prop="password" required>
              <el-input v-model="selectUser.password" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('m.RealName')" prop="realname" required>
              <el-input v-model="selectUser.realname" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('m.Nickname')" prop="nickname" required>
              <el-input v-model="selectUser.nickname" size="small"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('m.Role')" prop="roleIds" required>
              <el-select v-model="selectUser.roleIds" multiple multiple-limit="1" placeholder="请选择角色" size="medium">
                <el-option v-for="role in roleList" :key="role.id" :label="role.role + ' - ' + role.description" :value="role.id"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('m.Join_Group')" prop="groupIds">
              <el-select v-model="selectUser.groupIds" multiple placeholder="请选择班级" size="medium">
                <el-option v-for="group in groupList" :key="group.id" :label="group.name" :value="group.id"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item v-if="false" :label="$t('m.Password')" prop="password" required>
              <el-input v-model="selectUser.password" :placeholder="$t('m.Password')" size="small"
                        type="password"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('m.Title_Name')">
              <el-input v-model="selectUser.titleName" size="small"></el-input>
            </el-form-item>
            <el-form-item :label="$t('m.Title_Color')">
              <el-color-picker
                  v-model="selectUser.titleColor"
              ></el-color-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('m.Status')">
              <el-switch v-model="selectUser.status" :active-text="$t('m.Normal')" :active-value="0"
                         :inactive-text="$t('m.Disable')" :inactive-value="1">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="积分" prop="points">
              <el-input-number v-model="selectUser.points" :min="0" size="small"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="danger" @click.native="showUserDialog = false">{{ $t('m.Cancel') }}</el-button>
        <el-button type="primary" @click.native="handleUser">{{ $t('m.OK') }}</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import api from '@/common/api';
import myMessage from '@/common/message';

export default {
  name: 'user',
  data() {
    const CheckUsernameNotExist = (rule, value, callback) => {
      api.checkUsernameOrEmail(value, undefined).then(
          (res) => {
            if (
                res.data.data.username === true &&
                value != this.selectUser.username
            ) {
              callback(new Error(this.$i18n.t('m.The_username_already_exists')));
            } else {
              callback();
            }
          },
          (_) => callback()
      );
    };
    const CheckEmailNotExist = (rule, value, callback) => {
      api.checkUsernameOrEmail(undefined, value).then(
          (res) => {
            if (res.data.data.email === true && value != this.selectUser.email) {
              callback(new Error(this.$i18n.t('m.The_email_already_exists')));
            } else {
              callback();
            }
          },
          (_) => callback()
      );
    };
    return {
      // 当前页码
      currentPage: 1,
      // 一页显示的用户数
      pageSize: 10,
      // 用户总数
      total: 0,
      // 数据库查询的用户列表
      userList: [],
      // 搜索关键字
      keyword: '',
      queryParam: {
        keyword: '',
        roleIds: [],
        groupIds: []
      },
      // 是否显示用户对话框
      showUserDialog: false,
      onlyAdmin: false,
      roleList: [],
      groupList: [],
      handleType: '',
      // 当前用户model
      selectUser: {
        uuid: '',
        username: '',
        realname: '',
        nickname: '',
        roleId: null,
        roleIds: [1002],
        groupIds: [],
        email: '',
        password: '',
        status: 0,
        titleName: '',
        titleColor: '',
        points: 0,
      },
      userFormRules: {
        username: [
          {required: true, message: 'Username is required', trigger: 'blur'},
          {
            validator: CheckUsernameNotExist,
            trigger: 'blur',
            message: this.$i18n.t('m.The_username_already_exists'),
          },
          {
            max: 255,
            message: this.$i18n.t('m.Username_Check_Max'),
            trigger: 'blur',
          },
        ],
        realname: [
          {
            max: 255,
            trigger: 'blur',
          },
        ],
        email: [
          {
            type: 'email',
            message: this.$i18n.t('m.Email_Check_Format'),
            trigger: 'blur',
          },
          {
            validator: CheckEmailNotExist,
            message: this.$i18n.t('m.The_email_already_exists'),
            trigger: 'blur',
          },
        ],
      },
      loadingTable: false,
      loadingGenerate: false,

      selectedUsers: [],
      formGenerateUser: {
        prefix: '',
        suffix: '',
        number_from: 0,
        number_to: 10,
        password_length: 6,
      },
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getUserPage();
      this.getRoleList();
      this.getGroupList();
    },
    searchData() {
      this.getUserPage();
    },
    resetFilters() {
      this.keyword = '';
      this.onlyAdmin = false;
      this.currentPage = 1;
      this.pageSize = 10;
      this.queryParam.roleIds = [];
      this.queryParam.groupIds = [];
      this.getUserList(1);
    },

    getUserPage() {
      this.loadingTable = true;

      api.admin_getUserPage({currentPage: this.currentPage, limit: this.pageSize}, {
        roleIds: this.queryParam.roleIds,
        groupIds: this.queryParam.groupIds,
        keyword: this.keyword,
        onlyAdmin: this.onlyAdmin
      }).then((res) => {
        this.total = res.data.data.total;
        this.userList = res.data.data.records;
        this.loadingTable = false;
      });
    },
    // 切换页码回调
    currentChange(page) {
      this.currentPage = page;
      this.getUserList(page);
    },
    onPageSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getUserList(this.currentPage);
    },
    // 提交修改用户的信息
    handleUser() {
      this.$refs['userForm'].validate((valid) => {
        if (!valid) {
          myMessage.error(this.$i18n.t('m.Error_Please_check_your_choice'))
        }
        switch (this.handleType) {
          case 1:
            api.admin_createUser(this.selectUser)
                .then((res) => {
                  myMessage.success(this.$i18n.t('m.Create_Successfully'));
                }).finally(() => {
              this.showUserDialog = false;
            })
            break
          case 2:
            api.admin_editUser(this.selectUser)
                .then((res) => {
                  // 更新列表
                  myMessage.success(this.$i18n.t('m.Update_Successfully'));
                  this.getUserList(this.currentPage);
                })
                .finally(() => {
                  this.showUserDialog = false;
                });
            break
        }

      });
    },
    filterByKeyword() {
      this.currentChange(1);
    },


    getRole(roles) {
      return roles[0]['id'];
    },
    // 打开用户对话框
    openUserDialog(row = null, handler) {
      this.handleType = handler;
      this.showUserDialog = true;

      switch (handler) {
        case 1:
          this.selectUser = {};
          break
        case 2:
          this.selectUser = {...row};
          break
      }
    },

    getRoleList() {
      api.admin_listAdminRole().then((res) => {
        this.roleList = res.data.data;
      }).catch((_) => {
      })
    },
    getGroupList() {
      api.admin_listAdminGroup()
          .then(res => {
            this.groupList = res.data.data;
          })
          .catch(err => {
          });
    },
    // 获取用户列表
    getUserList(page) {
      this.getUserPage();
      // this.loadingTable = true;
      // api
      //     .admin_getUserList(page, this.pageSize, this.keyword, this.onlyAdmin)
      //     .then(
      //         (res) => {
      //           this.loadingTable = false;
      //           this.total = res.data.data.total;
      //           this.userList = res.data.data.records;
      //         },
      //         (res) => {
      //           this.loadingTable = false;
      //         }
      //     );
    },
    resetUserPassword(uuid) {
      api.admin_resetPassword(uuid)
          .then(res => {
            myMessage.success(this.$i18n.t('m.Reset_successfully'));
          })
          .catch(err => {
            myMessage.error(this.$i18n.t('m.Reset_failed'));
          });
    },
    deleteUsers(ids) {
      if (!ids) {
        ids = this.selectedUsers;
      }
      if (ids.length > 0) {
        this.$confirm(this.$i18n.t('m.Delete_User_Tips'), 'Tips', {
          confirmButtonText: this.$i18n.t('m.OK'),
          cancelButtonText: this.$i18n.t('m.Cancel'),
          type: 'warning',
        }).then(
            () => {
              api
                  .admin_deleteUsers(ids)
                  .then((res) => {
                    myMessage.success(this.$i18n.$t('m.Delete_successfully'));
                    this.selectedUsers = [];
                    this.getUserList(this.currentPage);
                  })
                  .catch(() => {
                    this.selectedUsers = [];
                    this.getUserList(this.currentPage);
                  });
            },
            () => {
            }
        );
      } else {
        myMessage.warning(
            this.$i18n.t('m.The_number_of_users_selected_cannot_be_empty')
        );
      }
    },
    checCheckboxkMethod({row}) {
      return row.uuid != this.userInfo.uuid;
    },
    // 用户表部分勾选 改变选中的内容
    handleSelectionChange({records}) {
      this.selectedUsers = [];
      for (let num = 0; num < records.length; num++) {
        this.selectedUsers.push(records[num].uuid);
      }
    },
    // 一键全部选中，改变选中的内容列表
    handlechangeAll() {
      let userList = this.$refs.xTable.getCheckboxRecords();
      this.selectedUsers = [];
      for (let num = 0; num < userList.length; num++) {
        this.selectedUsers.push(userList[num].uuid);
      }
    },
  },
  computed: {
    selectedUserIDs() {
      let ids = [];
      for (let user of this.selectedUsers) {
        ids.push(user.id);
      }
      return ids;
    },
    userInfo() {
      return this.$store.getters.userInfo;
    },
  },
  watch: {
    uploadUsersCurrentPage(page) {
      this.uploadUsersPage = this.uploadUsers.slice(
          (page - 1) * this.uploadUsersPageSize,
          page * this.uploadUsersPageSize
      );
    },
  },
};
</script>


<style scoped>
.import-user-icon {
  color: #555555;
  margin-left: 4px;
}

.userPreview {
  padding-left: 10px;
}

::v-deep .el-tag--dark {
  border-color: #fff;
}

::v-deep .el-dialog__body {
  padding-bottom: 0;
}

::v-deep .el-select-item {
  margin-bottom: 10px !important;
}

.notification p {
  margin: 0;
  text-align: left;
}


.handler-row {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.handler-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.handler-btn-group {
  display: flex;
  gap: 10px;
}

.select-container {
  display: flex;
  gap: 20px;
  align-items: center;
}

.select-item {
  width: 200px;
}

el-select,
vxe-input {
  width: 100%;
}

.button-group {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* Aligns buttons to the start of the container */
  gap: 8px; /* Creates space between buttons */
}
</style>
