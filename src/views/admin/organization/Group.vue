<template>
  <div class="view">
    <h1>班级管理</h1>
    <div class="group-container">
      <el-row align="middle" justify="space-between" type="flex">
        <!-- 搜索框 -->
        <el-col :span="6">
          <el-input v-model="searchQuery.keyword" class="search-bar" placeholder="输入班级名进行搜索" size="mini"
                    @change="searchGroup">
            <template #prepend>
              <el-icon name="search" @click="searchGroup"></el-icon>
            </template>
          </el-input>
        </el-col>

        <!-- 搜索框 -->
        <el-col v-if="isAdminRole" :span="6">
          <el-switch v-model="searchQuery.onlyMine" active-text="加入班级" class="search-bar" inactive-text="所有班级"
                     placeholder="输入班级名进行搜索"
                     size="mini" @change="getGroupList">
          </el-switch>
        </el-col>

        <!-- 添加班级按钮 -->
        <el-col :span="6">
          <el-button plain size="mini" type="success" @click="openCreateDialog">
            <i class="el-icon-plus"></i>创建班级
          </el-button>
        </el-col>
      </el-row>
    </div>

    <el-table v-loading="loading" :data="groupList" size="mini" style="width: 100%;">
      <el-table-column label="ID" prop="id" width="80"></el-table-column>
      <el-table-column v-if="false" label="头像" prop="avatar">
        <template slot-scope="scope">
          <img :src="scope.row.avatar" alt="avatar" height="50" width="50"/>
        </template>
      </el-table-column>
      <el-table-column label="班级名称" prop="name"></el-table-column>
      <el-table-column label="简称" prop="shortName"></el-table-column>
      <el-table-column label="简介" prop="brief"></el-table-column>
      <el-table-column label="负责人" prop="owner"></el-table-column>
      <el-table-column label="班级代码" prop="code"></el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <div style="display: flex; justify-content: center; align-items: center; gap: 10px;">
            <el-button icon="el-icon-edit" plain size="small" type="primary" @click="openEditDialog(scope.row)">编辑
            </el-button>
            <el-button icon="el-icon-delete" plain size="small" type="danger" @click="openDeleteDialog(scope.row)">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :page-sizes="[10, 20, 30, 40]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="currentChange">
    </el-pagination>

    <!--表单弹窗-->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
      <el-form ref="form" :model="currentGroup" :rules="rules" class="optimized-form" label-width="100px">

        <!-- 班级名称 和 简称 -->
        <el-row gutter={20}>
          <el-col :span="12">
            <!-- 班级名称 -->
            <el-form-item label="班级名称" prop="name">
              <el-input v-model="currentGroup.name" class="input-width" maxlength="25" placeholder="请输入班级名称"
                        show-word-limit></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 简称 -->
            <el-form-item label="简称" prop="shortName">
              <el-input v-model="currentGroup.shortName" class="input-width" maxlength="10" placeholder="请输入班级简称"
                        show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 班级代码 和 班级简介 -->
        <el-row gutter={20}>
          <el-col :span="12">
            <el-form-item label="班级代码" prop="code">
              <el-input v-model="currentGroup.code" class="input-width" maxlength="6"
                        placeholder="请输入班级代码" show-word-limit></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 班级简介 -->
            <el-form-item label="班级简介" prop="brief">
              <el-input v-model="currentGroup.brief" class="input-width" maxlength="50" placeholder="请输入班级简介"
                        show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 班级负责人  班级介绍 -->
        <el-row gutter={20}>
          <el-col :span="12">
            <el-form-item label="班级负责人" prop="owner">
              <el-input v-model="currentGroup.owner" class="input-width" maxlength="11"
                        placeholder="请输入班级负责人手机号"
                        show-word-limit></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="班级介绍" prop="description">
              <el-input v-model="currentGroup.description" :rows="3" class="input-width" maxlength="1000"
                        placeholder="请输入班级介绍" show-word-limit type="textarea"></el-input>
            </el-form-item>
          </el-col>
        </el-row>


        <!-- 班级权限 和 状态 -->
        <el-row gutter="20">
          <el-col :span="12">
            <!-- 班级权限 -->
            <el-form-item label="班级权限" prop="auth">
              <el-select v-model="currentGroup.auth" class="input-width" disabled placeholder="请选择权限">
                <el-option :value="1" label="公开"></el-option>
                <el-option :value="2" label="保护"></el-option>
                <el-option :value="3" label="私有"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 状态 -->
            <el-form-item label="状态" prop="status">
              <el-select v-model="currentGroup.status" class="input-width" disabled placeholder="请选择状态">
                <el-option :value="0" label="正常"></el-option>
                <el-option :value="1" label="无效"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 班级可见性 -->
        <el-form-item label="可见性" prop="visible">
          <el-switch v-model="currentGroup.visible" disabled></el-switch>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-row align="middle" gutter="16" justify="end">
          <!-- 取消按钮 -->
          <el-button style="width: 100px;" @click="dialogVisible = false">取消</el-button>
          <!-- 确定按钮 -->
          <el-button style="width: 100px;" type="primary" @click="submitForm">确定</el-button>
        </el-row>
      </div>

    </el-dialog>

    <!--删除确认弹窗-->
    <el-dialog :visible.sync="deleteDialogVisible" title="确认删除" width="400px" @close="handleCloseDeleteDialog">
      <span>确定删除此项吗？</span>
      <div slot="footer" class="dialog-footer">
        <el-row align="middle" gutter="16" justify="space-between">
          <!-- 取消按钮 -->
          <el-button style="width: 100px;" @click="deleteDialogVisible = false">取消</el-button>
          <!-- 删除按钮 -->
          <el-button style="width: 100px;" type="danger" @click="deleteItem">删除</el-button>
        </el-row>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import api from "@/common/api";
import myMessage from "@/common/message";
import {mapGetters} from "vuex";

export default {
  name: 'group',
  data() {
    return {
      pageSize: 10,
      total: 0,
      currentPage: 1,
      groupList: [],
      loading: false,
      searchQuery: {
        keyword: '',
        onlyMine: false
      },
      dialogVisible: false,
      dialogTitle: '创建班级',
      deleteDialogVisible: false, // 控制删除对话框的显示与隐藏
      itemToDelete: null, // 当前要删除的项
      rules: {
        name: [
          {required: true, message: '请输入班级名称', trigger: 'blur'},
          {min: 5, max: 25, message: '班级名称长度应在5到25之间', trigger: 'blur'}
        ],
        owner: [
          {required: true, message: '请输入班级负责人手机号', trigger: 'blur'},
        ],
        shortName: [
          {required: true, message: '请输入班级简称', trigger: 'blur'},
          {min: 5, max: 10, message: '班级简称长度应在5到10之间', trigger: 'blur'}
        ],
        brief: [
          {required: true, message: '请输入班级简介', trigger: 'blur'},
          {min: 5, max: 50, message: '班级简介长度应在5到50之间', trigger: 'blur'}
        ],
        description: [
          {required: true, message: '请输入班级介绍', trigger: 'blur'},
          {min: 5, max: 1000, message: '班级描述长度应在5到1000之间', trigger: 'blur'}
        ],
        code: [
          {min: 6, max: 6, message: '班级代码长度应为6', trigger: 'blur'}
        ]
      },
      currentGroup: {
        // 班级ID，唯一标识符。在创建新班级时通常为空。
        id: null,
        // 头像地址，存储班级头像的图片链接。
        avatar: null,
        // 班级名称，字符长度应在5到25之间。
        name: null,
        // 班级简称，字符长度应在5到10之间。
        shortName: null,
        // 班级简介，字符长度应在5到50之间。
        brief: null,
        // 班级描述/介绍，字符长度应在5到1000之间。
        description: null,
        // 班级负责人的用户名。
        owner: null,
        // 班级负责人的UUID。
        uid: null,
        // 班级权限，1表示公开，2表示保护，3表示私有。
        auth: 3,
        // 班级是否可见，布尔值表示。
        visible: true,
        // 班级状态，0表示正常，1表示封禁或删除。
        status: 0,
        // 班级代码，班级的唯一标识符。
        code: null,
        // 班级创建时间，通常在表单中不作修改。
        gmtCreate: null,
        // 班级信息的最后修改时间，通常在表单中不作修改。
        gmtModified: null
      }
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getGroupList();
    },
    // 打开删除对话框并传入要删除的项
    openDeleteDialog(row) {
      this.itemToDelete = row; // 记录当前选中的项
      this.deleteDialogVisible = true; // 显示删除对话框
    },

    // 删除操作
    deleteItem() {
      // 在这里处理删除逻辑，例如通过 API 请求删除数据
      console.log('删除项:', this.itemToDelete);

      // 模拟删除后关闭对话框
      this.deleteDialogVisible = false;
      api.deleteGroup(this.itemToDelete.id).then(res => {
        myMessage.success("删除成功")
        this.getGroupList();
      }).catch(res => {
        myMessage.error("删除失败")
      })
    },

    // 关闭对话框
    handleCloseDeleteDialog() {
      this.itemToDelete = null; // 清空选中的项
    },
    currentChange(page) {
      this.currentPage = page;
      this.getGroupList();
    },
    onPageSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getGroupList();
    },
    getGroupList() {
      this.loading = true;
      api.getGroupList(this.currentPage, this.pageSize, this.searchQuery)
          .then(res => {
            this.groupList = res.data.data.records;
            this.total = res.data.data.total;
            this.loading = false;
          })
          .catch(err => {
            this.loading = false;
          });
    },
    searchGroup() {
      this.currentPage = 1;
      this.getGroupList();
    },
    openCreateDialog() {
      this.dialogTitle = '创建班级';
      // 清空表单
      this.currentGroup = {
        // 班级ID，唯一标识符。在创建新班级时通常为空。
        id: null,
        // 头像地址，存储班级头像的图片链接。
        avatar: null,
        // 班级名称，字符长度应在5到25之间。
        name: null,
        // 班级简称，字符长度应在5到10之间。
        shortName: null,
        // 班级简介，字符长度应在5到50之间。
        brief: null,
        // 班级描述/介绍，字符长度应在5到1000之间。
        description: null,
        // 班级负责人的用户名。
        owner: null,
        // 班级负责人的UUID。
        uid: null,
        // 班级权限，1表示公开，2表示保护，3表示私有。
        auth: 3,
        // 班级是否可见，布尔值表示。
        visible: true,
        // 班级状态，0表示正常，1表示封禁或删除。
        status: 0,
        // 班级代码，班级的唯一标识符。
        code: null,
        // 班级创建时间，通常在表单中不作修改。
        gmtCreate: null,
        // 班级信息的最后修改时间，通常在表单中不作修改。
        gmtModified: null
      };
      this.dialogVisible = true;

    },
    openEditDialog(group) {
      this.dialogTitle = '编辑班级';
      this.currentGroup = {...group}; // 克隆对象，避免直接引用
      this.dialogVisible = true;
    },

    submitForm() {
      // 触发表单验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log('表单验证成功');

          // 根据是否存在 currentGroup.id 确定操作类型（编辑或创建）
          const action = this.currentGroup.id
              ? api.updateGroup(this.currentGroup)
              : api.addGroup(this.currentGroup);

          // 执行对应的 API 操作
          action.then(() => {
            this.dialogVisible = false; // 隐藏对话框
            this.getGroupList();       // 刷新分组列表
          }).catch((error) => {
            console.error('操作失败:', error); // 捕获并处理错误
          });
        } else {
          console.log('表单验证失败');
        }
      });
    },

  },
  computed: {
    ...mapGetters(['isAdminRole', 'isGroupAdmin']),
  },
}
;
</script>

<style scoped>
.group-container {
  width: 100%;
  padding: 10px;
  border: 1px solid #dcdfe6; /* 添加边框 */
  border-radius: 5px; /* 圆角边框 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  margin: 20px auto; /* 居中显示 */
}

.dialog-footer {
  text-align: center;
  margin: 20px auto;
  padding: 10px 20px;
}

.search-bar {
  width: 100%; /* 确保输入框占满整个容器 */
}

.el-input {
  max-width: 500px; /* 限制输入框最大宽度 */
}

.el-button {
  width: 100%; /* 可以根据需要调整宽度 */
}


</style>
