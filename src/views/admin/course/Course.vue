<template>
  <div class="course-plan-container">
    <!-- 搜索区域 -->
    <el-form :inline="true" class="search-form">
      <el-form-item label="课程名称2">
        <el-input v-model="queryParams.name" placeholder="请输入名称"></el-input>
      </el-form-item>
      <el-form-item label="选择班级">
        <GroupSelect v-model="queryParams.gidList" :multiple="true"/>
      </el-form-item>
      <el-form-item label="近期课程">
        <el-select v-model="queryParams.latelyDay" clearable placeholder="请选择天数">
          <el-option
              v-for="n in 7"
              :key="n"
              :label="`近${n}天课程`"
              :value="n"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 操作按钮 -->
    <div class="operation-btns">
      <el-button icon="el-icon-plus" type="primary" @click="handleAdd">新增课程计划</el-button>
    </div>
    <!-- 数据表格 -->
    <el-table
        :cell-style="{ textAlign: 'center' }"
        :data="courseList"
        :header-cell-style="{ textAlign: 'center' }"
        border
        style="width: 100%">

      <el-table-column label="课程名称" prop="name"></el-table-column>
      <el-table-column label="班级" prop="groupName"></el-table-column>
      <el-table-column label="课程链接">
        <template slot-scope="scope">
          <el-link
              style="display: flex; justify-content: center"
              type="primary"
              @click="copyUrl(scope.row.url)">
            {{ scope.row.url }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column label="上课时间" width="250">
        <template slot-scope="scope">
          <div class="time-range">
            <el-tag type="info">{{ formatDate(scope.row.startTime) }}</el-tag>
            <el-tag type="primary">
              <el-text type="primary">{{ formatTime(scope.row.startTime) }}</el-text>
              <span class="separator">-</span>
              <el-text type="primary">{{ formatTime(scope.row.endTime) }}</el-text>
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-tag
              :type="getStatusType(scope.row)"
              style="margin: 0 auto">
            {{ getStatusText(scope.row) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300">
        <template slot-scope="scope">
          <div class="action-buttons">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
            <el-tooltip v-if="false" content="发送短信提醒学生即将上课" placement="top">
              <el-button
                  icon="el-icon-message"
                  size="mini"
                  type="warning"
                  @click="handleSendReminder(scope.row)">
                提醒
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
        :current-page="pagination.current"
        :page-size="pagination.size"
        :page-sizes="[10, 20, 50]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange">
    </el-pagination>
    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="isEditing ? '编辑课程计划' : '新增课程计划'" :visible.sync="dialogVisible" width="600px">
      <el-form ref="editForm" :model="editForm" :rules="rules" label-width="100px">
        <el-form-item label="课程名称" prop="name">
          <el-input v-model="editForm.name"></el-input>
        </el-form-item>
        <el-form-item label="课程内容" prop="content">
          <el-input v-model="editForm.content" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="课程链接" prop="url">
          <el-input v-model="editForm.url"></el-input>
        </el-form-item>
        <el-form-item label="上课班级" prop="gid">
          <GroupSelect v-model="editForm.gid" :data-list="groupList" :multiple="false"/>
        </el-form-item>
        <el-form-item label="上课时间" prop="startTime">
          <el-date-picker
              v-model="editForm.startTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="下课时间" prop="endTime">
          <el-date-picker
              v-model="editForm.endTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>


  </div>
</template>
<script>
import api from '@/common/api';
import GroupSelect from "@/components/admin/GroupSelect.vue";
import myMessage from "@/common/message";

export default {
  components: {GroupSelect},
  data() {
    return {
      // 查询参数
      queryParams: {
        name: '',
        gidList: null,
        latelyDay: null,
      },

      // 分页参数
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },

      // 表格数据
      courseList: [],
      groupList: [],
      // 弹窗相关
      dialogVisible: false,
      isEditing: false,
      // 在data中添加
      detailDialogVisible: false,
      detailData: {},
      editForm: {
        id: null,
        name: '',
        gid: null,
        content: '',
        url: '',
        startTime: null,
        endTime: null
      },

      // 表单验证规则
      rules: {
        name: [{required: true, message: '请输入课程名称', trigger: 'blur'}],
        url: [{required: true, message: '请输入课程链接', trigger: 'blur'}],
        startTime: [{required: true, message: '请输入上课时间', trigger: 'blur'}],
        endTime: [{required: true, message: '请输入下课时间', trigger: 'blur'}],
      }
    }
  },

  mounted() {
    this.getCoursePlans()
    this.loadGroupList();
  },

  methods: {
    // 获取课程列表
    async getCoursePlans() {
      try {
        const res = await api.getCoursePlanPage(
            {
              currentPage: this.pagination.current,
              limit: this.pagination.size
            },
            this.queryParams
        )

        if (res.data.status === 200) {
          this.courseList = res.data.data.records
          this.pagination.total = res.data.data.total
        }
        myMessage.success('查询完毕')

      } catch (error) {
        myMessage.error('获取数据失败')
      }
    },
    // 添加提醒方法
    async handleSendReminder(row) {
      try {
        const res = await api.sendCourseReminder({id: row.id});
        if (res.data.status === 200) {
          myMessage.success('提醒已发送');
        }
      } catch (error) {
        myMessage.error('发送失败');
      }
    },
    loadGroupList() {
      api.admin_listAdminGroup()
          .then(res => {
            this.groupList = res.data.data;
          })
          .catch(err => {
            console.error('Error fetching group list:', err);
          });
    },

    // 搜索
    search() {
      this.pagination.current = 1
      this.getCoursePlans()
    },

    // 重置查询
    resetQuery() {
      this.queryParams = {
        name: '',
        gid: null
      }
      this.getCoursePlans()
    },

    // 分页变化
    handlePageChange(current) {
      this.pagination.current = current
      this.getCoursePlans()
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.size = size
      this.getCoursePlans()
    },

    // 新增
    handleAdd() {
      this.isEditing = false
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.editForm.resetFields()
      })
    },

    // 编辑
    handleEdit(row) {
      this.isEditing = true
      this.dialogVisible = true
      this.$nextTick(() => {
        this.editForm = {
          id: row.id,
          name: row.name,
          content: row.content,
          gid: row.gid,
          url: row.url,
          startTime: row.startTime,
          endTime: row.endTime,
        }
      })
    },

    // 提交表单
    submitForm() {
      this.$refs.editForm.validate(async valid => {
        if (valid) {
          const formData = {
            ...this.editForm,
          }

          try {
            let res
            if (this.isEditing) {
              res = await api.updateCoursePlan(formData)
            } else {
              res = await api.addCoursePlan(formData)
            }

            if (res.data.status === 200) {
              myMessage.success(this.isEditing ? '修改成功' : '新增成功')
              this.dialogVisible = false
              this.getCoursePlans()
            }
          } catch (error) {
            console.log(error)
            myMessage.error(error.data?.message || '操作失败')
          }
        }
      })
    },

    // 删除
    handleDelete(id) {
      this.$confirm('确定要删除该课程计划吗？', '提示', {
        type: 'warning'
      }).then(async () => {
        try {
          const res = await api.deleteCoursePlan(id)
          if (res.data.status === 200) {
            myMessage.success('删除成功')
            this.getCoursePlans()
          }
        } catch (error) {
          myMessage.error(error.data?.message || '删除失败')
        }
      })
    },
    formatDate(fullTime) {
      if (!fullTime) return '';
      // 假设时间格式为 "YYYY-MM-DD HH:mm"
      return fullTime.split(' ')[0]; // 提取 HH:mm 格式
    },
    formatTime(fullTime) {
      if (!fullTime) return '';
      // 假设时间格式为 "YYYY-MM-DD HH:mm"
      return fullTime.split(' ')[1].substr(0, 5); // 提取 HH:mm 格式
    },
    // 添加状态计算方法
    getStatusText(row) {
      const now = new Date().getTime();
      const start = new Date(row.startTime).getTime();
      const end = new Date(row.endTime).getTime();

      if (now < start) return '未上课';
      if (now > end) return '已下课';
      return '上课中';
    },
    getStatusType(row) {
      const status = this.getStatusText(row);
      return {
        '未上课': 'info',
        '上课中': 'success',
        '已下课': 'danger'
      }[status];
    },

    // 添加复制方法
    copyUrl(url) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(url)
            .then(() => myMessage.success('复制成功'))
            .catch(() => this.fallbackCopy(url));
      } else {
        this.fallbackCopy(url);
      }
    },
    fallbackCopy(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        myMessage.success('复制成功');
      } catch (err) {
        myMessage.error('复制失败，请手动复制');
      }
      document.body.removeChild(textArea);
    }
  }
}
</script>
<style scoped>
.course-plan-container {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.operation-btns {
  margin-bottom: 20px;
}

/* 添加样式 */
.time-range {
  display: flex;
  align-items: center;
  justify-content: center;
}

.separator {
  margin: 0 5px;
  color: #606266;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

/* 调整按钮最小宽度 */
.el-button--mini {
  min-width: 60px;
}

</style>
