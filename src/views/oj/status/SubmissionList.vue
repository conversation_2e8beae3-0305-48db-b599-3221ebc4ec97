<template>
  <div>
    <submit-list :type="SubmitListType.All"></submit-list>
  </div>
</template>

<script>
import { SubmitListType } from '@/common/submitListType';
import SubmitList from '@/components/tuc/SubmitList.vue';
import {mapActions, mapGetters} from "vuex";
import api from "@/common/api";
import {CONTEST_STATUS, JUDGE_STATUS, JUDGE_STATUS_RESERVE, RULE_TYPE,} from "@/common/constants";
import utils from "@/common/utils";
import Pagination from "@/components/oj/common/Pagination";
import myMessage from "@/common/message";
import "element-ui/lib/theme-chalk/display.css";

export default {
  name: "submissionList",
  components: {
    SubmitList,
    Pagination,
  },
  data() {
    return {
      formFilter: {
        onlyMine: false,
        status: "",
        username: "",
        problemID: "",
      },
      loadingTable: false,
      submissions: [],
      needCheckSubmitIds: {}, // 当前状态为6和7的提交记录Id 需要重新检查更新
      total: 30,
      limit: 15,
      currentPage: 1,
      contestID: null,
      groupID: null,
      routeName: "",
      checkStatusNum: 0,
      JUDGE_STATUS: "",
      JUDGE_STATUS_LIST: "",
      CHANGE_JUDGE_STATUS_LIST: "",
      autoCheckOpen: false,
      JUDGE_STATUS_RESERVE: {},
      CONTEST_STATUS: {},
      RULE_TYPE: {},
      hideManuallyJugdeTooltip:false,
      changeJudgeStatusDialogVisible: false,
      changeJudgeStatusLoading: false,
      changeJudgeStatus: {
        submitId: null,
        status: null,
        score: null,
      },
    };
  },
  created() {
    this.init();
  },
  mounted() {
    this.JUDGE_STATUS = Object.assign({}, JUDGE_STATUS);
    this.JUDGE_STATUS_LIST = Object.assign({}, JUDGE_STATUS);
    this.JUDGE_STATUS_RESERVE = Object.assign({}, JUDGE_STATUS_RESERVE);
    this.CONTEST_STATUS = Object.assign({}, CONTEST_STATUS);
    this.RULE_TYPE = Object.assign({}, RULE_TYPE);
    // 去除下拉框选择中的Not Submitted,Submitted Unknown Result 三种状态
    delete this.JUDGE_STATUS_LIST["9"];
    delete this.JUDGE_STATUS_LIST["-5"];
    delete this.JUDGE_STATUS_LIST["-10"];

    // 再次去除Cancelled,Compile Error,Compiling,Judging,Submitting
    this.CHANGE_JUDGE_STATUS_LIST = Object.assign({}, this.JUDGE_STATUS_LIST);
    delete this.CHANGE_JUDGE_STATUS_LIST["-4"];
    delete this.CHANGE_JUDGE_STATUS_LIST["-2"];
    delete this.CHANGE_JUDGE_STATUS_LIST["5"];
    delete this.CHANGE_JUDGE_STATUS_LIST["6"];
    delete this.CHANGE_JUDGE_STATUS_LIST["7"];
    this.getData();
  },
  methods: {
    init() {
      this.checkStatusNum = 0;
      this.contestID = this.$route.params.contestID;
      this.groupID = this.$route.params.groupID;
      let query = this.$route.query;
      this.formFilter.problemID = query.problemID;
      this.formFilter.username = query.username || "";
      this.formFilter.onlyMine = query.onlyMine + "" == "true" ? true : false; // 统一换成字符串判断
      this.formFilter.status = query.status;
      this.formFilter.completeProblemID = query.completeProblemID || false;
      if (this.formFilter.onlyMine) {
        // 当前为搜索自己的提交 那么不可搜索别人的提交
        this.formFilter.username = "";
      }
      this.currentPage = parseInt(query.currentPage) || 1;
      if (this.currentPage < 1) {
        this.currentPage = 1;
      }
      this.limit = parseInt(query.limit) || 15;
      this.routeName = this.$route.name;
    },

    getData() {
      this.getSubmissions();
    },

    buildQuery() {
      return {
        onlyMine: this.formFilter.onlyMine,
        status: this.formFilter.status,
        username: this.formFilter.username,
        problemID: this.formFilter.problemID,
        currentPage: this.currentPage,
        limit: this.limit,
        completeProblemID: this.formFilter.completeProblemID,
        gid: this.groupID,
      };
    },

    submissionTimeFormat(time) {
      return utils.submissionTimeFormat(time);
    },

    submissionMemoryFormat(memory) {
      return utils.submissionMemoryFormat(memory);
    },

    submissionLengthFormat(length) {
      return utils.submissionLengthFormat(length);
    },
    reSubmit(row) {
      api.reSubmitRemoteJudge(row.submitId).then((res) => {
        let xTable = this.$refs.xTable;
        // 重新提交开始，需要将该提交的部分参数初始化
        row.status = res.data.data.status;
        row.time = res.data.data.time;
        row.memory = res.data.data.memory;
        row.errorMessage = res.data.data.errorMessage;
        row.judger = res.data.data.judger;
        row.isManual = false;
        // 重新加载该行数据到view
        xTable.reloadRow(row, null, null);

        this.submissions[row.index] = res.data.data;
        myMessage.success(this.$i18n.t("m.Resubmitted_Successfully"));

        // 加入待重判列表
        this.needCheckSubmitIds[row.submitId] = row.index;

        this.checkStatusNum = 0;
        if (!this.autoCheckOpen) {
          // 如果当前未开启自动检查提交状态的定时任务，则开启
          this.checkSubmissionsStatus();
        }
      });
    },
    getSubmissions() {
      let params = this.buildQuery();
      params.contestID = this.contestID;
      params.gid = this.groupID;
      if (this.contestID) {
        if (this.contestStatus == CONTEST_STATUS.SCHEDULED) {
          params.beforeContestSubmit = true;
        } else {
          params.beforeContestSubmit = false;
        }
        params.containsEnd = true;
      }
      if (this.formFilter.onlyMine) {
        // 需要判断是否为登陆状态
        if (this.isAuthenticated) {
          params.username = ""; // 如果是搜索当前用户的提交记录，那么用户名搜索应该无效
          this.formFilter.username = "";
        } else {
          this.formFilter.onlyMine = false;
          myMessage.error(this.$i18n.t("m.Please_login_first"));
          return;
        }
      }

      this.loadingTable = true;
      this.submissions = [];
      this.needCheckSubmitIds = {};
      let func = this.contestID
        ? "getContestSubmissionList"
        : "getSubmissionList";
      api[func](this.limit, utils.filterEmptyValue(params))
        .then((res) => {
          let data = res.data.data;
          let index = 0;
          for (let v of data.records) {
            if (
              v.status == JUDGE_STATUS_RESERVE["Pending"] ||
              v.status == JUDGE_STATUS_RESERVE["Compiling"] ||
              v.status == JUDGE_STATUS_RESERVE["Judging"]
            ) {
              this.needCheckSubmitIds[v.submitId] = index;
            }
            v.loading = false;
            v.index = index;
            index += 1;
          }
          this.loadingTable = false;
          this.submissions = data.records;
          this.total = data.total;
          if (Object.keys(this.needCheckSubmitIds).length > 0) {
            this.checkSubmissionsStatus();
          }
        })
        .catch(() => {
          this.loadingTable = false;
        });
    },
    // 对当前提交列表 状态为Pending（6）和Judging（7）的提交记录每2秒查询一下最新结果
    checkSubmissionsStatus() {
      // 使用setTimeout避免一些问题
      if (this.refreshStatus) {
        // 如果之前的提交状态检查还没有停止,则停止,否则将会失去timeout的引用造成无限请求
        clearTimeout(this.refreshStatus);
        this.autoCheckOpen = false;
      }
      const checkStatus = () => {
        let submitIds = this.needCheckSubmitIds;
        let func = this.contestID
          ? "checkContestSubmissonsStatus"
          : "checkSubmissonsStatus";
        api[func](Object.keys(submitIds), this.contestID).then(
          (res) => {
            let result = res.data.data;
            if (!this.$refs.xTable) {
              // 避免请求一半退出view保错
              return;
            }
            let viewData = this.$refs.xTable.getTableData().tableData;
            for (let key in submitIds) {
              let submitId = parseInt(key);
              if (!result[submitId]) {
                continue;
              }
              // 更新数据列表
              this.submissions[submitIds[key]] = result[submitId];
              // 更新view中的结果，f分数，耗时，空间消耗，判题机ip
              viewData[submitIds[key]].status = result[submitId].status;
              viewData[submitIds[key]].score = result[submitId].score;
              viewData[submitIds[key]].time = result[submitId].time;
              viewData[submitIds[key]].memory = result[submitId].memory;
              viewData[submitIds[key]].judger = result[submitId].judger;
              viewData[submitIds[key]].isManual = result[submitId].isManual;
              // 重新加载这行数据到view中
              this.$refs.xTable.reloadRow(viewData[submitIds[key]], null, null);

              if (
                result[submitId].status != JUDGE_STATUS_RESERVE["Pending"] &&
                result[submitId].status != JUDGE_STATUS_RESERVE["Compiling"] &&
                result[submitId].status != JUDGE_STATUS_RESERVE["Judging"]
              ) {
                delete this.needCheckSubmitIds[key];
              }
            }
            // 当前提交列表的提交都判题结束或者检查结果600s（2s*300）还没判题结束，为了避免无用请求加重服务器负担，直接停止检查的请求。
            if (
              Object.keys(this.needCheckSubmitIds).length == 0 ||
              this.checkStatusNum == 300
            ) {
              clearTimeout(this.refreshStatus);
              this.autoCheckOpen = false;
            } else {
              this.checkStatusNum += 1;
              this.refreshStatus = setTimeout(checkStatus, 2000);
            }
          },
          (res) => {
            clearTimeout(this.refreshStatus);
            this.autoCheckOpen = false;
          }
        );
      };
      // 设置每2秒检查一下提交结果
      this.checkStatusNum += 1;
      this.refreshStatus = setTimeout(checkStatus, 2000);
      this.autoCheckOpen = true;
    },
    onPageSizeChange(pageSize) {
      this.limit = pageSize;
      this.changeRoute();
    },
    // 改变route， 通过监听route变化请求数据，这样可以产生route history， 用户返回时就会保存之前的状态
    changeRoute() {
      let query = this.buildQuery();
      query.contestID = this.contestID;
      let queryParams = utils.filterEmptyValue(query);
      // 判断新路径请求参数与当前路径请求的参数是否一致，避免重复访问路由报错
      let equal = true;
      for (let key in queryParams) {
        if (queryParams[key] != this.$route.query[key]) {
          equal = false;
          break;
        }
      }
      if (equal) {
        // 判断请求参数的长短
        if (
          Object.keys(queryParams).length !=
          Object.keys(this.$route.query).length
        ) {
          equal = false;
        }
      }

      if (!equal) {
        // 避免重复同个路径请求导致报错
        let routeName = queryParams.contestID
          ? "ContestSubmissionList"
          : this.groupID
          ? "GroupSubmissionList"
          : "SubmissionList";
        this.$router.push({
          name: routeName,
          query: queryParams,
        });
      }
    },
    goRoute(route) {
      this.$router.push(route);
    },
    goUserHome(username, uid) {
      this.$router.push({
        path: "/organization-home",
        query: {uid, username},
      });
    },
    handleStatusChange(status) {
      if (status == "All") {
        this.formFilter.status = "";
      } else {
        this.formFilter.status = status;
      }
      this.currentPage = 1;
      this.changeRoute();
    },
    handleQueryChange(searchParam) {
      if (searchParam == "probemID") {
        this.formFilter.completeProblemID = false; // 并非走完全检索displayID了
      }
      this.currentPage = 1;
      this.changeRoute();
    },
    handleRejudge(row) {
      this.submissions[row.index].loading = true;
      api.submissionRejudge(row.submitId).then(
        (res) => {
          let xTable = this.$refs.xTable;
          // 重判开始，需要将该提交的部分参数初始化
          row.status = res.data.data.status;
          row.score = null;
          row.time = res.data.data.time;
          row.memory = res.data.data.memory;
          row.errorMessage = res.data.data.errorMessage;
          row.judger = res.data.data.judger;
          row.loading = false;
          row.isManual = false;
          // 重新加载该行数据到view
          xTable.reloadRow(row, null, null);

          this.submissions[row.index] = res.data.data;
          this.submissions[row.index].loading = false;
          myMessage.success(this.$i18n.t("m.Rejudge_successfully"));

          // 加入待重判列表
          this.needCheckSubmitIds[row.submitId] = row.index;
          this.checkStatusNum = 0;
          if (!this.autoCheckOpen) {
            // 如果当前未开启自动检查提交状态的定时任务，则开启
            this.checkSubmissionsStatus();
          }
        },
        () => {
          this.submissions[row.index].loading = false;
        }
      );
    },
    handleOnlyMine() {
      if (this.formFilter.onlyMine) {
        // 需要判断是否为登陆状态
        if (this.isAuthenticated) {
          this.formFilter.username = "";
        } else {
          this.formFilter.onlyMine = false;
          myMessage.error(this.$i18n.t("m.Please_login_first"));
          return;
        }
      }
      this.currentPage = 1;
      this.changeRoute();
    },
    ...mapActions(["changeModalStatus"]),

    showSubmitDetail(row) {
      if (this.contestID != null) {
        // 比赛提交详情
        this.$router.push({
          name: "ContestSubmissionDetails",
          params: {
            contestID: this.contestID,
            problemID: row.displayId,
            submitID: row.submitId,
          },
        });
      } else if (this.groupID != null) {
        this.$router.push({
          name: "GroupSubmissionDetails",
          params: { submitID: row.submitId },
        });
      } else {
        this.$router.push({
          name: "SubmissionDetails",
          params: { submitID: row.submitId },
        });
      }
    },
    getProblemUri(pid) {
      if (this.contestID) {
        this.$router.push({
          name: "ContestProblemDetails",
          params: {
            contestID: this.$route.params.contestID,
            problemID: pid,
          },
        });
      } else if (this.groupID) {
        this.$router.push({
          name: "GroupProblemDetails",
          params: {
            problemID: pid,
            groupID: this.groupID,
          },
        });
      } else {
        this.$router.push({
          name: "ProblemDetails",
          params: {
            problemID: pid,
          },
        });
      }
    },
    getStatusColor(status) {
      return "el-tag el-tag--medium status-" + JUDGE_STATUS[status]["color"];
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.username == this.userInfo.username && this.isAuthenticated) {
        return "own-submit-row";
      }
    },
    disabledManualJudge(status){
      return !this.isSuperAdmin || status == JUDGE_STATUS_RESERVE['Judging']
          || status == JUDGE_STATUS_RESERVE['Compiling']
          || status == JUDGE_STATUS_RESERVE['ce']
    },
    openChangeJudgeStatusDialog(judge) {
      this.changeJudgeStatus = {
        submitId: judge.submitId,
        score: judge.score,
        status: judge.status,
        index: judge.index,
      };
      this.changeJudgeStatusDialogVisible = true;
    },
    cancelJudge(row) {
      this.$confirm(this.$i18n.t("m.Cancel_Judge_Tips"), "Run ID："+row.submitId, {
        type: "warning",
      }).then(
        () => {
          api
            .admin_cancelJudge(row.submitId)
            .then((res) => {
              myMessage.success(this.$i18n.t("m.Cancel_Successfully"));
              let data = res.data.data;
              row.status = data.status;
              row.score = data.score;
              row.oiRankScore = data.oiRankScore;
              row.judger = data.judger;
              row.loading = false;
              row.isManual = true;
              // 重新加载这行数据到view中
              this.$refs.xTable.reloadRow(row, null, null);
            })
            .catch(() => {});
        },
        () => {}
      );
    },
    manualJudge() {
      this.changeJudgeStatusLoading = true;
      api
        .admin_manualJudge(
          this.changeJudgeStatus.submitId,
          this.changeJudgeStatus.status,
          this.changeJudgeStatus.score
        )
        .then(
          (res) => {
            myMessage.success(this.$i18n.t("m.Update_Successfully"));
            let data = res.data.data;
            // 更新数据列表
            this.submissions[this.changeJudgeStatus.index].status = data.status;
            this.submissions[this.changeJudgeStatus.index].score = data.score;
            this.submissions[this.changeJudgeStatus.index].oiRankScore =
              data.oiRankScore;
            this.submissions[this.changeJudgeStatus.index].judger = data.judger;
            this.submissions[this.changeJudgeStatus.index].isManual = true;
            // 更新view中的结果，f分数，耗时，空间消耗，判题机ip
            let viewData = this.$refs.xTable.getTableData().tableData;
            let row = viewData[this.changeJudgeStatus.index];
            row.status = data.status;
            row.score = data.score;
            row.oiRankScore = data.oiRankScore;
            row.judger = data.judger;
            row.isManual = true;
            // 重新加载这行数据到view中
            this.$refs.xTable.reloadRow(row, null, null);
            this.changeJudgeStatusLoading = false;
            this.changeJudgeStatusDialogVisible = false;
          },
          (err) => {
            this.changeJudgeStatusLoading = false;
          }
        )
        .catch(() => {});
    },
  },
  computed: {
    SubmitListType() {
      return SubmitListType;
    },
    ...mapGetters([
      "isAuthenticated",
      "userInfo",
      "isSuperAdmin",
      "isAdminRole",
      "contestRuleType",
      "contestStatus",
      "ContestRealTimePermission",
    ]),
    title() {
      if (!this.contestID) {
        return "Status";
      } else if (this.problemID) {
        return "Problem Submissions";
      } else {
        return "Submissions";
      }
    },
    status() {
      return this.formFilter.status === ""
        ? this.$i18n.t("m.Status")
        : JUDGE_STATUS[this.formFilter.status]
        ? JUDGE_STATUS[this.formFilter.status].name
        : this.$i18n.t("m.Status");
    },
    rejudgeColumnVisible() {
      return this.isSuperAdmin;
    },
    scoreColumnVisible() {
      return (
        (this.contestID && this.contestRuleType == this.RULE_TYPE.OI) ||
        !this.contestID
      );
    },
  },
  watch: {
    $route(newVal, oldVal) {
      if (newVal !== oldVal) {
        if (this.autoCheckOpen) {
          clearInterval(this.refreshStatus);
        }
        this.init();
        this.getData();
      }
    },
    isAuthenticated() {
      this.init();
      this.getData();
    },
  },
  beforeRouteLeave(to, from, next) {
    // 防止切换组件后仍然不断请求
    clearInterval(this.refreshStatus);
    next();
  },
};
</script>

<style scoped>
@media only screen and (max-width: 767px) {
  .search {
    margin-top: 20px;
  }
}

.flex-container #main {
  flex: auto;
}
.flex-container .filter {
  margin-right: -10px;
}
.flex-container #contest-menu {
  flex: none;
  width: 210px;
}
::v-deep .el-card__header {
  border-bottom: 0px;
  padding-bottom: 0px;
  text-align: center;
}

::v-deep .el-dialog {
  border-radius: 6px !important;
  text-align: center;
}
::v-deep .el-switch {
  padding-top: 6px;
}
@media only screen and (min-width: 768px) and (max-width: 992px) {
  .el-col-sm-12 {
    padding-top: 10px;
  }
}
@media screen and (min-width: 1350px) {
  ::v-deep .vxe-table--body-wrapper {
    overflow-x: hidden !important;
  }
}
::v-deep .vxe-table .vxe-cell{
  padding: 0 !important;
}
::v-deep .el-dialog--center .el-dialog__body {
  padding-bottom: 0px !important;
}
.manual-judge-title {
  text-align: center;
  font-weight: bolder;
  font-size: 15px;
  margin-bottom: 5px;
}
</style>
