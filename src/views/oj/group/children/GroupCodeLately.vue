<template>
  <el-card>
    <!-- 新增Tab栏 -->
    <el-tabs
        v-model="activeType"
        @tab-click="handleTabChange"
    >
      <el-tab-pane
          v-for="item in codeTypes"
          :key="item.code"
          :label="item.description"
          :name="item.code"
      />
    </el-tabs>

    <div class="filter-row"/>
    <code-table
        :button-text="'推送代码'"
        :code-list="latelyCodeList"
        @other-operation="openPushCodeDialog"
    />

    <!-- 弹窗用于推送代码 -->
    <transition name="dialog-fly">
      <el-dialog
          v-if="dialogVisible"
          :modal-append-to-body="false"
          :title="`推送代码给 【${currentRowData?.realname || ''}】`"
          :visible.sync="dialogVisible"
          width="70%"
      >
        <div class="dialog-header">
          <div class="dialog-header-buttons">
            <el-button type="primary" @click="confirmPushCode">确认推送</el-button>
            <el-button @click="dialogVisible = false">取消</el-button>
          </div>
          <span class="push-target">推送对象：<strong>{{ currentRowData?.realname || '未指定' }}</strong></span>
        </div>

        <tu-code-mirror ref="codeEditor"/>
      </el-dialog>
    </transition>
  </el-card>
</template>

<script>
import api from '@/common/api';
import CodeTable from '@/components/tuc/CodeTable.vue';
import TuCodeMirror from '@/components/tuc/TuCodeMirror.vue';
import myMessage from '@/common/message';
import {CodeType} from '@/views/tuco/js/enums';

export default {
  name: 'GroupCodeLately',
  components: {
    TuCodeMirror,
    CodeTable,
  },
  data() {
    return {
      query: {
        page: 1,
        limit: 30,
        searchUser: null,
        gid: null,
        type: 0,
      },
      codeTypes: Object.values(CodeType), // 转换枚举为数组
      activeType: CodeType.Tuc.code,      // 当前选中的类型
      gid: null,
      latelyCodeList: [],
      dialogVisible: false,
      currentRowData: null,
      intervalId: null,
    };
  },
  watch: {
    $route: {
      handler: 'init',
      immediate: true,
    },
  },
  mounted() {
    this.setAutoRefresh();
  },
  beforeDestroy() {
    this.clearAutoRefresh();
  },
  methods: {
    init() {
      const route = this.$route.query;
      this.query.searchUser = route.searchUser || '';
      this.query.gid = this.$route.params.groupID;
      this.gid = this.$route.params.groupID;
      this.query.page = route.page || 1;
      this.query.limit = route.limit || 30;
      this.query.type = route.type || 0;
      this.getList();
    },

    handleTabChange() {
      this.init();
    },

    setAutoRefresh() {
      this.intervalId = setInterval(() => {
        this.getList();
      }, 3000);
    },

    clearAutoRefresh() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    },

    async getList() {
      try {
        const res = await api.listLatelyByGid({
          gid: this.gid,
          type: this.activeType, // 使用当前选中的类型
        });
        this.latelyCodeList = res.data.data;
      } catch (err) {
        console.error('获取最新代码失败', err);
        myMessage.error('数据加载失败');
      }
    },

    openPushCodeDialog(rowData) {
      this.currentRowData = rowData;
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.codeEditor.setCode(rowData.code);
      });
    },

    async confirmPushCode() {
      if (!this.currentRowData) return;

      try {
        const pushedCode = this.$refs.codeEditor.getCode();
        await api.pushCode({
          id: this.currentRowData.id,
          uid: this.currentRowData.uid,
          pushCode: pushedCode,
        });
        myMessage.success('代码推送成功！');
        this.dialogVisible = false;
      } catch (err) {
        console.error('代码推送失败', err);
        myMessage.error(`推送失败：${err.message}`);
      }
    },
  },
};
</script>

<style scoped>
.type-tabs {
  margin-bottom: 20px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dialog-header-buttons {
  display: flex;
  gap: 10px;
}

.push-target {
  font-size: 14px;
  color: #333;
}

.push-target strong {
  color: #409eff;
  font-weight: bold;
}
</style>
