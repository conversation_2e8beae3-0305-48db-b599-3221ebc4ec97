<template>
  <el-card>
    <div class="filter-row">
      <el-row>
        <el-col :md="3" :xs="24">
          <span class="title">{{ $t('m.Group_Course') }}</span>
        </el-col>
        <el-col :md="21" :xs="24">
          <el-form :inline="true" class="search-form">
            <el-form-item label="课程名称">
              <el-input v-model="queryParams.name" placeholder="请输入名称"></el-input>
            </el-form-item>
            <el-form-item label="近期课程">
              <el-select v-model="queryParams.latelyDay" clearable placeholder="请选择天数">
                <el-option
                    v-for="n in 7"
                    :key="n"
                    :label="`近${n}天课程`"
                    :value="n"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    
    <!-- 数据表格 -->
    <vxe-table
        border="inner"
        stripe
        auto-resize
        highlight-hover-row
        :data="courseList"
        :loading="loading"
        align="center"
    >
      <vxe-table-column field="rank" :title="$t('m.Number')" min-width="60" show-overflow></vxe-table-column>
      <vxe-table-column field="name" :title="课程名称" min-width="200" align="center"></vxe-table-column>
      <vxe-table-column field="content" :title="课程内容" min-width="200" align="center" show-overflow></vxe-table-column>
      <vxe-table-column field="url" :title="课程链接" min-width="150" align="center">
        <template v-slot="{ row }">
          <el-link
              type="primary"
              @click="copyUrl(row.url)">
            {{ row.url }}
          </el-link>
        </template>
      </vxe-table-column>
      <vxe-table-column :title="上课时间" min-width="250" align="center">
        <template v-slot="{ row }">
          <div class="time-range">
            <el-tag type="info">{{ formatDate(row.startTime) }}</el-tag>
            <el-tag type="primary">
              {{ formatTime(row.startTime) }} - {{ formatTime(row.endTime) }}
            </el-tag>
          </div>
        </template>
      </vxe-table-column>
      <vxe-table-column :title="状态" min-width="100" align="center">
        <template v-slot="{ row }">
          <el-tag :type="getStatusType(row)">
            {{ getStatusText(row) }}
          </el-tag>
        </template>
      </vxe-table-column>
      <vxe-table-column :title="操作" min-width="150" align="center">
        <template v-slot="{ row }">
          <div class="action-buttons">
            <el-button size="mini" type="success" @click="goUpCourse(row)">去上课</el-button>
          </div>
        </template>
      </vxe-table-column>
    </vxe-table>

    <!-- 分页 -->
    <Pagination
        :total="total"
        :page-size="limit"
        @on-change="currentChange"
        :current.sync="currentPage"
        @on-page-size-change="onPageSizeChange"
        :layout="'prev, pager, next, sizes'"
    ></Pagination>
  </el-card>
</template>

<script>
import { mapGetters } from 'vuex';
import Pagination from '@/components/oj/common/Pagination';
import api from '@/common/api';
import myMessage from "@/common/message";

export default {
  name: 'GroupCourse',
  components: {
    Pagination
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        name: '',
        gidList: [this.$route.params.groupID], // 默认只查询当前班级
        latelyDay: null,
      },
      total: 0,
      currentPage: 1,
      limit: 10,
      courseList: [],
      loading: false
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getGroupCourseList();
    },
    onPageSizeChange(pageSize) {
      this.limit = pageSize;
      this.init();
    },
    currentChange(page) {
      this.currentPage = page;
      this.init();
    },
    // 获取课程列表
    async getGroupCourseList() {
      this.loading = true;
      try {
        const res = await api.getStudentCoursePlanPage(
            {
              currentPage: this.currentPage,
              limit: this.limit
            },
            this.queryParams
        )
        if (res.data.status === 200) {
          // 添加序号
          this.courseList = res.data.data.records.map((item, index) => {
            return {
              ...item,
              rank: (this.currentPage - 1) * this.limit + index + 1
            }
          })
          this.total = res.data.data.total
        }
        this.loading = false;
      } catch (error) {
        this.loading = false;
        myMessage.error('获取数据失败')
      }
    },
    // 搜索
    search() {
      this.currentPage = 1
      this.getGroupCourseList()
    },
    // 重置查询
    resetQuery() {
      this.queryParams = {
        name: '',
        gidList: [this.$route.params.groupID], // 重置时仍然保留当前班级
        latelyDay: null
      }
      this.getGroupCourseList()
    },
    // 去上课
    goUpCourse(row) {
      window.open(row.url, '_blank')
    },
    formatDate(fullTime) {
      if (!fullTime) return '';
      return fullTime.split(' ')[0]; 
    },
    formatTime(fullTime) {
      if (!fullTime) return '';
      return fullTime.split(' ')[1].substr(0, 5); 
    },
    // 状态计算方法
    getStatusText(row) {
      const now = new Date().getTime();
      const start = new Date(row.startTime).getTime();
      const end = new Date(row.endTime).getTime();

      if (now < start) return '未上课';
      if (now > end) return '已下课';
      return '上课中';
    },
    getStatusType(row) {
      const status = this.getStatusText(row);
      return {
        '未上课': 'info',
        '上课中': 'success',
        '已下课': 'danger'
      }[status];
    },
    // 复制方法
    copyUrl(url) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(url)
            .then(() => myMessage.success('复制成功'))
            .catch(() => this.fallbackCopy(url));
      } else {
        this.fallbackCopy(url);
      }
    },
    fallbackCopy(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        myMessage.success('复制成功');
      } catch (err) {
        myMessage.error('复制失败，请手动复制');
      }
      document.body.removeChild(textArea);
    }
  },
  computed: {
    ...mapGetters(['isAuthenticated', 'isSuperAdmin', 'isGroupAdmin']),
  },
};
</script>

<style scoped>
.title {
  font-size: 20px;
  vertical-align: middle;
  float: left;
}
.filter-row {
  margin-bottom: 15px;
  text-align: center;
}
.time-range {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 5px;
}
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}
@media screen and (max-width: 768px) {
  .filter-row span {
    margin-left: 5px;
    margin-right: 5px;
  }
}
@media screen and (min-width: 768px) {
  .filter-row span {
    margin-left: 10px;
    margin-right: 10px;
  }
}
</style> 