<template>
  <el-card>
    <div class="member-management">
      <!-- 标题区 -->
      <div class="header">
        <div class="title-section">
          <span class="icon el-icon-user-solid"></span>
          <h2 class="title">{{ $t('m.Group_Member') }}</h2>
        </div>
        <el-button
          v-if="isSuperAdmin || isGroupAdmin"
          :type="adminPage ? 'warning' : 'primary'"
          class="toggle-button"
          size="small"
          @click="toggleAdminPage"
        >
          <span v-if="adminPage">{{ $t('m.Back') }}</span>
          <span v-else>{{ $t('m.Member_Admin') }}</span>
        </el-button>
      </div>

      <!-- 功能区 -->
      <div class="actions">
        <!-- 添加成员模块 -->
        <transition name="fade">
          <div v-show="adminPage" class="add-member">
            <el-input
              v-model="newMemberPhone"
              class="input-phone"
              placeholder="输入手机号"
              prefix-icon="el-icon-mobile-phone"
              size="small"
            ></el-input>
            <el-button size="small" type="success" @click="addMember">
              添加成员
            </el-button>
          </div>
        </transition>

        <!-- 搜索模块 -->
        <div class="search-member">
          <el-input
            v-model="searchMemberName"
            class="input-name"
            placeholder="输入名字"
            prefix-icon="el-icon-search"
            size="small"
            @keyup.enter.native="searchMember"
          ></el-input>
          <el-button icon="el-icon-search" size="small" type="primary" @click="searchMember">
            搜索
          </el-button>
        </div>
      </div>
    </div>


    <div v-if="!adminPage">
      <vxe-table
        :data="memberList"
        :loading="loading"
        align="center"
        auto-resize
        stripe
      >
        <vxe-table-column
          :title="$t('m.Username')"
          field="username"
          min-width="120"
          show-overflow
        >
          <template v-slot="{ row }">
            <el-link
              style="font-size: 13px;"
              type="primary"
              @click="goUserHome(row.username)"
            >{{ row.username }}
            </el-link>
          </template>
        </vxe-table-column>
        <vxe-table-column
          :title="'姓名'"
          field="realname"
          min-width="120"
        >
          <template v-slot="{ row }">
            {{ row.realname }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          :title="'昵称'"
          field="nickname"
          min-width="120"
        >
          <template v-slot="{ row }">
            {{ row.nickname }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          :title="$t('m.Join_Time')"
          field="gmtCreate"
          min-width="120"
        >
          <template v-slot="{ row }">
            {{ row.gmtCreate | localtime }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          :title="$t('m.Member_Auth')"
          field="auth"
          min-width="100"
        >
          <template v-slot="{ row }">
            <el-select v-model="row.auth" disabled size="small">
              <el-option :label="$t('m.Applying')" :value="1"></el-option>
              <el-option :label="$t('m.Refused')" :value="2"></el-option>
              <el-option :label="$t('m.General_Member')" :value="3"></el-option>
              <el-option :label="$t('m.Group_Admin')" :value="4"></el-option>
              <el-option :label="$t('m.Group_Root')" :value="5"></el-option>
            </el-select>
          </template>
        </vxe-table-column>
        <vxe-table-column
          :title="'操作'"
          field="actions"
          min-width="200"
        >
          <template v-slot="{ row }">
            <div style="display: flex; gap: 10px; justify-content: center;">
              <el-button
                icon="el-icon-document"
                plain
                size="mini"
                type="primary"
                @click="viewTucCodeLog(row)"
              >
                Tuc代码
              </el-button>
              <el-button
                icon="el-icon-tickets"
                plain
                size="mini"
                type="success"
                @click="viewEvaluationLog(row)"
              >
                OJ代码
              </el-button>
            </div>
          </template>
        </vxe-table-column>

      </vxe-table>

      <!-- 弹窗 -->
      <div>
        <el-dialog
          :title="dialogTitle"
          :visible.sync="dialogVisible"
          width="80%"
          @close="dialogVisible = false"
        >
          <code-table v-if="isTucCode" :code-list="tucCodeList"></code-table>
          <submit-list :key="uid" v-else ref="submitList" :type="SubmitListType.User" :uid="uid" />
          <el-button @click="dialogVisible = false">关闭</el-button>
        </el-dialog>
      </div>

      <Pagination
        :current.sync="currentPage"
        :layout="'prev, pager, next, sizes'"
        :page-size="limit"
        :total="total"
        @on-change="currentChange"
        @on-page-size-change="onPageSizeChange"
      ></Pagination>
    </div>
    <MemberList v-if="adminPage" @currentChange="currentChange"></MemberList>
  </el-card>
</template>

<script>
import { SubmitListType } from '@/common/submitListType';
import SubmitList from '@/components/tuc/SubmitList.vue';
import { mapGetters } from 'vuex';
import Pagination from '@/components/oj/common/Pagination';
import api from '@/common/api';
import Editor from '@/components/admin/Editor.vue';
import MemberList from '@/components/oj/group/MemberList';
import myMessage from '@/common/message';
import CodeTable from '@/components/tuc/CodeTable.vue';

export default {
  name: 'GroupMemberList',
  components: {
    SubmitList,
    CodeTable,
    Pagination,
    Editor,
    MemberList,
  },
  data() {
    return {
      total: 0,
      currentPage: 1,
      limit: 10,
      isTucCode: true,
      dialogVisible: false,
      memberList: [],
      tucCodeList: [],
      loading: false,
      adminPage: false,
      newMemberPhone: '', // 存储输入的手机号
      gid: null,
      uid: null,
      searchMemberName: undefined,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.gid = this.$route.params.groupID;
      this.getGroupMemberList();
    },
    // 切换 "成员管理" 与 "返回" 状态
    toggleAdminPage() {
      this.adminPage = !this.adminPage;
    },
    viewTucCodeLog(row) {
      this.isTucCode = true;
      api.listCodeRecordByUid({ uid: row.uid })
        .then((res) => {
          this.tucCodeList = res.data.data;
          this.dialogVisible = true;
        })
        .catch((error) => {
          myMessage.error(error.data.message);
        });
    },
    viewEvaluationLog(row) {
      // 先打开对话框再设置uid
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.uid = row.uid;
        this.isTucCode = false;
      });
    },
    searchMember() {
      if (!this.searchMemberName.trim()) {
        myMessage.warning('请输入有效的名字');
        return;
      }
      this.getGroupMemberList();
      // 替换为实际的搜索逻辑，比如调用API。
      console.log(`搜索成员名字: ${this.searchMemberName}`);
    },
    addMember() {
      const phone = this.newMemberPhone.trim();

      if (!phone) {
        return myMessage.warning('请输入有效的手机号');
      }

      api.addGroupMemberByPhone(this.gid, phone)
        .then(() => {
          myMessage.success(`添加成员 ${phone} 成功`);
          this.newMemberPhone = ''; // 清除输入框内容
          this.getGroupMemberList();
        })
        .catch((error) => {
          myMessage.error(error.data.message);
        });

      console.log(`添加成员手机号: ${phone}`);
    },
    onPageSizeChange(pageSize) {
      this.limit = pageSize;
      this.init();
    },
    currentChange(page) {
      this.currentPage = page;
      this.init();
    },
    goUserHome(username) {
      // this.$router.push({
      //   path: '/organization-home',
      //   query: { username },
      // });
    },
    getGroupMemberList(keyword) {
      this.loading = true;
      api.getGroupMemberList(
        this.currentPage,
        this.limit,
        this.$route.params.groupID,
        this.searchMemberName,
      )
        .then(
          (res) => {
            this.memberList = res.data.data.records;
            this.total = res.data.data.total;
            this.loading = false;
          },
          (err) => {
            this.loading = false;
          },
        );
    },
  },
  computed: {
    SubmitListType() {
      return SubmitListType;
    },
    ...mapGetters(['userInfo', 'isSuperAdmin', 'isGroupAdmin']),
  },
};
</script>
<style scoped>
/* 整体布局 */
.member-management {
  padding: 15px 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: Arial, sans-serif;
}

/* 标题区 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
  margin-bottom: 15px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin: 0;
}

.icon {
  font-size: 22px;
  color: #409eff;
}

/* 按钮样式 */
.toggle-button {
  font-size: 14px;
}

/* 功能区 */
.actions {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 添加成员模块 */
.add-member {
  display: flex;
  align-items: center;
  gap: 10px;
  animation: fadeIn 0.3s ease-in-out;
}

.input-phone {
  flex: 1;
  max-width: 240px;
}

/* 搜索模块 */
.search-member {
  display: flex;
  align-items: center;
  gap: 10px;
}

.input-name {
  flex: 1;
  max-width: 240px;
}

/* 按钮颜色差异化 */
.el-button {
  border-radius: 5px;
  font-size: 14px;
}

.el-button[type="success"] {
  background-color: #67c23a;
  border-color: #67c23a;
  color: white;
}

.el-button[type="primary"] {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.el-button[type="warning"] {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: white;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式优化 */
@media screen and (max-width: 768px) {
  .actions {
    flex-direction: column;
    gap: 15px;
  }

  .title {
    font-size: 16px;
  }
}
</style>





