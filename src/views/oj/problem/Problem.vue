<template>
  <div :class="bodyClass">
    <div id="problem-main">
      <!--problem main-->
      <el-row :id="'problem-box' + '-' + $route.name" class="problem-box">
        <!--<el-col v-if="notProgrammerProblem" :lg="6" :md="6" :sm="24">-->
        <!--  <div>-</div>-->
        <!--</el-col>-->
        <el-col :id="'problem-left'+'-'+ $route.name" :class="notProgrammerProblem?'problem-center':'problem-left'"
                :lg="notProgrammerProblem?24:12"
                :md="notProgrammerProblem?24:12" :sm="24">
          <el-tabs v-model="activeName" type="border-card" @tab-click="handleClickTab">
            <!-- 问题详情标签页 -->
            <el-tab-pane v-loading="loading" name="problemDetail">
              <!-- 标签页标题 -->
              <span slot="label"><i class="fa fa-list-alt">
                  {{ $t('m.Problem_Description') }}</i>
              </span>
              <!-- 问题详情内容区域 -->
              <div :id="'js-left'+'-'+ $route.name" :padding="10" class="js-left" shadow>
                <!-- 面板标题区域 -->
                <div slot="header" class="panel-title">
                  <span>{{ problemData.problem.title }}</span><br />
                  <!-- 问题标签区域 -->
                  <div class="problem-tag">
                    <!-- 文件I/O标签 -->
                    <span v-if="problemData.problem.isFileIO"
                          style="padding-right: 10px">
                      <el-popover
                        placement="bottom"
                        trigger="hover"
                      >
                      <el-tag
                        slot="reference"
                        effect="dark"
                        size="medium"
                        style="cursor: pointer;"
                        type="warning"
                      ><i class="el-icon-document"> {{ $t('m.File_IO') }}</i>
                      </el-tag>
                      <table style="white-space: nowrap;">
                        <tbody>
                          <tr>
                            <td align="right" style="padding-right: 10px">
                            <strong>{{ $t('m.Input_File') }}</strong>
                            </td>
                            <td>{{ problemData.problem.ioReadFileName }}</td>
                          </tr>
                          <tr>
                            <td align="right" style="padding-right: 10px">
                              <strong>{{ $t('m.Output_File') }}</strong>
                            </td>
                            <td>{{ problemData.problem.ioWriteFileName }}</td>
                          </tr>
                        </tbody>
                      </table>
                      </el-popover>
                    </span>
                    <!-- 比赛问题标签 -->
                    <span v-if="contestID && !contestEnded">
                      <el-tag
                        effect="plain"
                        size="medium"
                      >{{
                          $t('m.Contest_Problem')
                        }}</el-tag>
                    </span>
                    <!-- 问题标签 -->
                    <span
                      v-else-if="problemData.tags.length > 0"
                    >
                      <el-popover
                        placement="right-start"
                        trigger="hover"
                        width="60"
                      >
                        <el-tag
                          slot="reference"
                          effect="plain"
                          size="medium"
                          style="cursor: pointer;"
                          type="primary"
                        >{{ $t('m.Show_Tags') }} <i class="el-icon-caret-bottom"></i></el-tag>
                        <el-tag
                          v-for="(tag, index) in problemData.tags"
                          :key="index"
                          :color="tag.color ? tag.color : '#409eff'"
                          effect="dark"
                          size="small"
                          style="margin-right:5px;margin-top:2px"
                        >{{ tag.name }}</el-tag>
                      </el-popover>
                    </span>
                    <!-- 无标签 -->
                    <span
                      v-else-if="problemData.tags.length == 0"
                    >
                      <el-tag
                        effect="plain"
                        size="medium"
                      >{{
                          $t('m.No_tag')
                        }}</el-tag>
                    </span>
                  </div>

                  <!-- 问题菜单区域 -->
                  <div class="problem-menu">
                    <!-- 问题讨论链接 -->
                    <span v-if="isShowProblemDiscussion">
                      <el-link
                        :underline="false"
                        type="primary"
                        @click="goProblemDiscussion"
                      ><i
                        aria-hidden="true"
                        class="fa fa-comments"
                      ></i>
                        {{ $t('m.Problem_Discussion') }}</el-link>
                    </span>
                    <!-- 统计链接 -->
                    <span>
                      <el-link
                        :underline="false"
                        type="primary"
                        @click="graphVisible = !graphVisible"
                      ><i
                        aria-hidden="true"
                        class="fa fa-pie-chart"
                      ></i>
                        {{ $t('m.Statistic') }}</el-link>
                    </span>
                    <!-- 解决方案链接 -->
                    <span>
                      <el-link
                        :underline="false"
                        type="primary"
                        @click="goProblemSubmission"
                      ><i
                        aria-hidden="true"
                        class="fa fa-bars"
                      ></i>
                        {{ $t('m.Solutions') }}</el-link>
                    </span>
                  </div>
                  <!-- 问题信息区域 -->
                  <div class="question-intr">
                    <div v-if="!notProgrammerProblem">
                      <!-- 时间和内存限制 -->
                      <template v-if="!isCFProblem">
                      <span>{{ $t('m.Time_Limit') }}：C/C++
                        {{ problemData.problem.timeLimit }}MS，{{
                          $t('m.Other')
                        }}
                        {{ problemData.problem.timeLimit * 2 }}MS</span><br />
                        <span>{{ $t('m.Memory_Limit') }}：C/C++
                        {{ problemData.problem.memoryLimit }}MB，{{
                            $t('m.Other')
                          }}
                        {{ problemData.problem.memoryLimit * 2 }}MB</span><br />
                      </template>
                      <template v-else>
                      <span>{{ $t('m.Time_Limit') }}：{{
                          problemData.problem.timeLimit
                        }}MS</span>
                        <br />
                        <span>{{ $t('m.Memory_Limit') }}：{{
                            problemData.problem.memoryLimit
                          }}MB</span><br />
                      </template>
                    </div>
                    <!-- 难度等级 -->
                    <template v-if="problemData.problem.difficulty != null">
                      <span>{{ $t('m.Level') }}：<span
                        :style="getLevelColor(problemData.problem.difficulty)"
                        class="el-tag el-tag--small"
                      >{{
                          getLevelName(problemData.problem.difficulty)
                        }}</span></span>
                      <br />
                    </template>
                    <!-- 分数 -->
                    <template v-if="problemData.problem.type == 1">
                      <span>{{ $t('m.Score') }}：{{ problemData.problem.ioScore }}
                      </span>
                      <span
                        v-if="!contestID"
                        style="margin-left:5px;"
                      >
                        {{ $t('m.OI_Rank_Score') }}：{{
                          calcOIRankScore(
                            problemData.problem.ioScore,
                            problemData.problem.difficulty,
                          )
                        }}(0.1*{{ $t('m.Score') }}+2*{{ $t('m.Level') }})
                      </span>
                      <br />
                    </template>
                    <!-- 作者 -->
                    <template v-if="problemData.problem.author">
                      <span>{{ $t('m.Created') }}：<el-link
                        class="author-name"
                        type="info"
                        @click="goUserHome(problemData.problem.author)"
                      >{{ problemData.problem.author }}</el-link></span><br />
                    </template>
                  </div>
                </div>
                <!-- 问题内容区域 -->
                <div id="problem-content">
                  <!-- 描述 -->
                  <template v-if="problemData.problem.description">
                    <p class="title">{{ $t('m.Description') }}</p>
                    <Markdown
                      :content="problemData.problem.description"
                      :isAvoidXss="problemData.problem.gid != null"
                      class="md-content">
                    </Markdown>
                  </template>
                  <!-- 输入 -->
                  <template v-if="problemData.problem.input">
                    <p class="title">{{ $t('m.Input') }}</p>
                    <Markdown
                      :content="problemData.problem.input"
                      :isAvoidXss="problemData.problem.gid != null"
                      class="md-content">
                    </Markdown>
                  </template>
                  <!-- 输出 -->
                  <template v-if="problemData.problem.output">
                    <p class="title">{{ $t('m.Output') }}</p>
                    <Markdown
                      :content="problemData.problem.output"
                      :isAvoidXss="problemData.problem.gid != null"
                      class="md-content">
                    </Markdown>
                  </template>
                  <!-- 示例 -->
                  <template v-if="problemData.problem.examples">
                    <div
                      v-for="(example, index) of problemData.problem.examples"
                      :key="index"
                    >
                      <div v-if="example.input" class="flex-container example">
                        <div class="example-input">
                          <p class="title">
                            {{ $t('m.Sample_Input') }} {{ index + 1 }}
                            <a v-clipboard:copy="example.input" v-clipboard:error="onCopyError"
                               v-clipboard:success="onCopy"
                               class="copy">
                              <i class="el-icon-document-copy"></i>
                            </a>
                          </p>
                          <el-input type="textarea" rows="10" v-model="example.input"></el-input>
                        </div>
                        <div class="example-output">
                          <p class="title">
                            {{ $t('m.Sample_Output') }} {{ index + 1 }}
                            <a
                              v-clipboard:copy="example.output"
                              v-clipboard:error="onCopyError"
                              v-clipboard:success="onCopy"
                              class="copy"
                            >
                              <i class="el-icon-document-copy"></i>
                            </a>
                          </p>
                          <el-input type="textarea" rows="10" v-model="example.output"></el-input>
                        </div>
                      </div>
                    </div>
                  </template>
                  <!-- 提示 -->
                  <template v-if="problemData.problem.hint">
                    <p class="title">{{ $t('m.Hint') }}</p>
                    <el-card dis-hover>
                      <Markdown
                        :content="problemData.problem.hint"
                        :isAvoidXss="problemData.problem.gid != null"
                        class="hint-content">
                      </Markdown>
                    </el-card>
                  </template>
                  <!-- 来源 -->
                  <template v-if="problemData.problem.source && !contestID">
                    <p class="title">{{ $t('m.Source') }}</p>
                    <template v-if="problemData.problem.gid != null">
                      <p
                        v-dompurify-html="problemData.problem.source"
                        class="md-content"
                      ></p>
                    </template>
                    <template v-else>
                      <p
                        class="md-content"
                        v-html="problemData.problem.source"
                      ></p>
                    </template>
                  </template>
                </div>

                <!-- 非编程题答题区-->
                <div v-if="notProgrammerProblem">
                  <question-renderer
                    :options="problemData.options"
                    :problem-type="problemData.problem.problemType"
                    :reply-options="replyOptions"
                    @replyOptions="handleReplyOptions"
                  />


                  <div class="btn-container">
                    <div id="js-right-bottom">
                      <el-row>
                        <el-col :lg="10" :md="10" :sm="24" style="margin-top:4px;">
                          <!-- 当用户未登录时，显示登录提示 -->
                          <div v-if="!isAuthenticated">
                            <el-alert :closable="false" effect="dark" show-icon type="info">{{
                                $t('m.Please_login_first')
                              }}
                            </el-alert>
                          </div>
                          <!-- 显示提交状态 -->
                          <div v-if="statusVisible" class="status">
                            <!-- 待处理状态 -->
                            <template v-if="result.status == JUDGE_STATUS_RESERVE['sf']">
                              <span>{{ $t('m.Status') }}:</span>
                              <el-tag :color="submissionStatus.color" effect="dark" @click.native="reSubmit(submissionId)">
                                <i class="el-icon-refresh"></i>
                                {{ submissionStatus.text }}
                              </el-tag>
                            </template>
                            <!-- 无结果状态 -->
                            <template v-else-if="result.status == JUDGE_STATUS_RESERVE['snr']">
                              <el-alert :closable="false" effect="dark" show-icon type="warning">{{
                                  $t('m.Submitted_Not_Result')
                                }}
                              </el-alert>
                            </template>
                            <!-- OI 或 ACM 比赛的实时结果状态 -->
                            <template v-else-if="
                        !this.contestID ||
                          (this.contestID &&
                            ContestRealTimePermission &&
                            this.contestRuleType == RULE_TYPE.OI) ||
                          (this.contestID &&
                            this.contestRuleType == RULE_TYPE.ACM)
                      ">
                              <span style="font-size: 14px;font-weight: bolder;">{{ $t('m.Status') }}:</span>
                              <el-tooltip :content="$t('m.View_submission_details')" class="item" effect="dark" placement="top">
                                <el-tag :color="submissionStatus.color" class="submission-status" effect="dark"
                                        @click.native="submissionRoute">
                                  <!-- 处理中的状态图标 -->
                                  <template v-if="this.result.status == JUDGE_STATUS_RESERVE['Pending']
                          || this.result.status == JUDGE_STATUS_RESERVE['Compiling']
                          || this.result.status == JUDGE_STATUS_RESERVE['Judging']
                          || this.result.status == JUDGE_STATUS_RESERVE['Submitting']">
                                    <i class="el-icon-loading"></i> {{ submissionStatus.text }}
                                  </template>
                                  <!-- 通过状态图标 -->
                                  <template v-else-if="this.result.status == JUDGE_STATUS_RESERVE.ac">
                                    <i class="el-icon-success"> {{ submissionStatus.text }}</i>
                                  </template>
                                  <!-- 部分通过状态图标 -->
                                  <template v-else-if="this.result.status == JUDGE_STATUS_RESERVE.pa">
                                    <i class="el-icon-remove"> {{ submissionStatus.text }}</i>
                                  </template>
                                  <!-- 未通过状态图标 -->
                                  <template v-else>
                                    <i class="el-icon-error"> {{ submissionStatus.text }}</i>
                                  </template>
                                </el-tag>
                              </el-tooltip>
                            </template>
                            <!-- OI 比赛结束后的提交状态 -->
                            <template v-else-if="
                        this.contestID &&
                          !ContestRealTimePermission &&
                          this.contestRuleType == RULE_TYPE.OI
                      ">
                              <el-alert :closable="false" effect="dark" show-icon type="success">
                                {{ $t('m.Submitted_successfully') }}
                              </el-alert>
                            </template>
                          </div>
                          <!-- 用户已解决该问题的提示 -->
                          <div v-else-if="
                  (!this.contestID ||
                        this.contestRuleType == RULE_TYPE.ACM) &&
                      problemData.myStatus == JUDGE_STATUS_RESERVE.ac
                    ">
                            <el-alert :closable="false" effect="dark" show-icon type="success">
                              {{ $t('m.You_have_solved_the_problem') }}
                            </el-alert>
                          </div>
                          <!-- 已提交解决方案的提示 -->
                          <div v-else-if="
                      this.contestID &&
                        !ContestRealTimePermission &&
                        this.contestRuleType == RULE_TYPE.OI &&
                        submissionExists
                    ">
                            <el-alert :closable="false" effect="dark" show-icon type="success">
                              {{ $t('m.You_have_submitted_a_solution') }}
                            </el-alert>
                          </div>
                          <!-- 比赛结束后的提示 -->
                          <div v-if="contestEnded && !statusVisible">
                            <el-alert :closable="false" effect="dark" show-icon type="warning">{{ $t('m.Contest_has_ended') }}
                            </el-alert>
                          </div>
                        </el-col>
                        <el-col :lg="14" :md="14" :sm="24" style="margin-top:4px;">
                          <el-button
                            :disabled="problemSubmitDisabled || submitted || submitDisabled"
                            :loading="submitting"
                            class="fl-right"
                            icon="el-icon-edit-outline"
                            size="small"
                            type="primary"
                            @click.native="submitCode"
                          >
                            <span v-if="submitting">{{ $t('m.Submitting') }}</span>
                            <span v-else>{{ $t('m.Submit') }}</span>
                          </el-button>
                          <el-tag

                            v-if="false"
                            :class="openTestCaseDrawer?'tj-btn active':'tj-btn non-active'"
                            effect="plain"
                            type="success"
                            @click.native="openTestJudgeDrawer"
                          >
                    <span style="vertical-align: middle;">
                      {{ $t('m.Online_Test') }}
                    </span>
                          </el-tag>
                        </el-col>
                      </el-row>
                    </div>
                  </div>

                </div>
              </div>
            </el-tab-pane>
            <!-- 我的提交标签页 -->
            <el-tab-pane name="mySubmission">
              <span slot="label"><i class="el-icon-time"></i> {{ $t('m.My_Submission') }}</span>
              <template v-if="!isAuthenticated">
                <div
                  id="js-submission"
                  style="margin:20px 0px;margin-left:-20px;"
                >
                  <el-alert
                    :closable="false"
                    :description="$t('m.Login_to_view_your_submission_history')"
                    :title="$t('m.Please_login_first')"
                    center
                    show-icon
                    type="warning"
                  >
                  </el-alert>
                </div>
              </template>
              <template v-else>
                <div
                  id="js-submission"
                  style="margin-right:10px;"
                >
                  <vxe-table
                    :data="mySubmissions"
                    :loading="loadingTable"
                    align="center"
                    auto-resize
                    border="inner"
                    stripe
                  >
                    <vxe-table-column
                      :title="$t('m.Submit_Time')"
                      min-width="96"
                    >
                      <template v-slot="{ row }">
                        <span>
                          <el-tooltip
                            :content="row.submitTime | localtime"
                            placement="top"
                          >
                            <span>{{ row.submitTime | fromNow }}</span>
                          </el-tooltip>
                        </span>
                      </template>
                    </vxe-table-column>
                    <vxe-table-column
                      :title="$t('m.Status')"
                      field="status"
                      min-width="160"
                    >
                      <template v-slot="{ row }">
                        <span :class="getStatusColor(row.status)">{{
                            JUDGE_STATUS[row.status].name
                          }}</span>
                      </template>
                    </vxe-table-column>
                    <vxe-table-column
                      :title="$t('m.Time')"
                      min-width="96"
                    >
                      <template v-slot="{ row }">
                        <span>{{ submissionTimeFormat(row.time) }}</span>
                      </template>
                    </vxe-table-column>
                    <vxe-table-column
                      :title="$t('m.Memory')"
                      min-width="96"
                    >
                      <template v-slot="{ row }">
                        <span>{{ submissionMemoryFormat(row.memory) }}</span>
                      </template>
                    </vxe-table-column>
                    <vxe-table-column
                      v-if="problemData.problem.type == 1"
                      :title="$t('m.Score')"
                      min-width="64"
                    >
                      <template v-slot="{ row }">
                        <template v-if="contestID && row.score != null">
                          <el-tag
                            :type="JUDGE_STATUS[row.status]['type']"
                            effect="plain"
                            size="medium"
                          >{{ row.score }}
                          </el-tag>
                        </template>
                        <template v-else-if="row.score != null">
                          <el-tooltip placement="top">
                            <div slot="content">
                              {{ $t('m.Problem_Score') }}：{{
                                row.score != null ? row.score : $t('m.Unknown')
                              }}<br />{{ $t('m.OI_Rank_Score') }}：{{
                                row.oiRankScore != null
                                  ? row.oiRankScore
                                  : $t('m.Unknown')
                              }}<br />
                              {{
                                $t('m.OI_Rank_Calculation_Rule')
                              }}：(score*0.1+difficulty*2)
                            </div>
                            <el-tag
                              :type="JUDGE_STATUS[row.status]['type']"
                              effect="plain"
                              size="medium"
                            >{{ row.score }}
                            </el-tag>
                          </el-tooltip>
                        </template>
                        <template v-else-if="
                            row.status == JUDGE_STATUS_RESERVE['Pending'] ||
                              row.status == JUDGE_STATUS_RESERVE['Compiling'] ||
                              row.status == JUDGE_STATUS_RESERVE['Judging']
                          ">
                          <el-tag
                            :type="JUDGE_STATUS[row.status]['type']"
                            effect="plain"
                            size="medium"
                          >
                            <i class="el-icon-loading"></i>
                          </el-tag>
                        </template>
                        <template v-else>
                          <el-tag
                            :type="JUDGE_STATUS[row.status]['type']"
                            effect="plain"
                            size="medium"
                          >--
                          </el-tag>
                        </template>
                      </template>
                    </vxe-table-column>
                    <vxe-table-column
                      :title="$t('m.Language')"
                      field="language"
                      min-width="130"
                      show-overflow
                    >
                      <template v-slot="{ row }">
                        <el-tooltip
                          :content="$t('m.View_submission_details')"
                          class="item"
                          effect="dark"
                          placement="top"
                        >
                          <el-button
                            type="text"
                            @click="showSubmitDetail(row)"
                          >{{ row.language }}
                          </el-button>
                        </el-tooltip>
                      </template>
                    </vxe-table-column>
                  </vxe-table>
                  <Pagination
                    :current.sync="mySubmission_currentPage"
                    :page-size="mySubmission_limit"
                    :total="mySubmission_total"
                    @on-change="getMySubmission"
                  ></Pagination>
                </div>
              </template>
            </el-tab-pane>

            <el-tab-pane
              v-if="userExtraFile"
              name="extraFile"
            >
              <span slot="label"><i class="fa fa-file-code-o"> {{ $t('m.Problem_Annex') }}</i>
              </span>
              <div id="js-extraFile">
                <el-divider></el-divider>
                <div>
                  <el-tag
                    v-for="(value, key, index) in userExtraFile"
                    :key="index"
                    :disable-transitions="false"
                    class="extra-file"
                    @click="showExtraFileContent(key, value)"
                  >
                    <i class="fa fa-file-code-o"> {{ key }}</i>
                  </el-tag>
                </div>
                <el-divider></el-divider>

                <div
                  v-if="fileContent"
                  class="markdown-body"
                >
                  <h3>
                    {{ fileName }}
                    <el-button
                      circle
                      class="file-download"
                      icon="el-icon-download"
                      size="small"
                      type="primary"
                      @click="downloadExtraFile"
                    ></el-button>
                  </h3>
                  <pre v-highlight="fileContent"><code class="c++"></code></pre>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-col>
        <div v-if="!notProgrammerProblem" :id="'js-center'+'-'+ $route.name" :title="$t('m.Shrink_Sidebar')"
             class="problem-resize hidden-sm-and-down">
          <span>⋮</span>
          <span>
            <el-tooltip v-if="!toResetWatch" :content="
              toWatchProblem
                ? $t('m.View_Problem_Content')
                : $t('m.Only_View_Problem')
            " placement="right">
              <el-button circle class="right-fold fold" icon="el-icon-caret-right" size="mini"
                         @click.stop="onlyWatchProblem"></el-button>
            </el-tooltip>
            <el-tooltip v-else :content="$t('m.Put_away_the_full_screen_and_write_the_code')" placement="left">
              <el-button circle class="left-fold fold" icon="el-icon-caret-left" size="mini"
                         @click.stop="resetWatch(false)"></el-button>
            </el-tooltip>
          </span>
        </div>
        <el-col v-if="!notProgrammerProblem"
                :id="'problem-right' + '-' + $route.name" :lg="12" :md="12" :sm="24" class="problem-right">
          <el-card id="submit-code" :padding="10" class="submit-detail" shadow="always">
            <div>
              <!--flow-design-->
              <flow-design v-model="flowImage" />
              <!-- CodeMirror组件用于代码编辑，接收多个属性和事件处理函数 -->
              <CodeMirror
                :fontSize.sync="fontSize"
                :height.sync="height"
                :isAuthenticated="isAuthenticated"
                :isRemoteJudge="problemData.problem.isRemote"
                :language.sync="language"
                :languages="problemData.languages"
                :openFocusMode.sync="openFocusMode"
                :openTestCaseDrawer.sync="openTestCaseDrawer"
                :pid="problemData.problem.id"
                :problemTestCase="problemData.problem.examples"
                :submitDisabled="submitDisabled"
                :tabSize.sync="tabSize"
                :theme.sync="theme"
                :type="problemType"
                :value.sync="code"
                @changeLang="onChangeLang"
                @changeTheme="onChangeTheme"
                @getUserLastAccepetedCode="getUserLastAccepetedCode"
                @resetCode="onResetToTemplate"
                @switchFocusMode="switchFocusMode"
              ></CodeMirror>
            </div>

            <div id="js-right-bottom">
              <el-row>
                <el-col :lg="10" :md="10" :sm="24" style="margin-top:4px;">
                  <!-- 当用户未登录时，显示登录提示 -->
                  <div v-if="!isAuthenticated">
                    <el-alert :closable="false" effect="dark" show-icon type="info">{{
                        $t('m.Please_login_first')
                      }}
                    </el-alert>
                  </div>
                  <!-- 显示提交状态 -->
                  <div v-if="statusVisible" class="status">
                    <!-- 待处理状态 -->
                    <template v-if="result.status == JUDGE_STATUS_RESERVE['sf']">
                      <span>{{ $t('m.Status') }}:</span>
                      <el-tag :color="submissionStatus.color" effect="dark" @click.native="reSubmit(submissionId)">
                        <i class="el-icon-refresh"></i>
                        {{ submissionStatus.text }}
                      </el-tag>
                    </template>
                    <!-- 无结果状态 -->
                    <template v-else-if="result.status == JUDGE_STATUS_RESERVE['snr']">
                      <el-alert :closable="false" effect="dark" show-icon type="warning">{{
                          $t('m.Submitted_Not_Result')
                        }}
                      </el-alert>
                    </template>
                    <!-- OI 或 ACM 比赛的实时结果状态 -->
                    <template v-else-if="
                        !this.contestID ||
                          (this.contestID &&
                            ContestRealTimePermission &&
                            this.contestRuleType == RULE_TYPE.OI) ||
                          (this.contestID &&
                            this.contestRuleType == RULE_TYPE.ACM)
                      ">
                      <span style="font-size: 14px;font-weight: bolder;">{{ $t('m.Status') }}:</span>
                      <el-tooltip :content="$t('m.View_submission_details')" class="item" effect="dark" placement="top">
                        <el-tag :color="submissionStatus.color" class="submission-status" effect="dark"
                                @click.native="submissionRoute">
                          <!-- 处理中的状态图标 -->
                          <template v-if="this.result.status == JUDGE_STATUS_RESERVE['Pending']
                          || this.result.status == JUDGE_STATUS_RESERVE['Compiling']
                          || this.result.status == JUDGE_STATUS_RESERVE['Judging']
                          || this.result.status == JUDGE_STATUS_RESERVE['Submitting']">
                            <i class="el-icon-loading"></i> {{ submissionStatus.text }}
                          </template>
                          <!-- 通过状态图标 -->
                          <template v-else-if="this.result.status == JUDGE_STATUS_RESERVE.ac">
                            <i class="el-icon-success"> {{ submissionStatus.text }}</i>
                          </template>
                          <!-- 部分通过状态图标 -->
                          <template v-else-if="this.result.status == JUDGE_STATUS_RESERVE.pa">
                            <i class="el-icon-remove"> {{ submissionStatus.text }}</i>
                          </template>
                          <!-- 未通过状态图标 -->
                          <template v-else>
                            <i class="el-icon-error"> {{ submissionStatus.text }}</i>
                          </template>
                        </el-tag>
                      </el-tooltip>
                    </template>
                    <!-- OI 比赛结束后的提交状态 -->
                    <template v-else-if="
                        this.contestID &&
                          !ContestRealTimePermission &&
                          this.contestRuleType == RULE_TYPE.OI
                      ">
                      <el-alert :closable="false" effect="dark" show-icon type="success">
                        {{ $t('m.Submitted_successfully') }}
                      </el-alert>
                    </template>
                  </div>
                  <!-- 用户已解决该问题的提示 -->
                  <div v-else-if="
                      (!this.contestID ||
                        this.contestRuleType == RULE_TYPE.ACM) &&
                      problemData.myStatus == JUDGE_STATUS_RESERVE.ac
                    ">
                    <el-alert :closable="false" effect="dark" show-icon type="success">
                      {{ $t('m.You_have_solved_the_problem') }}
                    </el-alert>
                  </div>
                  <!-- 已提交解决方案的提示 -->
                  <div v-else-if="
                      this.contestID &&
                        !ContestRealTimePermission &&
                        this.contestRuleType == RULE_TYPE.OI &&
                        submissionExists
                    ">
                    <el-alert :closable="false" effect="dark" show-icon type="success">
                      {{ $t('m.You_have_submitted_a_solution') }}
                    </el-alert>
                  </div>
                  <!-- 比赛结束后的提示 -->
                  <div v-if="contestEnded && !statusVisible">
                    <el-alert :closable="false" effect="dark" show-icon type="warning">{{ $t('m.Contest_has_ended') }}
                    </el-alert>
                  </div>
                </el-col>
                <el-col :lg="14" :md="14" :sm="24" style="margin-top:4px;">
                  <el-button
                    :disabled="problemSubmitDisabled || submitted || submitDisabled"
                    :loading="submitting"
                    class="fl-right"
                    icon="el-icon-edit-outline"
                    size="small"
                    type="primary"
                    @click.native="submitCode"
                  >
                    <span v-if="submitting">{{ $t('m.Submitting') }}</span>
                    <span v-else>{{ $t('m.Submit') }}</span>
                  </el-button>
                  <el-tag
                    v-if="!submitDisabled"
                    :class="openTestCaseDrawer?'tj-btn active':'tj-btn non-active'"
                    effect="plain"
                    type="success"
                    @click.native="openTestJudgeDrawer"
                  >
                    <span style="vertical-align: middle;">
                      {{ $t('m.Online_Test') }}
                    </span>
                  </el-tag>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <ProblemHorizontalMenu
      v-if="showProblemHorizontalMenu"
      ref="problemHorizontalMenu"
      :cid="contestID"
      :gid="groupID"
      :pid.sync="problemData.problem.id"
      :tid="trainingID">
    </ProblemHorizontalMenu>

    <!--
      弹窗对话框组件，用于展示图表详情。
      使用 :visible.sync 语法糖绑定 graphVisible 属性，实现弹窗的显示与隐藏。
      宽度设置为 400px，以控制弹窗的大小。
    -->
    <el-dialog
      :visible.sync="graphVisible"
      width="400px"
    >
      <!--
        ECharts 图表容器，使用 id="pieChart-detail" 标识。
        通过 ECharts 组件渲染大型饼图，传递 largePie 配置项和 largePieInitOpts 初始化选项。
      -->
      <div id="pieChart-detail">
        <ECharts
          :initOptions="largePieInitOpts"
          :options="largePie"
        ></ECharts>
      </div>
      <!--
        对话框底部操作按钮区域。
        使用具名插槽 slot="footer" 定义底部内容。
        提供一个“关闭”按钮，点击后将 graphVisible 设置为 false，从而关闭对话框。
        按钮类型为 ghost，表示幽灵按钮样式，尺寸为 small。
        使用 $t('m.Close') 实现国际化，动态显示“关闭”文本。
      -->
      <div slot="footer">
        <el-button
          size="small"
          type="ghost"
          @click="graphVisible = false"
        >{{
            $t('m.Close')
          }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="submitPwdVisible"
      width="340px"
    >
      <el-form>
        <el-form-item
          :label="$t('m.Enter_the_contest_password')"
          required
        >
          <el-input
            v-model="submitPwd"
            :placeholder="$t('m.Enter_the_contest_password')"
            show-password
          ></el-input>
        </el-form-item>
        <el-button
          round
          style="margin-left:130px"
          type="primary"
          @click="checkContestPassword"
        >
          {{ $t('m.Submit') }}
        </el-button>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/common/api';
import { addCodeBtn } from '@/common/codeblock';
import { buildIndividualLanguageAndSettingKey, buildProblemCodeAndSettingKey, CONTEST_STATUS, JUDGE_STATUS, JUDGE_STATUS_RESERVE, PROBLEM_LEVEL, RULE_TYPE } from '@/common/constants';
import myMessage from '@/common/message';
import storage from '@/common/storage';
import utils from '@/common/utils';
import CodeMirror from '@/components/oj/common/CodeMirror.vue';
import Markdown from '@/components/oj/common/Markdown';
import Pagination from '@/components/oj/common/Pagination';
import ProblemHorizontalMenu from '@/components/oj/common/ProblemHorizontalMenu';
import Diagram from '@/components/tuc/flow/components/Diagram.vue';
import FlowDesign from '@/components/tuc/FlowDesign.vue';
import { ProblemType } from '@/components/tuc/problem/problemEnum';
import QuestionRenderer from '@/components/tuc/problem/QuestionRenderer.vue';
import { mapActions, mapGetters } from 'vuex';
import { largePie, pie } from './chartData';
// 只显示这些状态的图形占用
const filtedStatus = ['wa', 'ce', 'ac', 'pa', 'tle', 'mle', 're', 'pe'];

export default {
  name: 'ProblemDetails',
  components: {
    Diagram,
    FlowDesign,
    QuestionRenderer,
    CodeMirror,
    Pagination,
    ProblemHorizontalMenu,
    Markdown,
  },
  data() {
    return {
      // 控制状态显示的可见性
      statusVisible: false,
      // 是否需要验证码
      captchaRequired: false,
      // 控制图形验证码的可见性
      graphVisible: false,
      // 提交记录是否存在
      submissionExists: false,
      // 验证码代码
      captchaCode: '',
      // 验证码图片源
      captchaSrc: '',
      // 比赛ID
      contestID: 0,
      // 班级ID，可能为空
      groupID: null,
      // 题目ID
      problemID: '',
      // 训练ID，可能为空
      trainingID: null,
      // 是否正在提交
      submitting: false,
      // 代码提交内容
      code: '',
      // 编程语言
      language: '',
      // 是否远程提交
      isRemote: false,
      // 编辑器主题
      theme: 'solarized',
      // 编辑器字体大小
      fontSize: '14px',
      // 编辑器Tab大小
      tabSize: 4,
      // 编辑器高度
      height: 550,
      // 提交记录ID
      submissionId: '',
      // 是否已提交
      submitted: false,
      // 是否禁用提交按钮
      submitDisabled: false,
      // 是否显示提交密码对话框
      submitPwdVisible: false,
      // 提交密码
      submitPwd: '',
      // 判定状态，初始化为9（通常代表等待中）
      result: {
        status: 9,
      },
      // 客观题回答内容
      replyOptions: [],
      // 流程图
      flowImage: '',
      // 题目数据
      problemData: {
        problem: {
          // 题目难度
          difficulty: 0,
        },
        // 题目数量统计
        problemCount: {},
        // 题目标签
        tags: [],
        // 支持的编程语言
        languages: [],
        // 代码模板
        codeTemplate: {},

        options: [],
      },
      // 引用pie图表
      pie: pie,
      // 引用大尺寸pie图表
      largePie: largePie,
      // echarts 无法获取隐藏dom的大小，需手动指定
      largePieInitOpts: {
        width: '380',
        height: '380',
      },
      // 判定状态预留
      JUDGE_STATUS_RESERVE: {},
      // 判定状态
      JUDGE_STATUS: {},
      // 题目难度级别
      PROBLEM_LEVEL: {},
      // 比赛规则类型
      RULE_TYPE: {},
      // 是否需要重置监视器
      toResetWatch: false,
      // 是否监视题目变化
      toWatchProblem: false,
      // 当前激活的标签页名称
      activeName: 'problemDetail',
      // 是否正在加载表格数据
      loadingTable: false,
      // 我的提交记录总数
      mySubmission_total: 0,
      // 我的提交记录每页数量
      mySubmission_limit: 10,
      // 我的提交记录当前页码
      mySubmission_currentPage: 1,
      // 我的提交记录列表
      mySubmissions: [],
      // 是否正在加载
      loading: false,
      // 页面body的样式类
      bodyClass: '',
      // 用户额外文件
      userExtraFile: null,
      // 文件内容
      fileContent: '',
      // 文件名
      fileName: '',
      // 是否打开测试用例抽屉
      openTestCaseDrawer: false,
      // 是否打开专注模式
      openFocusMode: false,
      // 是否显示题目水平菜单
      showProblemHorizontalMenu: false,
    };
  },
  created() {
    this.initProblemCodeAndSetting();
    this.JUDGE_STATUS_RESERVE = Object.assign({}, JUDGE_STATUS_RESERVE);
    this.JUDGE_STATUS = Object.assign({}, JUDGE_STATUS);
    this.PROBLEM_LEVEL = Object.assign({}, PROBLEM_LEVEL);
    this.RULE_TYPE = Object.assign({}, RULE_TYPE);
    let isFocusModePage = utils.isFocusModePage(this.$route.name);
    if (
      this.$route.name === 'ProblemDetails' || isFocusModePage
    ) {
      this.bodyClass = 'problem-body';
    }
    if (isFocusModePage && (this.$route.params.contestID || this.$route.params.trainingID)) {
      this.contestID = this.$route.params.contestID;
      this.trainingID = this.$route.params.trainingID;
      this.showProblemHorizontalMenu = true;
    }
  },

  mounted() {
    this.init();
    this.dragControllerDiv();
    this.resizeWatchHeight();
    window.onresize = () => {
      this.resizeWatchHeight();
    };
  },
  methods: {
    // 映射 changeDomTitle 动作到组件
    ...mapActions(['changeDomTitle']),

    /**
     * 初始化问题代码和设置
     * 本方法尝试从缓存中加载问题的代码和设置，如果找不到，则加载用户的语言和设置
     */
    initProblemCodeAndSetting() {
      this.code = '';
      // 获取缓存中的该题的做题代码，代码语言，代码风格
      let problemCodeAndSetting = storage.get(
        buildProblemCodeAndSettingKey(
          this.$route.params.problemID,
          this.$route.params.contestID,
        ),
      );
      if (problemCodeAndSetting) {
        this.language = problemCodeAndSetting.language;
        this.code = problemCodeAndSetting.code;
        this.theme = problemCodeAndSetting.theme;
        this.fontSize = problemCodeAndSetting.fontSize;
        this.tabSize = problemCodeAndSetting.tabSize;
      } else {
        let individualLanguageAndSetting = storage.get(
          buildIndividualLanguageAndSettingKey(),
        );
        if (individualLanguageAndSetting) {
          this.language = individualLanguageAndSetting.language;
          this.theme = individualLanguageAndSetting.theme;
          this.fontSize = individualLanguageAndSetting.fontSize;
          this.tabSize = individualLanguageAndSetting.tabSize;
        }
      }
    },

    /**
     * 处理点击选项卡事件
     * 如果用户点击“我的提交”选项卡并且用户已认证，则调用 getMySubmission 方法
     * @param {Object} params - 事件参数，包含选项卡的名称
     */
    handleClickTab({ name }) {
      if (name == 'mySubmission' && this.isAuthenticated) {
        this.getMySubmission();
      }
    },

    /**
     * 获取我的提交记录
     * 本方法根据当前问题和比赛ID获取用户的提交记录，并处理分页和状态
     */
    getMySubmission() {
      let params = {
        onlyMine: true,
        currentPage: this.mySubmission_currentPage,
        problemID: this.problemID,
        contestID: this.contestID,
        completeProblemID: true,
        gid: this.groupID,
        limit: this.mySubmission_limit,
      };
      if (this.contestID) {
        if (this.contestStatus == CONTEST_STATUS.SCHEDULED) {
          params.beforeContestSubmit = true;
        } else {
          params.beforeContestSubmit = false;
        }
        params.containsEnd = true;
      }
      let func = this.contestID
        ? 'getContestSubmissionList'
        : 'getSubmissionList';
      this.loadingTable = true;
      api[func](this.mySubmission_limit, utils.filterEmptyValue(params))
        .then(
          (res) => {
            let data = res.data.data;
            this.mySubmissions = data.records;
            this.mySubmission_total = data.total;
            this.loadingTable = false;
          },
          (err) => {
            this.loadingTable = false;
          },
        )
        .catch(() => {
          this.loadingTable = false;
        });
    },

    /**
     * 获取状态颜色
     * 根据提交状态返回相应的 CSS 类名，用于显示不同颜色的状态标签
     * @param {String} status - 提交状态
     * @return {String} CSS 类名
     */
    getStatusColor(status) {
      return 'el-tag el-tag--medium status-' + JUDGE_STATUS[status].color;
    },

    /**
     * 提交时间格式化
     * 格式化提交时间，使其更易于阅读
     * @param {String} time - 提交时间
     * @return {String} 格式化后的时间
     */
    submissionTimeFormat(time) {
      return utils.submissionTimeFormat(time);
    },

    /**
     * 格式化提交内存
     * @param {number} memory - 提交使用的内存量
     * @returns {string} - 格式化后的内存字符串
     */
    submissionMemoryFormat(memory) {
      return utils.submissionMemoryFormat(memory);
    },

    /**
     * 显示提交详情
     * 根据不同的条件路由到相应的提交详情页面
     * @param {object} row - 包含提交信息的对象
     */
    showSubmitDetail(row) {
      if (row.cid != 0) {
        // 比赛提交详情
        this.$router.push({
          name: 'ContestSubmissionDetails',
          params: {
            contestID: this.$route.params.contestID,
            problemID: row.displayId,
            submitID: row.submitId,
          },
        });
      } else if (this.groupID) {
        this.$router.push({
          name: 'GroupSubmissionDetails',
          params: {
            submitID: row.submitId,
          },
        });
      } else {
        this.$router.push({
          name: 'SubmissionDetails',
          params: { submitID: row.submitId },
        });
      }
    },

    /**
     * 控制拖拽分割线的函数
     * 该函数用于处理问题页面左右拖拽的功能
     */
    dragControllerDiv() {
      var resize = document.getElementById(
        'js-center' + '-' + this.$route.name,
      );
      var left = document.getElementById(
        'problem-left' + '-' + this.$route.name,
      );
      var right = document.getElementById(
        'problem-right' + '-' + this.$route.name,
      );
      var box = document.getElementById(
        'problem-box' + '-' + this.$route.name,
      );
      const _this = this;
      // 鼠标按下事件
      resize.onmousedown = function(e) {
        //颜色改变提醒
        resize.style.background = '#818181';
        var startX = e.clientX;
        // 鼠标拖动事件
        document.onmousemove = function(e) {
          resize.left = startX;
          var endX = e.clientX;
          var moveLen = resize.left + (endX - startX); // （endx-startx）=移动的距离。resize.left+移动的距离=左边区域最后的宽度
          var maxT = box.offsetWidth - resize.offsetWidth; // 容器宽度 - 左边区域的宽度 = 右边区域的宽度
          if (moveLen < 420) {
            moveLen = 0; // 左边区域的最小宽度为420px
            _this.toWatchProblem = true;
          } else {
            _this.toWatchProblem = false;
          }
          if (moveLen > maxT - 580) moveLen = maxT - 580; //右边区域最小宽度为580px
          let leftRadio = (moveLen / box.offsetWidth) * 100;
          resize.style.left = leftRadio + '%';
          left.style.width = leftRadio + '%'; // 设置左侧区域的宽度
          right.style.width = (100 - leftRadio) + '%';
          if (leftRadio < 100) {
            _this.toResetWatch = false;
            right.style.display = '';
          }
        };
        // 鼠标松开事件
        document.onmouseup = function(evt) {
          //颜色恢复
          resize.style.background = '#d6d6d6';
          document.onmousemove = null;
          document.onmouseup = null;
          resize.releaseCapture && resize.releaseCapture(); //当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
        };
        resize.setCapture && resize.setCapture(); //该函数在属于当前线程的指定窗口里设置鼠标捕获
        return false;
      };
    },
    /**
     * 进入只监视问题模式
     * 此函数用于切换界面布局，以便用户专注于问题显示区域
     */
    onlyWatchProblem() {
      // 如果已经处于只监视问题模式，则重置监视并退出此模式
      if (this.toWatchProblem) {
        this.resetWatch(true);
        this.toWatchProblem = false;
        return;
      }
      // 获取界面元素，用于调整布局
      var resize = document.getElementById(
        'js-center' + '-' + this.$route.name,
      );
      var left = document.getElementById(
        'problem-left' + '-' + this.$route.name,
      );
      var right = document.getElementById(
        'problem-right' + '-' + this.$route.name,
      );
      var box = document.getElementById(
        'problem-box' + '-' + this.$route.name,
      );
      // 调整左边栏和右边栏的宽度，以及分割线的位置
      resize.style.left = box.clientWidth - 10 + 'px';
      left.style.width = box.clientWidth - 10 + 'px';
      right.style.width = '0px';
      right.style.display = 'none';
      this.toResetWatch = true;
    },

    /**
     * 重置监视模式
     * @param {boolean} minLeft - 是否恢复左边栏的最小宽度
     * 此函数用于在只监视问题模式和其他布局之间切换
     */
    resetWatch(minLeft = false) {
      // 获取界面元素，用于调整布局
      var resize = document.getElementById(
        'js-center' + '-' + this.$route.name,
      );
      var left = document.getElementById(
        'problem-left' + '-' + this.$route.name,
      );
      var right = document.getElementById(
        'problem-right' + '-' + this.$route.name,
      );
      var box = document.getElementById(
        'problem-box' + '-' + this.$route.name,
      );

      let leftWidth = 0;
      // 根据参数决定左边栏的宽度
      if (minLeft) {
        leftWidth = 431; // 恢复左边最小420px+滑块11px
      } else {
        leftWidth = box.clientWidth - 580; // 右边最小580px
      }
      // 计算左边栏的百分比宽度，并调整布局
      let leftRadio = (leftWidth / box.offsetWidth) * 100;
      resize.style.left = leftRadio + '%';
      left.style.width = leftRadio + '%';
      right.style.width = (100 - leftRadio) + '%';
      right.style.display = '';
      this.toResetWatch = false;
    },

    /**
     * 调整监视模式的高度
     * 此函数用于根据窗口大小动态调整问题显示区域的高度
     */
    resizeWatchHeight() {
      try {
        // 获取头部的高度和宽度，用于计算问题显示区域的高度
        let headerHeight = document.getElementById('header').offsetHeight;
        let headerWidth = document.getElementById('header').offsetWidth;
        let totalHeight = window.innerHeight;

        let left = document.getElementById(
          'problem-left' + '-' + this.$route.name,
        );
        let right = document.getElementById(
          'problem-right' + '-' + this.$route.name,
        );
        // 根据窗口大小调整左边栏和右边栏的宽度
        if (headerWidth >= 992) {
          let box = document.getElementById(
            'problem-box' + '-' + this.$route.name,
          );
          let tmp = (left.clientWidth / box.clientWidth) * 100;
          left.style.width = tmp + '%';
          right.style.width = (100 - tmp) + '%';
        } else {
          right.style.width = '100%';
        }

        // 计算问题显示区域的高度
        let problemLeftHight = totalHeight - (headerHeight + 64);
        if (this.showProblemHorizontalMenu) {
          let footerMenuHeight = document.getElementById('problem-footer').offsetHeight;
          problemLeftHight = problemLeftHight - footerMenuHeight;
        }
        let jsRHeaderHeight =
          document.getElementById('js-right-header').offsetHeight;
        let jsRBottomHeight =
          document.getElementById('js-right-bottom').offsetHeight;

        // 确保底部区域的高度不小于最小值
        if (jsRBottomHeight < 48) {
          jsRBottomHeight = 48;
        }

        let problemRightHight = problemLeftHight - 95 - (jsRHeaderHeight - 36) - (jsRBottomHeight - 48);
        if (problemRightHight < 0) {
          problemRightHight = 0;
        }
        this.height = problemRightHight;
        if (problemLeftHight < 0) {
          problemLeftHight = 0;
        }
        // 根据当前显示的选项卡调整左边栏的高度
        if (this.activeName == 'problemDetail') {
          if (headerWidth >= 992) {
            document
              .getElementById('js-left' + '-' + this.$route.name)
              .setAttribute(
                'style',
                'height:' + problemLeftHight + 'px !important',
              );
          } else {
            document
              .getElementById('js-left' + '-' + this.$route.name)
              .setAttribute(
                'style',
                'height: auto',
              );
          }
        } else if (this.activeName == 'mySubmission') {
          document
            .getElementById('js-submission')
            .setAttribute(
              'style',
              'height:' + problemLeftHight + 'px !important',
            );
        } else if (this.activeName == 'extraFile') {
          document
            .getElementById('js-extraFile')
            .setAttribute(
              'style',
              'height:' + problemLeftHight + 'px !important',
            );
        }
        // 调整分割线的位置
        document
          .getElementById('js-center' + '-' + this.$route.name)
          .setAttribute(
            'style',
            'top:' + problemLeftHight * 0.5 + 'px !important; left:'
            + left.style.width,
          );
      } catch (e) {
      }
    },
    /**
     * 初始化函数
     * 根据不同的路由名称和参数，初始化问题详情页面的状态和数据
     * 包括获取竞赛数据、设置焦点模式、处理路由参数等
     */
    init() {
      // 如果路由名称是ContestFullProblemDetails，则获取竞赛数据
      if (this.$route.name === 'ContestFullProblemDetails') {
        this.$store.dispatch('getContest');
      }
      // 检查是否是焦点模式页面
      this.openFocusMode = utils.isFocusModePage(this.$route.name);
      // 处理路由参数，获取竞赛ID、班级ID、问题ID和训练ID
      if (this.$route.params.contestID) {
        this.contestID = this.$route.params.contestID;
      }
      if (this.$route.params.groupID) {
        this.groupID = this.$route.params.groupID;
      }
      this.problemID = this.$route.params.problemID;
      if (this.$route.params.trainingID) {
        this.trainingID = this.$route.params.trainingID;
      }
      // 根据路由名称确定获取问题数据的函数
      let func =
        this.$route.name === 'ContestProblemDetails' ||
        this.$route.name === 'ContestFullProblemDetails'
          ? 'getContestProblem'
          : 'getProblem';
      this.loading = true;
      // 调用API获取问题数据
      api[func](this.problemID, this.contestID, this.groupID, true).then(
        (res) => {
          let result = res.data.data;
          // 更新页面标题
          this.changeDomTitle({ title: result.problem.title });
          // 初始化问题数据，包括设置默认状态和解析示例代码
          result['myStatus'] = -10; // 设置默认值
          result.problem.examples = utils.stringToExamples(
            result.problem.examples,
          );
          if (result.problem.userExtraFile) {
            this.userExtraFile = JSON.parse(result.problem.userExtraFile);
          }
          this.problemData = result;
          this.loading = false;
          // 如果用户已认证，则获取用户问题状态
          if (this.isAuthenticated) {
            let pidList = [result.problem.id];
            let isContestProblemList = this.contestID ? true : false;
            api
              .getUserProblemStatus(
                pidList,
                isContestProblemList,
                this.contestID,
                this.groupID,
                true,
              )
              .then((res) => {
                let statusMap = res.data.data;
                if (statusMap[result.problem.id].status != -10) {
                  this.submissionExists = true;
                  this.problemData.myStatus =
                    statusMap[result.problem.id].status;
                } else {
                  this.submissionExists = false;
                }
              });
          }
          this.isRemote = result.problem.isRemote;
          this.changePie(result.problemCount);
          // 如果本地有代码，则无需加载模板
          if (this.code !== '') {
            return;
          }
          // 设置编程语言和加载问题模板
          if (this.problemData.languages.length != 0) {
            if (
              !this.language ||
              this.problemData.languages.indexOf(this.language) == -1
            ) {
              this.language = this.problemData.languages[0];
            }
          }
          let codeTemplate = this.problemData.codeTemplate;
          if (codeTemplate && codeTemplate[this.language]) {
            this.code = codeTemplate[this.language];
          }
          this.$nextTick((_) => {
            addCodeBtn();
          });
        },
        (err) => {
          this.submitDisabled = true;
          this.loading = false;
        },
      );
      // 如果活动名称是mySubmission，则获取我的提交记录
      if (this.activeName == 'mySubmission') {
        this.getMySubmission();
      }
    },
    /**
     * 更新饼图数据
     * 根据问题数据更新饼图的系列数据和图例
     * @param {Object} problemData - 问题数据对象
     */
    changePie(problemData) {
      let total = problemData.total;
      let acNum = problemData.ac;
      // 过滤掉结果数为0的状态和无关参数
      for (let k in problemData) {
        if (problemData[k] == 0 || filtedStatus.indexOf(k) === -1) {
          delete problemData[k];
        }
      }
      let data = [
        { name: 'WA', value: total - acNum },
        { name: 'AC', value: acNum },
      ];
      this.pie.series[0].data = data;
      // 只把大图的AC selected下，这里需要做一下deepcopy
      let data2 = JSON.parse(JSON.stringify(data));
      data2[1].selected = true;
      this.largePie.series[1].data = data2;
      // 根据结果设置legend,没有提交过的legend不显示
      let legend = Object.keys(problemData).map((ele) =>
        (ele + '').toUpperCase(),
      );
      if (legend.length === 0) {
        legend.push('AC', 'WA');
      }
      this.largePie.legend.data = legend;
      // 把ac的数据提取出来放在最后
      let acCount = problemData.ac;
      delete problemData.ac;
      let largePieData = [];
      Object.keys(problemData).forEach((ele) => {
        largePieData.push({
          name: (ele + '').toUpperCase(),
          value: problemData[ele],
        });
      });
      largePieData.push({ name: 'AC', value: acCount });
      this.largePie.series[0].data = largePieData;
    },
    /**
     * 跳转到问题提交页面
     */
    goProblemSubmission() {
      if (this.contestID) {
        this.$router.push({
          name: 'ContestSubmissionList',
          params: { contestID: this.contestID },
          query: { problemID: this.problemID, completeProblemID: true },
        });
      } else if (this.groupID) {
        this.$router.push({
          name: 'GroupSubmissionList',
          query: {
            problemID: this.problemID,
            completeProblemID: true,
            gid: this.groupID,
          },
        });
      } else {
        this.$router.push({
          name: 'SubmissionList',
          query: {
            problemID: this.problemID,
            completeProblemID: true,
          },
        });
      }
    },
    /**
     * 跳转到问题讨论页面
     */
    goProblemDiscussion() {
      if (this.groupID) {
        this.$router.push({
          name: 'GroupProblemDiscussion',
          params: { problemID: this.problemID, groupID: this.groupID },
        });
      } else {
        this.$router.push({
          name: 'ProblemDiscussion',
          params: { problemID: this.problemID },
        });
      }
    },
    /**
     * 当选择编程语言时更新代码模板
     * @param {String} newLang - 新选择的编程语言
     */
    onChangeLang(newLang) {
      if (this.code == this.problemData.codeTemplate[this.language]) {
        //原语言模板未变化，只改变语言
        if (this.problemData.codeTemplate[newLang]) {
          this.code = this.problemData.codeTemplate[newLang];
        } else {
          this.code = '';
        }
      }
      this.language = newLang;
    },
    /**
     * 当选择主题时更新编辑器主题
     * @param {String} newTheme - 新选择的主题
     */
    onChangeTheme(newTheme) {
      this.theme = newTheme;
    },
    /**
     * 重置代码到模板
     */
    onResetToTemplate() {
      this.$confirm(
        this.$i18n.t('m.Are_you_sure_you_want_to_reset_your_code'),
        'Tips',
        {
          cancelButtonText: this.$i18n.t('m.Cancel'),
          confirmButtonText: this.$i18n.t('m.OK'),
          type: 'warning',
        },
      )
        .then(() => {
          let codeTemplate = this.problemData.codeTemplate;
          if (codeTemplate && codeTemplate[this.language]) {
            this.code = codeTemplate[this.language];
          } else {
            this.code = '';
          }
        })
        .catch(() => {
        });
    },
    /**
     * 获取用户最后一次通过的代码
     */
    getUserLastAccepetedCode() {
      if (this.problemData.myStatus != 0) {
        this.$notify.error({
          title: this.$i18n.t('m.Error'),
          message: this.$i18n.t(
            'm.You_havenot_passed_the_problem_so_you_cannot_get_the_code_passed_recently',
          ),
          duration: 4000,
          offset: 50,
        });
        return;
      }
      this.$confirm(
        this.$i18n.t(
          'm.Are_you_sure_you_want_to_get_your_recent_accepted_code',
        ),
        'Tips',
        {
          cancelButtonText: this.$i18n.t('m.Cancel'),
          confirmButtonText: this.$i18n.t('m.OK'),
          type: 'warning',
        },
      )
        .then(() => {
          api
            .getUserLastAccepetedCode(
              this.problemData.problem.id,
              this.contestID,
            )
            .then((res) => {
              this.code = res.data.data.code;
              let lang = res.data.data.language;
              if (lang && this.problemData.languages.includes(lang)) {
                this.language = lang;
              }
            });
        })
        .catch(() => {
        });
    },
    /**
     * 检查提交状态
     * 定期检查提交结果，直到提交完成
     */
    checkSubmissionStatus() {
      // 使用setTimeout避免一些问题
      if (this.refreshStatus) {
        // 如果之前的提交状态检查还没有停止,则停止,否则将会失去timeout的引用造成无限请求
        clearTimeout(this.refreshStatus);
      }
      const checkStatus = () => {
        let submitId = this.submissionId;
        api.getSubmission(submitId).then(
          (res) => {
            this.result.status = res.data.data.submission.status;
            if (Object.keys(res.data.data.submission).length !== 0) {
              // status不为判题和排队中才表示此次判题结束
              if (
                res.data.data.submission.status !=
                JUDGE_STATUS_RESERVE['Pending'] &&
                res.data.data.submission.status !=
                JUDGE_STATUS_RESERVE['Compiling'] &&
                res.data.data.submission.status !=
                JUDGE_STATUS_RESERVE['Judging']
              ) {
                this.submitting = false;
                this.submitted = false;
                clearTimeout(this.refreshStatus);
                this.init();
                if (this.showProblemHorizontalMenu) {
                  this.$refs.problemHorizontalMenu.getFullScreenProblemList();
                }
              } else {
                this.refreshStatus = setTimeout(checkStatus, 2000);
              }
            } else {
              this.refreshStatus = setTimeout(checkStatus, 2000);
            }
          },
          (res) => {
            this.submitting = false;
            clearTimeout(this.refreshStatus);
          },
        );
      };
      // 设置每2秒检查一下该题的提交结果
      this.refreshStatus = setTimeout(checkStatus, 2000);
    },

    /**
     * 检查比赛密码
     * 此函数验证用户输入的比赛密码是否为空，如果为空则提示用户输入，否则调用API进行注册
     * 无参数
     * 返回值: 无
     */
    checkContestPassword() {
      // 密码为空，需要重新输入
      if (!this.submitPwd) {
        myMessage.warning(this.$i18n.t('m.Enter_the_contest_password'));
        return;
      }
      // 注册比赛并验证密码
      api.registerContest(this.contestID + '', this.submitPwd).then(
        (res) => {
          // 密码验证成功，更新状态并隐藏密码输入框
          this.$store.commit('contestSubmitAccess', { submitAccess: true });
          this.submitPwdVisible = false;
          this.submitCode();
        },
        (res) => {
          // 密码验证失败，此处可根据需要添加处理逻辑
        },
      );
    },

    handleReplyOptions(replyOptions) {
      this.replyOptions = replyOptions;
    },

    /**
     * 提交代码函数
     * 此函数用于将用户编写的代码提交到服务器进行评测
     * 在提交之前，会进行一系列的校验，包括代码是否为空、代码长度是否超过限制等
     * 如果是比赛题目，还需要检查是否有权限提交
     * 提交成功后，会更新提交状态，并根据提交结果进行相应的提示
     */
    submitCode() {
      // 非编程题处理
      if (this.notProgrammerProblem) {
        console.log(this.replyOptions);
      }

      // 编程题处理
      // 检查代码是否为空
      if (!this.notProgrammerProblem && this.code.trim() === '') {
        myMessage.error(this.$i18n.t('m.Code_can_not_be_empty'));
        return;
      }

      // 检查代码长度是否超过65535
      if (this.code.length > 65535) {
        myMessage.error(this.$i18n.t('m.Code_Length_can_not_exceed_65535'));
        return;
      }

      // 比赛题目需要检查是否有权限提交
      if (!this.canSubmit && this.$route.params.contestID) {
        this.submitPwdVisible = true;
        return;
      }

      // 初始化提交状态
      this.submissionId = '';
      this.result = { status: 9 };
      this.submitting = true;

      // 准备提交的数据
      let data = {
        pid: this.problemID, // 如果是比赛题目就为display_id
        language: this.language,
        code: this.code,
        cid: this.contestID,
        tid: this.trainingID,
        gid: this.groupID,
        isRemote: this.isRemote,
        problemType: this.problemData.problem.problemType,
        replyOptions: this.replyOptions,
        flowImage: this.flowImage,
      };

      // 如果需要验证码，则添加验证码到提交数据中
      if (this.captchaRequired) {
        data.captcha = this.captchaCode;
      }

      // 提交代码的函数
      const submitFunc = (data, detailsVisible) => {
        this.statusVisible = true;
        api.submitCode(data).then(
          (res) => {
            this.submissionId = res.data.data && res.data.data.submitId;
            // 定时检查状态
            this.submitting = false;
            this.submissionExists = true;
            if (!detailsVisible) {
              this.$Modal.success({
                title: 'Success',
                content: this.$i18n.t('m.Submit_code_successfully'),
              });
              return;
            } else {
              myMessage.success(this.$i18n.t('m.Submit_code_successfully'));
            }
            // 更新store的可提交权限
            if (!this.canSubmit) {
              this.$store.commit('contestIntoAccess', { access: true });
            }
            this.submitted = true;
            this.checkSubmissionStatus();
          },
          (res) => {
            // this.getCaptchaSrc();
            // if (res.data.data.startsWith('Captcha is required')) {
            //   this.captchaRequired = true;
            // }
            this.submitting = false;
            this.statusVisible = false;
          },
        );
      };

      // 检查比赛规则类型和实时提交权限
      if (
        this.contestRuleType === RULE_TYPE.OI &&
        !this.ContestRealTimePermission
      ) {
        if (this.submissionExists) {
          this.$confirm(
            this.$i18n.t(
              'm.You_have_submission_in_this_problem_sure_to_cover_it',
            ),
            'Warning',
            {
              confirmButtonText: this.$i18n.t('m.OK'),
              cancelButtonText: this.$i18n.t('m.Cancel'),
              type: 'warning',
            },
          )
            .then(() => {
              // 暂时解决对话框与后面提示对话框冲突的问题(否则一闪而过）
              setTimeout(() => {
                submitFunc(data, false);
              }, 1000);
            })
            .catch(() => {
              this.submitting = false;
            });
        } else {
          submitFunc(data, false);
        }
      } else {
        submitFunc(data, true);
      }
    },

    /**
     * 重新提交指定的提交记录
     * @param {number} submitId - 需要重新提交的记录ID
     * 此函数将指定的提交记录重新提交给远程评测系统
     * 在提交过程中，会更新UI状态，提交成功后会显示成功消息并检查提交状态
     * 如果提交失败，则恢复UI状态
     */
    reSubmit(submitId) {
      this.result = { status: 9 };
      this.submitting = true;
      api.reSubmitRemoteJudge(submitId).then(
        (res) => {
          myMessage.success(this.$i18n.t('m.Resubmitted_Successfully'));
          this.submitted = true;
          this.checkSubmissionStatus();
        },
        (err) => {
          this.submitting = false;
          this.statusVisible = false;
        },
      );
    },

    /**
     * 显示额外文件的内容
     * @param {string} name - 文件名
     * @param {string} content - 文件内容
     * 此函数用于显示额外文件的内容，并在显示后添加代码按钮
     */
    showExtraFileContent(name, content) {
      this.fileName = name;
      this.fileContent = content;
      this.$nextTick((_) => {
        addCodeBtn();
      });
    },

    /**
     * 下载额外文件
     * 此函数根据文件名和内容生成并触发文件下载
     */
    downloadExtraFile() {
      utils.downloadFileByText(this.fileName, this.fileContent);
    },

    /**
     * 获取难度等级的颜色
     * @param {number} difficulty - 题目的难度等级
     * @returns {string} 难度等级对应的颜色
     * 此函数用于根据难度等级获取对应的颜色，以便在UI中显示
     */
    getLevelColor(difficulty) {
      return utils.getLevelColor(difficulty);
    },

    /**
     * 获取难度等级的名称
     * @param {number} difficulty - 题目的难度等级
     * @returns {string} 难度等级对应的名称
     * 此函数用于根据难度等级获取对应的名称，以便在UI中显示
     */
    getLevelName(difficulty) {
      return utils.getLevelName(difficulty);
    },

    /**
     * 跳转到用户主页
     * @param {string} username - 用户名
     * 此函数用于跳转到指定用户的主页
     */
    goUserHome(username) {
      this.$router.push({
        path: '/organization-home',
        query: { username },
      });
    },

    /**
     * 计算OI排名分数
     * @param {number} score - 用户得分
     * @param {number} difficulty - 题目难度
     * @returns {number} 计算得到的排名分数
     * 此函数用于根据用户得分和题目难度计算排名分数
     */
    calcOIRankScore(score, difficulty) {
      return Math.round(0.1 * score + 2 * difficulty);
    },

    /**
     * 当复制操作成功时的处理函数
     * @param {Event} event - 复制事件对象
     */
    onCopy(event) {
      myMessage.success(this.$i18n.t('m.Copied_successfully'));
    },

    /**
     * 当复制操作失败时的处理函数
     * @param {Event} e - 复制失败事件对象
     */
    onCopyError(e) {
      myMessage.success(this.$i18n.t('m.Copied_failed'));
    },

    /**
     * 开启或关闭测试用例抽屉的函数
     * 此函数通过切换 openTestCaseDrawer 属性的值来控制抽屉的显示与隐藏
     */
    openTestJudgeDrawer() {
      this.openTestCaseDrawer = !this.openTestCaseDrawer;
    },

    /**
     * 切换专注模式的函数
     * @param {boolean} isOpen - 表示是否开启专注模式
     * 此函数通过更改 openFocusMode 属性的值并更新路由来切换专注模式
     */
    switchFocusMode(isOpen) {
      this.openFocusMode = isOpen;
      this.$router.push({
        name: utils.getSwitchFoceusModeRouteName(this.$route.name),
        params: {
          trainingID: this.trainingID,
          contestID: this.contestID,
          problemID: this.problemID,
          groupID: this.groupID,
        },
      });
    },

    /**
     * 离开前执行的操作函数
     * @param {number} cid - 比赛ID
     * 此函数用于在离开页面前保存代码和编辑设置到本地存储，并清除定时器
     */
    beforeLeaveDo(cid) {
      clearInterval(this.refreshStatus);
      storage.set(
        buildProblemCodeAndSettingKey(this.problemID, cid),
        {
          code: this.code,
          language: this.language,
          theme: this.theme,
          fontSize: this.fontSize,
          tabSize: this.tabSize,
        },
      );

      storage.set(buildIndividualLanguageAndSettingKey(), {
        language: this.language,
        theme: this.theme,
        fontSize: this.fontSize,
        tabSize: this.tabSize,
      });
    },
  },

  computed: {
    notProgrammerProblem() {
      return this.problemData.problem.problemType !== ProblemType.Programmer.code;
    },
    ProblemType() {
      return ProblemType;
    },
    ...mapGetters([
      'problemSubmitDisabled',
      'contestRuleType',
      'ContestRealTimePermission',
      'contestStatus',
      'isAuthenticated',
      'canSubmit',
      'websiteConfig',
    ]),
    // 获取当前比赛信息
    contest() {
      return this.$store.state.contest.contest;
    },

    // 判断比赛是否结束
    contestEnded() {
      return this.contestStatus === CONTEST_STATUS.ENDED;
    },

    // 获取提交状态的文本和颜色
    submissionStatus() {
      return {
        text: JUDGE_STATUS[this.result.status]['name'],
        color: JUDGE_STATUS[this.result.status]['rgb'],
      };
    },

    // 根据不同情况跳转到提交详情页面
    submissionRoute() {
      if (this.contestID) {
        // 比赛提交详情
        this.$router.push({
          name: 'ContestSubmissionDetails',
          params: {
            contestID: this.contestID,
            problemID: this.problemID,
            submitID: this.submissionId,
          },
        });
      } else if (this.groupID) {
        // 小组提交详情
        this.$router.push({
          name: 'GroupSubmissionDetails',
          params: { submitID: this.submissionId, gid: this.groupID },
        });
      } else {
        // 公开提交详情
        this.$router.push({
          name: 'SubmissionDetails',
          params: { submitID: this.submissionId },
        });
      }
    },

    // 判断问题是否来自Codeforces
    isCFProblem() {
      if (
        this.problemID.indexOf('CF-') == 0 ||
        this.problemID.indexOf('GYM-') == 0
      ) {
        return true;
      } else {
        return false;
      }
    },

    // 判断是否显示问题讨论
    isShowProblemDiscussion() {
      if (!this.contestID) {
        if (this.groupID) {
          // 小组讨论是否开放
          if (this.websiteConfig.openGroupDiscussion) {
            return true;
          }
        } else {
          // 公开讨论是否开放
          if (this.websiteConfig.openPublicDiscussion) {
            return true;
          }
        }
      }
      return false;
    },

    // 获取问题类型
    problemType() {
      if (this.contestID) {
        return 'contest';
      } else if (this.groupID) {
        return 'group';
      } else {
        return 'public';
      }
    },
  },
  /**
   * 在路由离开前执行的钩子函数
   * 该函数用于在离开当前路由前做一些清理工作或状态保存
   * @param {Route} to 即将进入的路由
   * @param {Route} from 当前离开的路由
   * @param {Function} next 路由导航的下一个步骤，必须调用
   */
  beforeRouteLeave(to, from, next) {
    // 调用自定义的 beforeLeaveDo 方法，传入当前离开的竞赛ID
    this.beforeLeaveDo(from.params.contestID);
    // 如果当前路由名称为 ContestFullProblemDetails，清理竞赛相关数据
    if (this.$route.name === 'ContestFullProblemDetails') {
      this.$store.commit('clearContest');
    }
    // 调用 next 以继续路由导航
    next();
  },

  /**
   * 在路由更新前执行的钩子函数
   * 该函数用于在路由参数变化但组件被复用时，执行一些更新操作
   * @param {Route} to 即将进入的路由
   * @param {Route} from 当前离开的路由
   * @param {Function} next 路由导航的下一个步骤，必须调用
   */
  beforeRouteUpdate(to, from, next) {
    // 调用自定义的 beforeLeaveDo 方法，传入当前离开的竞赛ID
    this.beforeLeaveDo(from.params.contestID);
    // 调用 next 以继续路由导航
    next();
  },
  watch: {
    /**
     * 当路由发生变化时执行的函数
     * 重置组件状态，准备处理新的路由请求
     */
    $route() {
      this.initProblemCodeAndSetting(); // 初始化问题代码和设置
      this.submitted = false; // 重置提交状态
      this.submitDisabled = false; // 启用提交功能
      this.submitting = false; // 重置提交中状态
      this.statusVisible = false; // 隐藏状态显示
      this.init(); // 重新初始化组件
    },

    /**
     * 监听用户认证状态变化的函数
     * 当用户认证状态变为已认证时，重置组件状态
     * @param {boolean} newVal - 用户认证状态的新值
     */
    isAuthenticated(newVal) {
      if (newVal === true) {
        this.submitted = false; // 重置提交状态
        this.submitDisabled = false; // 启用提交功能
        this.submitting = false; // 重置提交中状态
        this.statusVisible = false; // 隐藏状态显示
        this.init(); // 重新初始化组件
      }
    },

    toWatchProblem() {

    },
    /**
     * 当活动标签发生变化时执行的函数
     * 调整视口高度以适应新的标签页
     */
    activeName() {
      this.resizeWatchHeight(); // 调整视口高度
    },
  },
};
</script>
<style>
.katex .katex-mathml {
  display: none;
}
</style>

<style scoped>
/* 新增样式 */
.btn-container {
  width: 80%;
}

.submit-btn {
  padding: 12px 32px; /* 增大按钮内间距 */
  font-size: 16px; /* 调整字体大小 */
  /* 若需要更大尺寸可增加以下属性 */
  /* min-width: 120px; */
  /* height: 40px; */
}

.problem-menu {
  float: left;
}

a {
  color: #3091f2 !important;
}

.problem-menu span {
  margin-left: 5px;
}

.el-link {
  font-size: 16px !important;
}

.author-name {
  font-size: 14px !important;
  color: #909399 !important;
}

.question-intr {
  margin-top: 30px;
  border-radius: 4px;
  border: 1px solid #ddd;
  border-left: 2px solid #3498db;
  background: #fafafa;
  padding: 10px;
  line-height: 1.8;
  margin-bottom: 10px;
  font-size: 14px;
}

.extra-file {
  margin: 10px;
  cursor: pointer;
}

.file-download {
  vertical-align: bottom;
  float: right;
  margin-right: 5px;
}

.submit-detail {
  height: 100%;
}

/deep/ .el-tabs--border-card > .el-tabs__content {
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 0px;
}

.js-left {
  padding-right: 15px;
}

@media screen and (min-width: 992px) {
  .problem-body {
    margin-left: -2%;
    margin-right: -2%;
  }

  .js-left {
    height: 730px !important;
    overflow-y: auto;
  }

  #js-extraFile {
    overflow-y: auto;
  }

  #js-submission {
    overflow-y: auto;
  }

  .submit-detail {
    overflow-y: auto;
  }

  .js-right {
    height: 635px !important;
  }

  #js-right-bottom {
    height: 49px;
  }

  .problem-tag {
    display: inline;
  }

  .problem-menu {
    float: right;
  }

  .problem-menu span {
    margin-left: 10px;
  }

  .question-intr {
    margin-top: 6px;
  }
}

@media screen and (min-width: 992px) {
  .problem-box {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .problem-left {
    width: 50%; /*左侧初始化宽度*/
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    float: left;
  }

  problem-center {
    width: 50%; /*左侧初始化宽度*/
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    align-content: center;
    justify-content: center;
    margin: auto;

  }

  .problem-resize {
    cursor: col-resize;
    position: absolute;
    top: 330px;
    left: 50%;
    background-color: #d6d6d6;
    border-radius: 5px;
    width: 10px;
    height: 50px;
    background-size: cover;
    background-position: center;
    font-size: 32px;
    color: white;
  }

  .problem-resize:hover .right-fold {
    display: block;
  }

  .problem-resize:hover .fold:before {
    content: "";
    position: absolute;
    display: block;
    width: 6px;
    height: 24px;
    left: -6px;
  }

  .right-fold {
    position: absolute;
    display: none;
    font-weight: bolder;
    margin-left: 15px;
    margin-top: -35px;
    cursor: pointer;
    z-index: 1000;
    text-align: center;
  }

  .left-fold {
    position: absolute;
    font-weight: bolder;
    margin-left: -40px;
    margin-top: 10px;
    cursor: pointer;
    z-index: 1000;
    text-align: center;
  }

  .fold:hover {
    color: #409eff;
    background: #fff;
  }

  /*拖拽区鼠标悬停样式*/
  .problem-resize:hover {
    color: #444444;
  }

  .problem-right {
    height: 100%;
    float: left;
    width: 50%;
  }
}

@media screen and (max-width: 992px) {
  .submit-detail {
    padding-top: 20px;
  }

  .submit-detail {
    height: 100%;
  }
}

/deep/ .el-card__header {
  border-bottom: 0px;
  padding-bottom: 0px;
}

/deep/ .el-card__body {
  padding-bottom: 5px !important;
}

#right-column {
  flex: none;
  width: 220px;
}

#problem-content {
  margin-top: -40px;
}

#problem-content .title {
  font-size: 16px;
  font-weight: 600;
  margin: 25px 0 8px 0;
  color: #3091f2;
}

#problem-content .copy {
  padding-left: 8px;
}

.hint-content {
  margin: 1em 0;
  font-size: 15px !important;
}

.md-content {
  margin: 1em;
  font-size: 15px;
}

.flex-container {
  display: flex;
  width: 100%;
  max-width: 100%;
  justify-content: space-around;
  align-items: flex-start;
  flex-flow: row nowrap;
}

.example {
  align-items: stretch;
}

.example-input,
.example-output {
  width: 50%;
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
}

.example pre {
  flex: 1 1 auto;
  align-self: stretch;
  border-style: solid;
  background: transparent;
  padding: 5px 10px;
  white-space: pre;
  margin-top: 10px;
  margin-bottom: 10px;
  background: #f1f1f1;
  border: 1px dashed #e9eaec;
  overflow: auto;
  font-size: 1.1em;
  margin-right: 7%;
}

#submit-code {
  height: auto;
}

#submit-code .status {
  float: left;
}

.submission-status:hover {
  cursor: pointer;
}

#submit-code .status span {
  margin-left: 10px;
}

.captcha-container {
  display: inline-block;
}

.captcha-container .captcha-code {
  width: auto;
  margin-top: -20px;
  margin-left: 20px;
}

.fl-right {
  float: right;
}

/deep/ .el-dialog__body {
  padding: 10px 10px !important;
}

#pieChart .echarts {
  height: 250px;
  width: 210px;
}

#pieChart #detail {
  position: absolute;
  right: 10px;
  top: 10px;
}

/deep/ .echarts {
  width: 350px;
  height: 350px;
}

#pieChart-detail {
  /* margin-top: 20px; */
  height: 350px;
}

.tj-btn {
  margin-right: 15px;
  float: right;
  cursor: pointer;
}

.tj-btn.non-active {
  border: 1px solid #32ca99;
}

.tj-btn.non-active:hover {
  background-color: #d5f1eb;
}

.tj-btn.active {
  background-color: #67c23a;
  border-color: #67c23a;
  color: #fff;
}
</style>
