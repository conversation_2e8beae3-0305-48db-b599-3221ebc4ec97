<template>
  <div class="score-report-container">
    <el-table
      v-loading="loading"
      :data="processedData"
      border
      header-row-class-name="custom-header"
      stripe
      style="width: 100%"
    >
      <!-- 固定列 -->
      <el-table-column fixed label="姓名" align="center" prop="realname" width="120"></el-table-column>
      <el-table-column fixed label="总分" align="center" width="100">
        <template slot-scope="{row}">
          <span class="total-score">{{ row.totalScore }}</span>
        </template>
      </el-table-column>

      <!-- 动态题目列 -->
      <el-table-column
        v-for="problem in problems"
        :key="problem.id"
        :label="`${problem.problemId}`"
        align="center"
        width="135"
      >
        <template slot-scope="{row}">
          <div class="problem-cell">
            <div v-if="row.problems[problem.id]">
              <div>
                <status-tag :status="row.problems[problem.id].status" />
                <p>得分: {{ row.problems[problem.id].score }}</p>
                <p>时间: {{ row.problems[problem.id].time }}ms</p>
                <p>内存: {{ row.problems[problem.id].memory }}KB</p>
              </div>
            </div>
            <span v-else class="no-data">-</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import StatusTag from '@/components/tuc/StatusTag.vue';
import Avatar from 'vue-avatar';
import { mapActions, mapGetters } from 'vuex';
import { JUDGE_STATUS } from '@/common/constants';

const Pagination = () => import('@/components/oj/common/Pagination');
import api from '@/common/api';
import { mapState } from 'vuex';
import time from '@/common/time';


export default {
  name: 'ReportCard',
  components: {
    StatusTag,
    Pagination,
  },
  data() {
    return {

      reportData: [],
      data: [],
      total: 0,
      page: 1,
      limit: 30,
      keyword: '',
      trainingID: '',
      dataRank: [],
      JUDGE_STATUS: {},
      groupID: null,
      loading: false,
    };
  },
  mounted() {
    this.JUDGE_STATUS = Object.assign({}, JUDGE_STATUS);
    if (this.$route.params.groupID) {
      this.groupID = this.$route.params.groupID;
    }

    this.trainingID = this.$route.params.trainingID;
    this.init();
  },
  methods: {
    ...mapActions(['getTrainingProblemList']),
    getStatusClass(status) {
      return {
        'status-correct': status === 2,
        'status-wrong': status >= 3 && status <= 5,
        'status-judging': status <= 1,
      };
    },

    async init() {
      this.loading = true;
      const res = await api.getReportCard({ tid: this.trainingID });
      console.log(res);
      this.data = res.data.data;
      this.reportData = res.data.data;
      this.total = res.data.data.total;
      this.loading = false;
    },
  },
  computed: {
    problems() {
      return this.reportData.problems || [];
    },
    processedData() {
      return (this.reportData.traningInfos || []).map(user => {
        const problemMap = {};
        // 转换judgeList为以pid为key的map
        if (user.judgeList) {
          user.judgeList.forEach(judge => {
            problemMap[judge.pid] = {
              status: judge.status,
              score: judge.score,
              time: judge.time,
              memory: judge.memory,
            };
          });
        }

        return {
          uid: user.uid,
          realname: user.realname,
          totalScore: user.totalScore || 0,
          problems: problemMap,
        };
      });
    },
  },
};
</script>

<style lang="scss">
.score-report-container {
  padding: 20px;

  .custom-header {
    th {
      background-color: #f8f9fa !important;
      font-weight: 600;
      color: #2c3e50;
    }
  }

  .total-score {
    font-weight: bold;
    color: #409EFF;
  }

  .problem-cell {
    text-align: center;

    .status-tag {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &.success {
        background-color: #f0f9eb;
        color: #67c23a;
      }

      &.warning {
        background-color: #fdf6ec;
        color: #e6a23c;
      }

      &.error {
        background-color: #fef0f0;
        color: #f56c6c;
      }
    }

    .no-data {
      color: #c0c4cc;
    }
  }

  .judge-tooltip {
    padding: 12px !important;
    line-height: 1.6;

    p {
      margin: 4px 0;
      font-size: 13px;
    }
  }
}
</style>
