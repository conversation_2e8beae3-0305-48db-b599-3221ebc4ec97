{"name": "hoj-vue", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@iktakahiro/markdown-it-katex": "^4.0.1", "@logicflow/core": "^1.2.3", "@logicflow/extension": "^1.2.3", "axios": "^0.21.0", "browser-detect": "^0.2.28", "compression-webpack-plugin": "^5.0.1", "core-js": "^3.6.5", "echarts": "^4.9.0", "element-china-area-data": "^6.1.0", "element-ui": "^2.15.14", "eslint-plugin-vue": "^9.32.0", "font-awesome": "^4.7.0", "highlight.js": "^10.3.2", "jquery": "^3.5.1", "mavon-editor": "2.9.1", "moment": "^2.29.1", "muse-ui": "^3.0.2", "nprogress": "^0.2.0", "papaparse": "^5.3.0", "vue": "^2.6.11", "vue-avatar": "^2.3.3", "vue-calendar-heatmap": "^0.8.4", "vue-clipboard2": "^0.3.1", "vue-codemirror-lite": "^1.0.4", "vue-color": "^2.8.1", "vue-cropper": "^0.5.5", "vue-dompurify-html": "^2.5.0", "vue-echarts": "^5.0.0-beta.0", "vue-i18n": "^8.24.4", "vue-katex-auto-render": "^0.1.3", "vue-m-message": "^3.1.0", "vue-monoplasty-slide-verify": "^1.3.1", "vue-particles": "^1.0.9", "vue-router": "^3.2.0", "vue-template-compiler": "^2.6.12", "vuex": "^3.4.0", "vuex-router-sync": "^5.0.0", "vxe-table": "^2.9.26", "xe-utils": "^2.8.1"}, "devDependencies": {"@types/highlight.js": "^10.1.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "prettier": "3.5.2", "sass": "^1.32.13", "sass-loader": "^10.1.1", "uglifyjs-webpack-plugin": "^2.2.0", "webpack-bundle-analyzer": "^4.4.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}