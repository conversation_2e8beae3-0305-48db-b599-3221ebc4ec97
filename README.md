# hoj-vue-pc

## Development Environment Requirements

We recommend using Node.js version 14.21.3.

If you wish to not disrupt your existing development environment, you can download precompiled binaries of Node.js and temporarily modify your environment variables to use this version.

[Download Precompiled Binaries](https://nodejs.org/en/download/prebuilt-binaries)

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).
