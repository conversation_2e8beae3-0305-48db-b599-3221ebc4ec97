# 使用官方 Node.js 作为基础镜像
FROM node:16-alpine


# 在容器中创建目录
WORKDIR /app

# 将package.json和package-lock.json复制到工作目录
COPY package*.json ./

# 使用pnpm 安装项目依赖
RUN npm install

# 将项目源代码复制到工作目录
COPY . .

# 构建项目
RUN npm run build

# 使用nginx作为服务
FROM nginx:stable-alpine as production-stage

# 将构建好的项目复制到nginx下
COPY --from=0 /app/dist /usr/share/nginx/html

# 将SSL证书和私钥复制到容器中的预定位置
RUN mkdir -p /etc/nginx/ssl/oj.aicx.cc/
COPY ssl/oj.aicx.cc.pem /etc/nginx/ssl/oj.aicx.cc/oj.aicx.cc.pem
COPY ssl/oj.aicx.cc.key /etc/nginx/ssl/oj.aicx.cc/oj.aicx.cc.key


# 将默认的nginx配置文件替换为我们自定义的nginx配置文件
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80
EXPOSE 443

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
