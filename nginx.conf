# HTTP server configuration
server {
    listen 80;
    server_name oj.aicx.cc;

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass https://***********:8212;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /run {
        proxy_pass https://***********:8211;

          # 添加 CORS 头信息
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT, X-CustomHeader, Keep-Alive, User-Agent, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # 使 OPTIONS 请求快速返回
            if ($request_method = 'OPTIONS') {
                return 204;
            }

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# HTTPS server configuration
server {
    listen 443 ssl;
    server_name oj.aicx.cc;

    ssl_certificate /etc/nginx/ssl/oj.aicx.cc/oj.aicx.cc.pem; # 修改为你的证书路径
    ssl_certificate_key /etc/nginx/ssl/oj.aicx.cc/oj.aicx.cc.key;     # 修改为你的密钥路径
    ssl_protocols TLSv1.2 TLSv1.3;                      # 推荐安全协议

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass https://oj.aicx.cc:8212;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /run {
        proxy_pass https://oj.aicx.cc:8211;

          # 添加 CORS 头信息
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT, X-CustomHeader, Keep-Alive, User-Agent, X-Requested-With, If-Modified-Since, Cache-Control, Content-Type' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # 使 OPTIONS 请求快速返回
            if ($request_method = 'OPTIONS') {
                return 204;
            }

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
