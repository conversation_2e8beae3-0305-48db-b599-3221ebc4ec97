const UglifyJsPlugin = require('uglifyjs-webpack-plugin'); // 用于清除注释

const isProduction = process.env.NODE_ENV === 'production';
const enabledPlugins = false;

// 使用 CSS JS 加载资源的配置
const externals = {
    vue: 'Vue',
    'vue-router': 'VueRouter',
    axios: 'axios',
    vuex: 'Vuex',
    'element-ui': 'ELEMENT',
    'highlight.js': 'hljs',
    moment: 'moment',
    'vue-echarts': 'VueECharts',
    echarts: 'echarts',
    katex: 'katex',
    'muse-ui': 'MuseUI',
    jquery: '$',
    // 'vxe-table': 'VXETable',
    // 'mavon-editor': 'mavonEditor',
};

module.exports = {
    publicPath: '/',
    assetsDir: 'assets',
    productionSourceMap: false, // 关闭生产环境的 source map

    devServer: {
        open: true,  // 启动后自动打开浏览器
        host: '0.0.0.0',  // 允许使用任意主机名访问
        port: 8066, // 开发服务器端口号
        proxy: {
            '/api': {
                target: 'http://**************:8202', // API 代理目标
                changeOrigin: true,
            },
            '/run': {
                target: 'https://***********:8211', // Tuc判断题机 服务代理目标
                changeOrigin: true,
            },
        },
        disableHostCheck: true,
    },

    chainWebpack: config => {
        // 分析打包文件体积大小，当 ANALYZE 环境变量为 true 时启用，使用命令为 : ANALYZE=true npm run build
        if (process.env.ANALYZE === 'true') {
            config.plugin('webpack-bundle-analyzer')
                .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin);
        }
    },

    configureWebpack: config => {
        config.externals = externals; // 忽略通过 CDN 引入的外部依赖
        config.mode = 'production'; // 确保使用生产模式
        config.performance = {
            maxEntrypointSize: 10000000, // 入口文件最大限制
            maxAssetSize: 30000000, // 单个资源最大限制
        };

        // 生产环境 或者 手动开启插件
        if (isProduction || enabledPlugins) {
            config.plugins.push(
                new UglifyJsPlugin({
                    uglifyOptions: {
                        output: {
                            comments: false, // 移除注释
                        },
                        warnings: false, // 不显示警告
                        compress: {
                            drop_console: true, // 不移除 console 语句 (设置为 true 可以移除全部 console)
                            drop_debugger: false, // 不移除 debugger 语句
                            // pure_funcs: ['console.log'] // 移除特定函数调用，启用时需解注释
                        },
                    },
                })
            );
        }
    },
};
