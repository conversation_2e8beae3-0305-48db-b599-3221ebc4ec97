<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta content="IE=edge" http-equiv="X-UA-Compatible">
    <meta content="width=device-width,initial-scale=1.0" name="viewport">
    <link href="<%= BASE_URL %>favicon.ico" rel="icon">

    <!-- 本地CSS文件 -->
    <link href="/css/index.min.css" rel="stylesheet">
    <link href="/css/github-markdown.min.css" rel="stylesheet">
    <link href="/css/katex.min.css" rel="stylesheet">
    <link href="/css/muse-ui.min.css" rel="stylesheet">
    <!-- <link href="/css/vxe-table.min.css" rel="stylesheet"> -->

    <title>TuDao OnlineJudge</title>
    <script>
        // IE10及以下版本的浏览器提示
        if (window.navigator.userAgent.indexOf('MSIE ') > 0 &&
            window.confirm('Your browser is not supported, click \'OK\' to update')) {
            window.location = 'http://outdatedbrowser.com'
        }
    </script>
    <!-- 预先加载动画 -->
    <style>
        @-webkit-keyframes enter {
            0% {
                opacity: 0;
                top: -10px;
            }
            5% {
                opacity: 1;
                top: 0px;
            }
            50.9% {
                opacity: 1;
                top: 0px;
            }
            55.9% {
                opacity: 0;
                top: 10px;
            }
        }

        @keyframes enter {
            0% {
                opacity: 0;
                top: -10px;
            }
            5% {
                opacity: 1;
                top: 0px;
            }
            50.9% {
                opacity: 1;
                top: 0px;
            }
            55.9% {
                opacity: 0;
                top: 10px;
            }
        }

        @-moz-keyframes enter {
            0% {
                opacity: 0;
                top: -10px;
            }
            5% {
                opacity: 1;
                top: 0px;
            }
            50.9% {
                opacity: 1;
                top: 0px;
            }
            55.9% {
                opacity: 0;
                top: 10px;
            }
        }

        body {
            background: #f8f8f9;
        }

        #app-loader {
            position: absolute;
            left: 50%;
            top: 50%;
            margin-left: -27.5px;
            margin-top: -27.5px;
        }

        #app-loader .square {
            background: #2d8cf0;
            width: 15px;
            height: 15px;
            float: left;
            top: -10px;
            margin-right: 5px;
            margin-top: 5px;
            position: relative;
            opacity: 0;
            -webkit-animation: enter 6s infinite;
            animation: enter 6s infinite;
        }

        #app-loader .enter {
            top: 0px;
            opacity: 1;
        }

        #app-loader .square:nth-child(1) {
            -webkit-animation-delay: 1.8s;
            -moz-animation-delay: 1.8s;
            animation-delay: 1.8s;
        }

        #app-loader .square:nth-child(2) {
            -webkit-animation-delay: 2.1s;
            -moz-animation-delay: 2.1s;
            animation-delay: 2.1s;
        }

        #app-loader .square:nth-child(3) {
            -webkit-animation-delay: 2.4s;
            -moz-animation-delay: 2.4s;
            animation-delay: 2.4s;
            background: #ff9900;
        }

        #app-loader .square:nth-child(4) {
            -webkit-animation-delay: 0.9s;
            -moz-animation-delay: 0.9s;
            animation-delay: 0.9s;
        }

        #app-loader .square:nth-child(5) {
            -webkit-animation-delay: 1.2s;
            -moz-animation-delay: 1.2s;
            animation-delay: 1.2s;
        }

        #app-loader .square:nth-child(6) {
            -webkit-animation-delay: 1.5s;
            -moz-animation-delay: 1.5s;
            animation-delay: 1.5s;
        }

        #app-loader .square:nth-child(8) {
            -webkit-animation-delay: 0.3s;
            -moz-animation-delay: 0.3s;
            animation-delay: 0.3s;
        }

        #app-loader .square:nth-child(9) {
            -webkit-animation-delay: 0.6s;
            -moz-animation-delay: 0.6s;
            animation-delay: 0.6s;
        }

        #app-loader .clear {
            clear: both;
        }

        #app-loader .last {
            margin-right: 0;
        }

        #app-loader .loader-content {
            color: #3498db;
            font-size: 16px;
            font-weight: 600;
        }
    </style>
</head>

<body>
<noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
</noscript>
<div id="app"></div>
<!-- built files will be auto injected -->
<div id="app-loader">
    <div class="square"></div>
    <div class="square"></div>
    <div class="square last"></div>
    <div class="square clear"></div>
    <div class="square"></div>
    <div class="square last"></div>
    <div class="square clear"></div>
    <div class="square "></div>
    <div class="square last"></div>
    <div class="loader-content">
        <span>Loading...</span>
    </div>
</div>

<!-- 本地JS文件 -->
<script src="/js/vue.min.js"></script>
<script src="/js/vue-router.min.js"></script>
<script src="/js/axios.min.js"></script>
<script src="/js/index.min.js"></script>
<script src="/js/highlight.min.js"></script>
<script src="/js/moment.min.js"></script>
<script src="/js/zh-cn.min.js"></script>
<script src="/js/en-gb.min.js"></script>
<script src="/js/echarts.min.js"></script>
<script src="/js/vue-echarts.min.js"></script>
<script src="/js/vuex.min.js"></script>
<script src="/js/katex.min.js"></script>
<script src="/js/auto-render.min.js"></script>
<script src="/js/muse-ui.min.js"></script>
<script src="/js/jquery.min.js"></script>
<!-- <script src="/js/xe-utils.umd.min.js"></script> -->
<!-- <script src="/js/vxe-table.umd.min.js"></script> -->
</body>

</html>
