!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("echarts/lib/echarts")):"function"==typeof define&&define.amd?define(["echarts/lib/echarts"],t):(e=e||self).VueECharts=t(e.echarts)}(this,function(r){"use strict";r=r&&r.hasOwnProperty("default")?r.default:r;function v(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function z(){return s.Date.now()}function o(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":_&&_ in Object(e)?function(e){var t=d.call(e,l),i=e[l];try{var n=!(e[l]=void 0)}catch(e){}var r=h.call(e);return n&&(t?e[l]=i:delete e[l]),r}(e):(t=e,u.call(t));var t}function b(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||null!=(i=t)&&"object"==typeof i&&"[object Symbol]"==o(t))return NaN;var t,i,n;if(v(e)&&(n="function"==typeof e.valueOf?e.valueOf():e,e=v(n)?n+"":n),"string"!=typeof e)return 0===e?e:+e;e=e.replace(f,"");var r=g.test(e);return r||m.test(e)?w(e.slice(2),r?2:8):p.test(e)?NaN:+e}function t(n,r,e){var o,s,i,a,c,d,h=0,l=!1,u=!1,t=!0;if("function"!=typeof n)throw new TypeError("Expected a function");function _(e){var t=o,i=s;return o=s=void 0,h=e,a=n.apply(i,t)}function f(e){var t=e-d;return void 0===d||r<=t||t<0||u&&i<=e-h}function p(){var e,t=z();if(f(t))return g(t);c=setTimeout(p,(e=r-(t-d),u?O(e,i-(t-h)):e))}function g(e){return c=void 0,t&&o?_(e):(o=s=void 0,a)}function m(){var e,t=z(),i=f(t);if(o=arguments,s=this,d=t,i){if(void 0===c)return h=e=d,c=setTimeout(p,r),l?_(e):a;if(u)return clearTimeout(c),c=setTimeout(p,r),_(d)}return void 0===c&&(c=setTimeout(p,r)),a}return r=b(r)||0,v(e)&&(l=!!e.leading,i=(u="maxWait"in e)?y(b(e.maxWait)||0,r):i,t="trailing"in e?!!e.trailing:t),m.cancel=function(){void 0!==c&&clearTimeout(c),o=d=s=c=void(h=0)},m.flush=function(){return void 0===c?a:g(z())},m}var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},i="object"==typeof e&&e&&e.Object===Object&&e,n="object"==typeof self&&self&&self.Object===Object&&self,s=i||n||Function("return this")(),a=s.Symbol,c=Object.prototype,d=c.hasOwnProperty,h=c.toString,l=a?a.toStringTag:void 0,u=Object.prototype.toString,_=a?a.toStringTag:void 0,f=/^\s+|\s+$/g,p=/^[-+]0x[0-9a-f]+$/i,g=/^0b[01]+$/i,m=/^0o[0-7]+$/i,w=parseInt,y=Math.max,O=Math.min,x=null,C=null;function E(e,t){void 0===t&&(t={});var i=document.createElement(e);return Object.keys(t).forEach(function(e){i[e]=t[e]}),i}function M(e,t,i){return(window.getComputedStyle(e,i||null)||{display:"none"})[t]}function T(e){if(!document.documentElement.contains(e))return{detached:!0,rendered:!1};for(var t=e;t!==document;){if("none"===M(t,"display"))return{detached:!1,rendered:!1};t=t.parentNode}return{detached:!1,rendered:!0}}var j=0,L=null;function S(e,t){var i,n,r,o,s,a,c,d,h;e.__resize_mutation_handler__||(e.__resize_mutation_handler__=function(){var e=T(this),t=e.rendered,i=e.detached;t!==this.__resize_rendered__&&(!i&&this.__resize_triggers__&&(N(this),this.addEventListener("scroll",A,!0)),this.__resize_rendered__=t,$(this))}.bind(e)),e.__resize_listeners__||(e.__resize_listeners__=[],window.ResizeObserver?(i=e.offsetWidth,n=e.offsetHeight,r=new ResizeObserver(function(){!e.__resize_observer_triggered__&&(e.__resize_observer_triggered__=!0,e.offsetWidth===i&&e.offsetHeight===n)||$(e)}),s=(o=T(e)).detached,a=o.rendered,e.__resize_observer_triggered__=!1===s&&!1===a,(e.__resize_observer__=r).observe(e)):e.attachEvent&&e.addEventListener?(e.__resize_legacy_resize_handler__=function(){$(e)},e.attachEvent("onresize",e.__resize_legacy_resize_handler__),document.addEventListener("DOMSubtreeModified",e.__resize_mutation_handler__)):(j||(d='.resize-triggers{visibility:hidden;opacity:0}.resize-contract-trigger,.resize-contract-trigger:before,.resize-expand-trigger,.resize-triggers{content:"";position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden}.resize-contract-trigger,.resize-expand-trigger{background:#eee;overflow:auto}.resize-contract-trigger:before{width:200%;height:200%}',(h=document.createElement("style")).type="text/css",h.styleSheet?h.styleSheet.cssText=d:h.appendChild(document.createTextNode(d)),(document.querySelector("head")||document.body).appendChild(h),L=h),function(e){var t=M(e,"position");t&&"static"!==t||(e.style.position="relative"),e.__resize_old_position__=t,e.__resize_last__={};var i=E("div",{className:"resize-triggers"}),n=E("div",{className:"resize-expand-trigger"}),r=E("div"),o=E("div",{className:"resize-contract-trigger"});n.appendChild(r),i.appendChild(n),i.appendChild(o),e.appendChild(i),e.__resize_triggers__={triggers:i,expand:n,expandChild:r,contract:o},N(e),e.addEventListener("scroll",A,!0),e.__resize_last__={width:e.offsetWidth,height:e.offsetHeight}}(e),e.__resize_rendered__=T(e).rendered,window.MutationObserver&&((c=new MutationObserver(e.__resize_mutation_handler__)).observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0}),e.__resize_mutation_observer__=c))),e.__resize_listeners__.push(t),j++}function A(){var e,t,s=this;N(this),this.__resize_raf__&&(e=this.__resize_raf__,(C=C||(window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||function(e){clearTimeout(e)}).bind(window))(e)),this.__resize_raf__=(t=function(){var e,t,i,n,r,o=(t=(e=s.__resize_last__).width,i=e.height,n=s.offsetWidth,r=s.offsetHeight,n!==t||r!==i?{width:n,height:r}:null);o&&(s.__resize_last__=o,$(s))},(x=x||(window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){return setTimeout(e,16)}).bind(window))(t))}function $(t){t&&t.__resize_listeners__&&t.__resize_listeners__.forEach(function(e){e.call(t)})}function N(e){var t=e.__resize_triggers__,i=t.expand,n=t.expandChild,r=t.contract,o=r.scrollWidth,s=r.scrollHeight,a=i.offsetWidth,c=i.offsetHeight,d=i.scrollWidth,h=i.scrollHeight;r.scrollLeft=o,r.scrollTop=s,n.style.width=a+1+"px",n.style.height=c+1+"px",i.scrollLeft=d,i.scrollTop=h}var R,W=["theme","initOptions","autoresize"],D=["manualUpdate","watchShallow"],F={props:{options:Object,theme:[String,Object],initOptions:Object,group:String,autoresize:Boolean,watchShallow:Boolean,manualUpdate:Boolean},data:function(){return{lastArea:0}},watch:{group:function(e){this.chart.group=e}},methods:{mergeOptions:function(e,t,i){this.manualUpdate&&(this.manualOptions=e),this.chart?this.delegateMethod("setOption",e,t,i):this.init(e)},appendData:function(e){this.delegateMethod("appendData",e)},resize:function(e){this.delegateMethod("resize",e)},dispatchAction:function(e){this.delegateMethod("dispatchAction",e)},convertToPixel:function(e,t){return this.delegateMethod("convertToPixel",e,t)},convertFromPixel:function(e,t){return this.delegateMethod("convertFromPixel",e,t)},containPixel:function(e,t){return this.delegateMethod("containPixel",e,t)},showLoading:function(e,t){this.delegateMethod("showLoading",e,t)},hideLoading:function(){this.delegateMethod("hideLoading")},getDataURL:function(e){return this.delegateMethod("getDataURL",e)},getConnectedDataURL:function(e){return this.delegateMethod("getConnectedDataURL",e)},clear:function(){this.delegateMethod("clear")},dispose:function(){this.delegateMethod("dispose")},delegateMethod:function(e){for(var t,i=[],n=arguments.length-1;0<n--;)i[n]=arguments[n+1];return this.chart||this.init(),(t=this.chart)[e].apply(t,i)},delegateGet:function(e){return this.chart||this.init(),this.chart[e]()},getArea:function(){return this.$el.offsetWidth*this.$el.offsetHeight},init:function(e){var i,n=this;this.chart||(i=r.init(this.$el,this.theme,this.initOptions),this.group&&(i.group=this.group),i.setOption(e||this.manualOptions||this.options||{},!0),Object.keys(this.$listeners).forEach(function(e){var t=n.$listeners[e];0===e.indexOf("zr:")?i.getZr().on(e.slice(3),t):i.on(e,t)}),this.autoresize&&(this.lastArea=this.getArea(),this.__resizeHandler=t(function(){0===n.lastArea?(n.mergeOptions({},!0),n.resize(),n.mergeOptions(n.options||n.manualOptions||{},!0)):n.resize(),n.lastArea=n.getArea()},100,{leading:!0}),S(this.$el,this.__resizeHandler)),Object.defineProperties(this,{width:{configurable:!0,get:function(){return n.delegateGet("getWidth")}},height:{configurable:!0,get:function(){return n.delegateGet("getHeight")}},isDisposed:{configurable:!0,get:function(){return!!n.delegateGet("isDisposed")}},computedOptions:{configurable:!0,get:function(){return n.delegateGet("getOption")}}}),this.chart=i)},initOptionsWatcher:function(){var i=this;this.__unwatchOptions&&(this.__unwatchOptions(),this.__unwatchOptions=null),this.manualUpdate||(this.__unwatchOptions=this.$watch("options",function(e,t){!i.chart&&e?i.init():i.chart.setOption(e,e!==t)},{deep:!this.watchShallow}))},destroy:function(){this.autoresize&&function(e,t){if(e.detachEvent&&e.removeEventListener)return e.detachEvent("onresize",e.__resize_legacy_resize_handler__),document.removeEventListener("DOMSubtreeModified",e.__resize_mutation_handler__);var i=e.__resize_listeners__;i&&(i.splice(i.indexOf(t),1),i.length||(e.__resize_observer__?(e.__resize_observer__.unobserve(e),e.__resize_observer__.disconnect(),e.__resize_observer__=null):(e.__resize_mutation_observer__&&(e.__resize_mutation_observer__.disconnect(),e.__resize_mutation_observer__=null),e.removeEventListener("scroll",A),e.removeChild(e.__resize_triggers__.triggers),e.__resize_triggers__=null),e.__resize_listeners__=null),!--j&&L&&L.parentNode.removeChild(L))}(this.$el,this.__resizeHandler),this.dispose(),this.chart=null},refresh:function(){this.chart&&(this.destroy(),this.init())}},created:function(){var t=this;this.initOptionsWatcher(),W.forEach(function(e){t.$watch(e,function(){t.refresh()},{deep:!0})}),D.forEach(function(e){t.$watch(e,function(){t.initOptionsWatcher(),t.refresh()})})},mounted:function(){this.options&&this.init()},activated:function(){this.autoresize&&this.chart&&this.chart.resize()},destroyed:function(){this.chart&&this.destroy()},connect:function(e){"string"!=typeof e&&(e=e.map(function(e){return e.chart})),r.connect(e)},disconnect:function(e){r.disConnect(e)},registerMap:function(e,t,i){r.registerMap(e,t,i)},registerTheme:function(e,t){r.registerTheme(e,t)},graphic:r.graphic},H="undefined"!=typeof navigator&&/msie [6-9]\\b/.test(navigator.userAgent.toLowerCase()),U={};return function(e,t,i,n,r){"boolean"!=typeof n&&(r=n,n=!1);var o,s,a,c="function"==typeof i?i.options:i;return e.render&&(c.render=e.render,c.staticRenderFns=e.staticRenderFns,c._compiled=!0),(o=n?function(e){t.call(this,(void 0)(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,r(e))})&&(c.functional?(s=c.render,c.render=function(e,t){return o.call(t),s(e,t)}):(a=c.beforeCreate,c.beforeCreate=a?[].concat(a,o):[o])),i}({render:function(){var e=this.$createElement;return(this._self._c||e)("div",{staticClass:"echarts"})},staticRenderFns:[]},function(e){e&&e("data-v-1e347cc8_0",{source:".echarts{width:600px;height:400px}",map:void 0,media:void 0})},F,!1,function(e){return function(e,t){return i=e,n=t,c=H?n.media||"default":i,void((d=U[c]||(U[c]={ids:new Set,styles:[]})).ids.has(i)||(d.ids.add(i),r=n.source,n.map&&(r+="\n/*# sourceURL="+n.map.sources[0]+" */",r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(n.map))))+" */"),d.element||(d.element=document.createElement("style"),d.element.type="text/css",n.media&&d.element.setAttribute("media",n.media),void 0===R&&(R=document.head||document.getElementsByTagName("head")[0]),R.appendChild(d.element)),"styleSheet"in d.element?(d.styles.push(r),d.element.styleSheet.cssText=d.styles.filter(Boolean).join("\n")):(o=d.ids.size-1,s=document.createTextNode(r),(a=d.element.childNodes)[o]&&d.element.removeChild(a[o]),a.length?d.element.insertBefore(s,a[o]):d.element.appendChild(s))));var i,n,r,o,s,a,c,d}})});